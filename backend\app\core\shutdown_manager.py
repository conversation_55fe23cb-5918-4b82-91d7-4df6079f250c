"""
Graceful Shutdown Manager for AI Crypto Trading System
Author: inkby<PERSON><PERSON>

Handles graceful shutdown with:
- Open position management
- Data persistence
- Service cleanup
- Connection termination
- Emergency procedures
"""

import asyncio
import logging
import signal
import sys
from datetime import datetime
from typing import List, Dict, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import json
from pathlib import Path

from app.core.config import settings
from app.core.logging_config import trading_context


class ShutdownReason(Enum):
    """Reasons for system shutdown"""
    USER_REQUEST = "user_request"
    SYSTEM_ERROR = "system_error"
    EMERGENCY_STOP = "emergency_stop"
    MAINTENANCE = "maintenance"
    SIGNAL_RECEIVED = "signal_received"


class ShutdownPhase(Enum):
    """Phases of shutdown process"""
    INITIATED = "initiated"
    STOPPING_NEW_TRADES = "stopping_new_trades"
    CLOSING_POSITIONS = "closing_positions"
    SAVING_DATA = "saving_data"
    CLEANUP_SERVICES = "cleanup_services"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class ShutdownState:
    """Current shutdown state"""
    reason: ShutdownReason
    phase: ShutdownPhase
    started_at: datetime
    progress_percentage: float = 0.0
    current_operation: str = ""
    errors: List[str] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []


class GracefulShutdownManager:
    """Manages graceful system shutdown"""
    
    def __init__(self):
        self.shutdown_handlers: List[Callable] = []
        self.is_shutting_down = False
        self.shutdown_state: Optional[ShutdownState] = None
        self.emergency_timeout = 30  # 30 seconds for emergency shutdown
        self.normal_timeout = 300    # 5 minutes for normal shutdown
        self.logger = logging.getLogger("shutdown")
        
        # Register signal handlers
        self._setup_signal_handlers()
    
    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            signal_name = signal.Signals(signum).name
            self.logger.info(f"Received signal {signal_name}, initiating graceful shutdown")
            
            # Start shutdown in background
            asyncio.create_task(self.initiate_shutdown(
                reason=ShutdownReason.SIGNAL_RECEIVED,
                details=f"Signal {signal_name} received"
            ))
        
        # Register handlers for common signals
        signal.signal(signal.SIGINT, signal_handler)   # Ctrl+C
        signal.signal(signal.SIGTERM, signal_handler)  # Termination request
        
        if hasattr(signal, 'SIGHUP'):
            signal.signal(signal.SIGHUP, signal_handler)  # Hangup (Unix only)
    
    def register_shutdown_handler(self, handler: Callable, priority: int = 0):
        """Register a shutdown handler with priority (higher = earlier execution)"""
        self.shutdown_handlers.append((priority, handler))
        self.shutdown_handlers.sort(key=lambda x: x[0], reverse=True)
    
    async def initiate_shutdown(
        self,
        reason: ShutdownReason = ShutdownReason.USER_REQUEST,
        details: str = "",
        emergency: bool = False
    ):
        """Initiate graceful shutdown process"""
        
        if self.is_shutting_down:
            self.logger.warning("Shutdown already in progress")
            return
        
        self.is_shutting_down = True
        self.shutdown_state = ShutdownState(
            reason=reason,
            phase=ShutdownPhase.INITIATED,
            started_at=datetime.now()
        )
        
        timeout = self.emergency_timeout if emergency else self.normal_timeout
        
        with trading_context(shutdown_reason=reason.value, emergency=emergency) as ctx:
            ctx.info(f"Initiating {'emergency' if emergency else 'graceful'} shutdown: {details}")
            
            try:
                # Execute shutdown with timeout
                await asyncio.wait_for(
                    self._execute_shutdown(),
                    timeout=timeout
                )
                
                self.shutdown_state.phase = ShutdownPhase.COMPLETED
                ctx.info("Graceful shutdown completed successfully")
                
            except asyncio.TimeoutError:
                self.shutdown_state.phase = ShutdownPhase.FAILED
                self.shutdown_state.errors.append(f"Shutdown timeout after {timeout} seconds")
                ctx.error(f"Shutdown timeout after {timeout} seconds, forcing exit")
                await self._force_shutdown()
                
            except Exception as e:
                self.shutdown_state.phase = ShutdownPhase.FAILED
                self.shutdown_state.errors.append(str(e))
                ctx.error(f"Shutdown failed: {e}")
                await self._force_shutdown()
            
            finally:
                # Save shutdown report
                await self._save_shutdown_report()
    
    async def _execute_shutdown(self):
        """Execute the shutdown process"""
        
        # Phase 1: Stop accepting new trades
        await self._stop_new_trades()
        
        # Phase 2: Close open positions safely
        await self._close_open_positions()
        
        # Phase 3: Save critical data
        await self._save_critical_data()
        
        # Phase 4: Cleanup services
        await self._cleanup_services()
    
    async def _stop_new_trades(self):
        """Stop accepting new trading signals and orders"""
        self.shutdown_state.phase = ShutdownPhase.STOPPING_NEW_TRADES
        self.shutdown_state.current_operation = "Stopping new trades"
        self.shutdown_state.progress_percentage = 10.0
        
        try:
            # Disable trading engine
            from app.services.trading_engine_service import trading_engine_service
            trading_engine_service.trading_enabled = False
            
            # Stop signal generation
            from app.services.signal_generation_service import signal_generation_service
            if hasattr(signal_generation_service, 'stop_generation'):
                await signal_generation_service.stop_generation()
            
            # Cancel pending orders
            await self._cancel_pending_orders()
            
            self.logger.info("New trades stopped successfully")
            
        except Exception as e:
            self.shutdown_state.errors.append(f"Error stopping new trades: {e}")
            self.logger.error(f"Error stopping new trades: {e}")
    
    async def _close_open_positions(self):
        """Close all open trading positions"""
        self.shutdown_state.phase = ShutdownPhase.CLOSING_POSITIONS
        self.shutdown_state.current_operation = "Closing open positions"
        self.shutdown_state.progress_percentage = 30.0
        
        try:
            from app.services.trading_engine_service import trading_engine_service
            
            # Get all open positions
            positions = await trading_engine_service.get_open_positions()
            
            if not positions:
                self.logger.info("No open positions to close")
                return
            
            self.logger.info(f"Closing {len(positions)} open positions")
            
            # Close positions with market orders for quick execution
            for i, position in enumerate(positions):
                try:
                    self.shutdown_state.current_operation = f"Closing position {position.symbol}"
                    
                    # Close position with market order
                    result = await trading_engine_service.close_position(
                        position_id=position.trade_id,
                        order_type="market",
                        reason="graceful_shutdown"
                    )
                    
                    if result.get("status") == "success":
                        self.logger.info(f"Closed position {position.symbol}")
                    else:
                        self.logger.warning(f"Failed to close position {position.symbol}: {result.get('message')}")
                    
                    # Update progress
                    progress = 30.0 + (i + 1) / len(positions) * 30.0
                    self.shutdown_state.progress_percentage = progress
                    
                    # Small delay between closes
                    await asyncio.sleep(0.5)
                    
                except Exception as e:
                    error_msg = f"Error closing position {position.symbol}: {e}"
                    self.shutdown_state.errors.append(error_msg)
                    self.logger.error(error_msg)
            
            self.logger.info("Position closing phase completed")
            
        except Exception as e:
            self.shutdown_state.errors.append(f"Error in position closing: {e}")
            self.logger.error(f"Error in position closing: {e}")
    
    async def _cancel_pending_orders(self):
        """Cancel all pending orders"""
        try:
            from app.services.trading_engine_service import trading_engine_service
            
            pending_orders = await trading_engine_service.get_pending_orders()
            
            for order in pending_orders:
                try:
                    await trading_engine_service.cancel_order(order.order_id)
                    self.logger.info(f"Cancelled pending order {order.order_id}")
                except Exception as e:
                    self.logger.warning(f"Failed to cancel order {order.order_id}: {e}")
            
        except Exception as e:
            self.logger.error(f"Error cancelling pending orders: {e}")
    
    async def _save_critical_data(self):
        """Save critical system data"""
        self.shutdown_state.phase = ShutdownPhase.SAVING_DATA
        self.shutdown_state.current_operation = "Saving critical data"
        self.shutdown_state.progress_percentage = 70.0
        
        try:
            # Save current system state
            await self._save_system_state()
            
            # Flush database connections
            await self._flush_database()
            
            # Save cache data
            await self._save_cache_data()
            
            self.logger.info("Critical data saved successfully")
            
        except Exception as e:
            self.shutdown_state.errors.append(f"Error saving data: {e}")
            self.logger.error(f"Error saving data: {e}")
    
    async def _cleanup_services(self):
        """Cleanup all services and connections"""
        self.shutdown_state.phase = ShutdownPhase.CLEANUP_SERVICES
        self.shutdown_state.current_operation = "Cleaning up services"
        self.shutdown_state.progress_percentage = 90.0
        
        try:
            # Execute registered shutdown handlers
            for priority, handler in self.shutdown_handlers:
                try:
                    if asyncio.iscoroutinefunction(handler):
                        await handler()
                    else:
                        handler()
                    self.logger.debug(f"Executed shutdown handler (priority {priority})")
                except Exception as e:
                    self.logger.error(f"Error in shutdown handler: {e}")
            
            # Close database connections
            await self._close_database_connections()
            
            # Stop background tasks
            await self._stop_background_tasks()
            
            # Close external connections
            await self._close_external_connections()
            
            self.shutdown_state.progress_percentage = 100.0
            self.logger.info("Service cleanup completed")
            
        except Exception as e:
            self.shutdown_state.errors.append(f"Error in cleanup: {e}")
            self.logger.error(f"Error in cleanup: {e}")
    
    async def _save_system_state(self):
        """Save current system state to file"""
        try:
            state_data = {
                "shutdown_time": datetime.now().isoformat(),
                "reason": self.shutdown_state.reason.value,
                "system_status": "shutdown_initiated",
                "active_services": [],  # Would list active services
                "configuration": {
                    "trading_enabled": getattr(settings, 'ENABLE_TRADING', False),
                    "environment": settings.ENVIRONMENT
                }
            }
            
            state_file = Path("system_state.json")
            with open(state_file, "w") as f:
                json.dump(state_data, f, indent=2)
            
            self.logger.info("System state saved")
            
        except Exception as e:
            self.logger.error(f"Error saving system state: {e}")
    
    async def _flush_database(self):
        """Flush database connections and commit pending transactions"""
        try:
            from app.core.database import get_db
            
            # This would flush any pending database operations
            # Implementation depends on your database setup
            self.logger.info("Database flushed")
            
        except Exception as e:
            self.logger.error(f"Error flushing database: {e}")
    
    async def _save_cache_data(self):
        """Save important cache data"""
        try:
            from app.core.database import cache_manager
            
            # Save critical cache data if needed
            # Implementation depends on cache setup
            self.logger.info("Cache data saved")
            
        except Exception as e:
            self.logger.error(f"Error saving cache data: {e}")
    
    async def _close_database_connections(self):
        """Close all database connections"""
        try:
            # Close database connections
            self.logger.info("Database connections closed")
            
        except Exception as e:
            self.logger.error(f"Error closing database connections: {e}")
    
    async def _stop_background_tasks(self):
        """Stop all background tasks"""
        try:
            # Cancel running tasks
            tasks = [task for task in asyncio.all_tasks() if not task.done()]
            
            for task in tasks:
                if task != asyncio.current_task():
                    task.cancel()
            
            # Wait for tasks to complete
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
            
            self.logger.info("Background tasks stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping background tasks: {e}")
    
    async def _close_external_connections(self):
        """Close external API connections"""
        try:
            # Close exchange connections, websockets, etc.
            self.logger.info("External connections closed")
            
        except Exception as e:
            self.logger.error(f"Error closing external connections: {e}")
    
    async def _force_shutdown(self):
        """Force immediate shutdown"""
        self.logger.critical("Forcing immediate shutdown")
        
        try:
            # Cancel all tasks
            tasks = [task for task in asyncio.all_tasks() if not task.done()]
            for task in tasks:
                if task != asyncio.current_task():
                    task.cancel()
            
            # Exit immediately
            sys.exit(1)
            
        except Exception as e:
            self.logger.critical(f"Error in force shutdown: {e}")
            sys.exit(1)
    
    async def _save_shutdown_report(self):
        """Save detailed shutdown report"""
        try:
            report = {
                "shutdown_id": f"shutdown_{int(self.shutdown_state.started_at.timestamp())}",
                "reason": self.shutdown_state.reason.value,
                "phase": self.shutdown_state.phase.value,
                "started_at": self.shutdown_state.started_at.isoformat(),
                "completed_at": datetime.now().isoformat(),
                "duration_seconds": (datetime.now() - self.shutdown_state.started_at).total_seconds(),
                "progress_percentage": self.shutdown_state.progress_percentage,
                "errors": self.shutdown_state.errors,
                "success": self.shutdown_state.phase == ShutdownPhase.COMPLETED
            }
            
            # Save to logs directory
            log_dir = Path("logs")
            log_dir.mkdir(exist_ok=True)
            
            report_file = log_dir / f"shutdown_report_{int(self.shutdown_state.started_at.timestamp())}.json"
            with open(report_file, "w") as f:
                json.dump(report, f, indent=2)
            
            self.logger.info(f"Shutdown report saved to {report_file}")
            
        except Exception as e:
            self.logger.error(f"Error saving shutdown report: {e}")
    
    def get_shutdown_status(self) -> Dict[str, Any]:
        """Get current shutdown status"""
        if not self.shutdown_state:
            return {"is_shutting_down": False}
        
        return {
            "is_shutting_down": self.is_shutting_down,
            "reason": self.shutdown_state.reason.value,
            "phase": self.shutdown_state.phase.value,
            "progress_percentage": self.shutdown_state.progress_percentage,
            "current_operation": self.shutdown_state.current_operation,
            "started_at": self.shutdown_state.started_at.isoformat(),
            "errors": self.shutdown_state.errors
        }


# Global shutdown manager instance
shutdown_manager = GracefulShutdownManager()

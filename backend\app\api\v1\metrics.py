"""
Metrics API Endpoints
Prometheus metrics exposure and monitoring dashboard APIs
Author: inkbytefo
"""

from fastapi import APIRouter, HTTPException, Depends, Response
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import logging

from app.services.metrics_service import metrics_service
from app.services.alerting_service import alerting_service, AlertRule, AlertSeverity
from app.core.auth import get_current_user
from app.models.user import User
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)

router = APIRouter()

# Pydantic Models
class MetricsSummaryResponse(BaseModel):
    """Response model for metrics summary"""
    timestamp: datetime
    total_metrics: int
    active_monitoring: bool
    system_health: Dict[str, Any]
    trading_metrics: Dict[str, Any]
    performance_metrics: Dict[str, Any]

class SystemHealthResponse(BaseModel):
    """Response model for system health"""
    status: str
    cpu_usage_percent: float
    memory_usage_percent: float
    disk_usage_percent: Optional[float] = None
    active_connections: int
    uptime_seconds: float
    last_updated: datetime

class TradingMetricsResponse(BaseModel):
    """Response model for trading metrics"""
    total_trades: int
    successful_trades: int
    failed_trades: int
    total_volume_usd: float
    average_trade_duration: float
    active_positions: int
    portfolio_value_usd: float
    current_drawdown_percent: float

class PerformanceMetricsResponse(BaseModel):
    """Response model for performance metrics"""
    api_requests_per_minute: float
    average_response_time_ms: float
    error_rate_percent: float
    market_data_latency_ms: float
    signal_generation_rate: float
    backtest_completion_rate: float

class AlertRuleRequest(BaseModel):
    """Request model for creating alert rules"""
    name: str = Field(..., description="Unique name for the alert rule")
    description: str = Field(..., description="Description of what the alert monitors")
    metric_name: str = Field(..., description="Name of the metric to monitor")
    condition: str = Field(..., pattern="^(gt|gte|lt|lte|eq|ne)$", description="Condition to evaluate")
    threshold: float = Field(..., description="Threshold value for the condition")
    severity: str = Field(..., pattern="^(info|warning|error|critical)$", description="Alert severity")
    duration_seconds: int = Field(60, ge=0, description="Duration condition must be true before alerting")
    cooldown_seconds: int = Field(300, ge=0, description="Minimum time between alerts")
    enabled: bool = Field(True, description="Whether the rule is enabled")
    labels: Dict[str, str] = Field(default_factory=dict, description="Additional labels for the alert")

class AlertResponse(BaseModel):
    """Response model for alerts"""
    rule_name: str
    severity: str
    status: str
    message: str
    metric_name: str
    current_value: float
    threshold: float
    triggered_at: datetime
    resolved_at: Optional[datetime] = None
    acknowledged_at: Optional[datetime] = None
    labels: Dict[str, str]
    metadata: Dict[str, Any]

class AlertSummaryResponse(BaseModel):
    """Response model for alert summary"""
    active_alerts: int
    active_by_severity: Dict[str, int]
    total_rules: int
    enabled_rules: int
    total_historical_alerts: int
    resolution_rate: float
    monitoring_active: bool

@router.get("/prometheus")
async def get_prometheus_metrics():
    """
    Get metrics in Prometheus format
    
    This endpoint exposes all system metrics in Prometheus-compatible format
    for scraping by Prometheus server.
    """
    try:
        metrics_export = metrics_service.get_metrics_export()
        
        return Response(
            content=metrics_export,
            media_type="text/plain; version=0.0.4; charset=utf-8"
        )
        
    except Exception as e:
        logger.error(f"Error exporting Prometheus metrics: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to export metrics: {str(e)}"
        )

@router.get("/summary", response_model=MetricsSummaryResponse)
async def get_metrics_summary(
    current_user: User = Depends(get_current_user)
):
    """
    Get comprehensive metrics summary
    
    Returns a summary of all system metrics including:
    - System health indicators
    - Trading performance metrics
    - API performance metrics
    """
    try:
        logger.info(f"User {current_user.username} requesting metrics summary")
        
        # Get all metrics
        metrics_summary = metrics_service.get_metrics_summary()
        
        # Extract system health metrics
        system_health = {
            "cpu_usage": metrics_summary.get("system_cpu_usage_percent", {}).get("value", 0),
            "memory_usage": metrics_summary.get("system_memory_usage_percent", {}).get("value", 0),
            "disk_usage": metrics_summary.get("system_disk_usage_percent", {}).get("value", 0),
            "network_sent": metrics_summary.get("network_bytes_sent_total", {}).get("value", 0),
            "network_received": metrics_summary.get("network_bytes_received_total", {}).get("value", 0)
        }
        
        # Extract trading metrics
        trading_metrics = {
            "total_trades": len([m for name, m in metrics_summary.items() if "trades_total" in name]),
            "portfolio_value": metrics_summary.get("portfolio_value_usd", {}).get("value", 0),
            "open_positions": metrics_summary.get("open_positions_count", {}).get("value", 0),
            "drawdown": metrics_summary.get("portfolio_drawdown_percent", {}).get("value", 0),
            "circuit_breaker_state": metrics_summary.get("circuit_breaker_state", {}).get("value", 0),
            "signals_generated": len([m for name, m in metrics_summary.items() if "signals_generated" in name])
        }
        
        # Extract performance metrics
        performance_metrics = {
            "api_requests": len([m for name, m in metrics_summary.items() if "api_requests_total" in name]),
            "market_data_updates": len([m for name, m in metrics_summary.items() if "market_data_updates" in name]),
            "backtests_completed": len([m for name, m in metrics_summary.items() if "backtests_total" in name]),
            "risk_alerts": len([m for name, m in metrics_summary.items() if "risk_alerts_total" in name])
        }
        
        response = MetricsSummaryResponse(
            timestamp=datetime.utcnow(),
            total_metrics=len(metrics_summary),
            active_monitoring=metrics_service.monitoring_active,
            system_health=system_health,
            trading_metrics=trading_metrics,
            performance_metrics=performance_metrics
        )
        
        return response
        
    except Exception as e:
        logger.error(f"Error getting metrics summary: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get metrics summary: {str(e)}"
        )

@router.get("/health", response_model=SystemHealthResponse)
async def get_system_health():
    """
    Get system health status
    
    Returns current system health indicators including:
    - CPU and memory usage
    - Service status
    - Connection counts
    """
    try:
        metrics_summary = metrics_service.get_metrics_summary()
        
        # Calculate system health status
        cpu_usage = metrics_summary.get("system_cpu_usage_percent", {}).get("value", 0)
        memory_usage = metrics_summary.get("system_memory_usage_percent", {}).get("value", 0)
        
        # Determine overall health status
        if cpu_usage > 90 or memory_usage > 90:
            status = "critical"
        elif cpu_usage > 70 or memory_usage > 70:
            status = "warning"
        else:
            status = "healthy"
        
        response = SystemHealthResponse(
            status=status,
            cpu_usage_percent=cpu_usage,
            memory_usage_percent=memory_usage,
            disk_usage_percent=metrics_summary.get("system_disk_usage_percent", {}).get("value"),
            active_connections=0,  # Would be extracted from actual connection pool
            uptime_seconds=0,  # Would be calculated from service start time
            last_updated=datetime.utcnow()
        )
        
        return response
        
    except Exception as e:
        logger.error(f"Error getting system health: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get system health: {str(e)}"
        )

@router.get("/trading", response_model=TradingMetricsResponse)
async def get_trading_metrics(
    current_user: User = Depends(get_current_user)
):
    """
    Get trading-specific metrics
    
    Returns comprehensive trading performance metrics including:
    - Trade statistics
    - Portfolio performance
    - Risk metrics
    """
    try:
        logger.info(f"User {current_user.username} requesting trading metrics")
        
        metrics_summary = metrics_service.get_metrics_summary()
        
        # Extract trading-specific metrics
        portfolio_value = metrics_summary.get("portfolio_value_usd", {}).get("value", 0)
        open_positions = metrics_summary.get("open_positions_count", {}).get("value", 0)
        drawdown = metrics_summary.get("portfolio_drawdown_percent", {}).get("value", 0)
        
        # Calculate derived metrics
        total_trades = len([m for name, m in metrics_summary.items() if "trades_total" in name])
        successful_trades = 0  # Would need to filter by status
        failed_trades = 0      # Would need to filter by status
        
        response = TradingMetricsResponse(
            total_trades=total_trades,
            successful_trades=successful_trades,
            failed_trades=failed_trades,
            total_volume_usd=0,  # Would be calculated from trade history
            average_trade_duration=0,  # Would be calculated from histogram
            active_positions=int(open_positions),
            portfolio_value_usd=portfolio_value,
            current_drawdown_percent=drawdown
        )
        
        return response
        
    except Exception as e:
        logger.error(f"Error getting trading metrics: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get trading metrics: {str(e)}"
        )

@router.get("/performance", response_model=PerformanceMetricsResponse)
async def get_performance_metrics(
    current_user: User = Depends(get_current_user)
):
    """
    Get system performance metrics
    
    Returns system performance indicators including:
    - API performance
    - Market data performance
    - Processing rates
    """
    try:
        logger.info(f"User {current_user.username} requesting performance metrics")
        
        metrics_summary = metrics_service.get_metrics_summary()
        
        # Extract performance metrics
        api_requests = len([m for name, m in metrics_summary.items() if "api_requests_total" in name])
        market_data_updates = len([m for name, m in metrics_summary.items() if "market_data_updates" in name])
        signals_generated = len([m for name, m in metrics_summary.items() if "signals_generated" in name])
        backtests_completed = len([m for name, m in metrics_summary.items() if "backtests_total" in name])
        
        # Calculate rates (would need time-based calculations)
        api_requests_per_minute = api_requests / 60.0  # Simplified
        signal_generation_rate = signals_generated / 60.0  # Simplified
        backtest_completion_rate = backtests_completed / 60.0  # Simplified
        
        response = PerformanceMetricsResponse(
            api_requests_per_minute=api_requests_per_minute,
            average_response_time_ms=0,  # Would be calculated from histogram
            error_rate_percent=0,  # Would be calculated from error counts
            market_data_latency_ms=0,  # Would be extracted from latency metrics
            signal_generation_rate=signal_generation_rate,
            backtest_completion_rate=backtest_completion_rate
        )
        
        return response
        
    except Exception as e:
        logger.error(f"Error getting performance metrics: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get performance metrics: {str(e)}"
        )

@router.post("/start-monitoring")
async def start_metrics_monitoring(
    current_user: User = Depends(get_current_user)
):
    """
    Start metrics monitoring
    
    Starts background collection of system metrics.
    """
    try:
        logger.info(f"User {current_user.username} starting metrics monitoring")
        
        await metrics_service.start_monitoring()
        
        return {
            "message": "Metrics monitoring started successfully",
            "monitoring_active": metrics_service.monitoring_active,
            "collection_interval": metrics_service.system_metrics_interval,
            "timestamp": datetime.utcnow()
        }
        
    except Exception as e:
        logger.error(f"Error starting metrics monitoring: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start metrics monitoring: {str(e)}"
        )

@router.post("/stop-monitoring")
async def stop_metrics_monitoring(
    current_user: User = Depends(get_current_user)
):
    """
    Stop metrics monitoring
    
    Stops background collection of system metrics.
    """
    try:
        logger.info(f"User {current_user.username} stopping metrics monitoring")
        
        await metrics_service.stop_monitoring()
        
        return {
            "message": "Metrics monitoring stopped successfully",
            "monitoring_active": metrics_service.monitoring_active,
            "timestamp": datetime.utcnow()
        }
        
    except Exception as e:
        logger.error(f"Error stopping metrics monitoring: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to stop metrics monitoring: {str(e)}"
        )

@router.get("/raw/{metric_name}")
async def get_raw_metric(
    metric_name: str,
    current_user: User = Depends(get_current_user)
):
    """
    Get raw metric data
    
    Returns raw time series data for a specific metric.
    """
    try:
        metric = metrics_service.collector.get_metric(metric_name)
        
        if not metric:
            raise HTTPException(
                status_code=404,
                detail=f"Metric '{metric_name}' not found"
            )
        
        # Convert metric values to response format
        values = []
        for value in metric.values:
            values.append({
                "timestamp": value.timestamp.isoformat(),
                "value": value.value,
                "labels": value.labels
            })
        
        return {
            "metric_name": metric_name,
            "metric_type": metric.metric_type,
            "help_text": metric.help_text,
            "values": values,
            "total_values": len(values)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting raw metric {metric_name}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get raw metric: {str(e)}"
        )

@router.get("/list")
async def list_available_metrics(
    current_user: User = Depends(get_current_user)
):
    """
    List all available metrics
    
    Returns a list of all registered metrics with their types and descriptions.
    """
    try:
        all_metrics = metrics_service.collector.get_all_metrics()
        
        metrics_list = []
        for name, series in all_metrics.items():
            metrics_list.append({
                "name": name,
                "type": series.metric_type,
                "help": series.help_text,
                "labels": list(series.labels.keys()),
                "value_count": len(series.values)
            })
        
        return {
            "total_metrics": len(metrics_list),
            "metrics": sorted(metrics_list, key=lambda x: x["name"])
        }
        
    except Exception as e:
        logger.error(f"Error listing metrics: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list metrics: {str(e)}"
        )

# Alerting Endpoints

@router.get("/alerts/summary", response_model=AlertSummaryResponse)
async def get_alerts_summary(
    current_user: User = Depends(get_current_user)
):
    """
    Get alerts summary

    Returns summary of all alerts including active alerts by severity,
    total rules, and resolution statistics.
    """
    try:
        summary = alerting_service.get_alert_summary()

        return AlertSummaryResponse(
            active_alerts=summary["active_alerts"],
            active_by_severity=summary["active_by_severity"],
            total_rules=summary["total_rules"],
            enabled_rules=summary["enabled_rules"],
            total_historical_alerts=summary["total_historical_alerts"],
            resolution_rate=summary["resolution_rate"],
            monitoring_active=summary["monitoring_active"]
        )

    except Exception as e:
        logger.error(f"Error getting alerts summary: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get alerts summary: {str(e)}"
        )

@router.get("/alerts/active", response_model=List[AlertResponse])
async def get_active_alerts(
    current_user: User = Depends(get_current_user)
):
    """
    Get all active alerts

    Returns list of currently active alerts with their details.
    """
    try:
        active_alerts = alerting_service.get_active_alerts()

        response = []
        for alert in active_alerts:
            response.append(AlertResponse(
                rule_name=alert.rule_name,
                severity=alert.severity.value,
                status=alert.status.value,
                message=alert.message,
                metric_name=alert.metric_name,
                current_value=alert.current_value,
                threshold=alert.threshold,
                triggered_at=alert.triggered_at,
                resolved_at=alert.resolved_at,
                acknowledged_at=alert.acknowledged_at,
                labels=alert.labels,
                metadata=alert.metadata
            ))

        return response

    except Exception as e:
        logger.error(f"Error getting active alerts: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get active alerts: {str(e)}"
        )

@router.get("/alerts/history", response_model=List[AlertResponse])
async def get_alerts_history(
    limit: int = 50,
    current_user: User = Depends(get_current_user)
):
    """
    Get alerts history

    Returns historical alerts with optional limit.
    """
    try:
        alert_history = alerting_service.get_alert_history(limit)

        response = []
        for alert in alert_history:
            response.append(AlertResponse(
                rule_name=alert.rule_name,
                severity=alert.severity.value,
                status=alert.status.value,
                message=alert.message,
                metric_name=alert.metric_name,
                current_value=alert.current_value,
                threshold=alert.threshold,
                triggered_at=alert.triggered_at,
                resolved_at=alert.resolved_at,
                acknowledged_at=alert.acknowledged_at,
                labels=alert.labels,
                metadata=alert.metadata
            ))

        return response

    except Exception as e:
        logger.error(f"Error getting alerts history: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get alerts history: {str(e)}"
        )

@router.post("/alerts/rules")
async def create_alert_rule(
    request: AlertRuleRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Create a new alert rule

    Creates a new alert rule for monitoring specific metrics.
    """
    try:
        logger.info(f"User {current_user.username} creating alert rule: {request.name}")

        # Convert severity string to enum
        severity = AlertSeverity(request.severity)

        # Create alert rule
        rule = AlertRule(
            name=request.name,
            description=request.description,
            metric_name=request.metric_name,
            condition=request.condition,
            threshold=request.threshold,
            severity=severity,
            duration_seconds=request.duration_seconds,
            cooldown_seconds=request.cooldown_seconds,
            enabled=request.enabled,
            labels=request.labels
        )

        alerting_service.add_rule(rule)

        return {
            "message": "Alert rule created successfully",
            "rule_name": request.name,
            "enabled": request.enabled,
            "timestamp": datetime.utcnow()
        }

    except ValueError as e:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid severity level: {request.severity}"
        )
    except Exception as e:
        logger.error(f"Error creating alert rule: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create alert rule: {str(e)}"
        )

@router.post("/alerts/start-monitoring")
async def start_alert_monitoring(
    current_user: User = Depends(get_current_user)
):
    """
    Start alert monitoring

    Starts background monitoring of all enabled alert rules.
    """
    try:
        logger.info(f"User {current_user.username} starting alert monitoring")

        await alerting_service.start_monitoring()

        return {
            "message": "Alert monitoring started successfully",
            "monitoring_active": alerting_service.monitoring_active,
            "evaluation_interval": alerting_service.evaluation_interval,
            "total_rules": len(alerting_service.rules),
            "enabled_rules": sum(1 for rule in alerting_service.rules.values() if rule.enabled),
            "timestamp": datetime.utcnow()
        }

    except Exception as e:
        logger.error(f"Error starting alert monitoring: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start alert monitoring: {str(e)}"
        )

@router.post("/alerts/stop-monitoring")
async def stop_alert_monitoring(
    current_user: User = Depends(get_current_user)
):
    """
    Stop alert monitoring

    Stops background monitoring of alert rules.
    """
    try:
        logger.info(f"User {current_user.username} stopping alert monitoring")

        await alerting_service.stop_monitoring()

        return {
            "message": "Alert monitoring stopped successfully",
            "monitoring_active": alerting_service.monitoring_active,
            "timestamp": datetime.utcnow()
        }

    except Exception as e:
        logger.error(f"Error stopping alert monitoring: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to stop alert monitoring: {str(e)}"
        )

"""
AI Analysis Service for Cryptocurrency Trading
Author: inkbytefo

This service provides:
- News aggregation from multiple sources
- Sentiment analysis using AI
- Market analysis and insights
- AI-powered trading recommendations
"""

import asyncio
import aiohttp
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
from dataclasses import dataclass
from enum import Enum
import json
import re
from urllib.parse import urljoin
import feedparser
import openai
from openai import AsyncOpenAI

from app.core.config import settings
from app.core.database import cache_manager
from app.core.ai_prompts import AIPromptTemplates, AIModelConfig, PromptValidation

logger = logging.getLogger(__name__)


class SentimentType(Enum):
    """Sentiment classification"""
    VERY_BEARISH = "very_bearish"
    BEARISH = "bearish"
    NEUTRAL = "neutral"
    BULLISH = "bullish"
    VERY_BULLISH = "very_bullish"


class NewsSource(Enum):
    """News source types"""
    COINGECKO = "coingecko"
    NEWSAPI = "newsapi"
    RSS_FEED = "rss_feed"
    REDDIT = "reddit"
    MANUAL = "manual"


@dataclass
class NewsArticle:
    """News article data structure"""
    title: str
    content: str
    url: str
    source: NewsSource
    published_at: datetime
    symbol: Optional[str] = None
    sentiment_score: Optional[float] = None
    sentiment_type: Optional[SentimentType] = None
    relevance_score: Optional[float] = None
    keywords: List[str] = None


@dataclass
class SentimentAnalysis:
    """Sentiment analysis result"""
    symbol: str
    overall_sentiment: SentimentType
    sentiment_score: float  # -1 to 1
    confidence: float  # 0 to 1
    news_count: int
    bullish_signals: List[str]
    bearish_signals: List[str]
    key_themes: List[str]
    analysis_timestamp: datetime


@dataclass
class AIMarketInsight:
    """AI-generated market insight"""
    symbol: str
    insight_type: str  # "technical", "fundamental", "sentiment", "news"
    title: str
    description: str
    confidence: float
    impact_score: float  # -1 to 1
    time_horizon: str  # "short", "medium", "long"
    supporting_evidence: List[str]
    generated_at: datetime


@dataclass
class AIAnalysisResult:
    """Complete AI analysis result"""
    symbol: str
    timeframe: str
    sentiment_analysis: SentimentAnalysis
    market_insights: List[AIMarketInsight]
    ai_recommendation: str
    confidence_score: float
    risk_assessment: str
    key_factors: List[str]
    generated_at: datetime
    valid_until: datetime


class AIAnalysisService:
    """AI Analysis Service for cryptocurrency trading"""
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.openai_client: Optional[AsyncOpenAI] = None
        self._initialize_clients()
    
    def _initialize_clients(self):
        """Initialize API clients"""
        try:
            # Initialize OpenAI client (compatible with local LLMs via LiteLLM)
            if settings.OPENAI_API_KEY:
                # Use OpenAI API or OpenAI-compatible endpoint
                base_url = getattr(settings, 'OPENAI_BASE_URL', 'https://api.openai.com/v1')
                self.openai_client = AsyncOpenAI(
                    api_key=settings.OPENAI_API_KEY,
                    base_url=base_url
                )
                logger.info("OpenAI client initialized")
            else:
                logger.warning("OpenAI API key not configured")

        except (ValueError, TypeError) as e:
            logger.error(f"Configuration error initializing AI clients: {e}")
        except Exception as e:
            logger.error(f"Unexpected error initializing AI clients: {e}")
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create HTTP session"""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30),
                headers={'User-Agent': 'AI-Crypto-Trading-Bot/1.0'}
            )
        return self.session
    
    async def close(self):
        """Close HTTP session"""
        if self.session and not self.session.closed:
            await self.session.close()
    
    async def get_crypto_news(self, symbol: str, limit: int = 20) -> List[NewsArticle]:
        """Aggregate cryptocurrency news from multiple sources"""
        try:
            logger.info(f"Fetching news for {symbol}")
            
            # Check cache first
            cache_key = f"news:{symbol}:{limit}"
            cached_news = await cache_manager.get(cache_key)
            if cached_news:
                logger.info(f"📰 Using cached news for {symbol}")
                return json.loads(cached_news)
            
            news_articles = []
            
            # Fetch from CoinGecko (if available)
            coingecko_news = await self._fetch_coingecko_news(symbol, limit // 3)
            news_articles.extend(coingecko_news)
            
            # Fetch from NewsAPI (if available)
            newsapi_news = await self._fetch_newsapi_crypto(symbol, limit // 3)
            news_articles.extend(newsapi_news)
            
            # Fetch from RSS feeds
            rss_news = await self._fetch_rss_crypto_news(symbol, limit // 3)
            news_articles.extend(rss_news)
            
            # Sort by relevance and recency
            news_articles = sorted(
                news_articles,
                key=lambda x: (x.relevance_score or 0, x.published_at),
                reverse=True
            )[:limit]
            
            # Cache results
            await cache_manager.set(
                cache_key,
                json.dumps([self._serialize_news_article(article) for article in news_articles]),
                ttl=1800  # 30 minutes
            )
            
            logger.info(f"📰 Fetched {len(news_articles)} news articles for {symbol}")
            return news_articles
            
        except Exception as e:
            logger.error(f"❌ Error fetching news for {symbol}: {e}")
            return []
    
    async def _fetch_coingecko_news(self, symbol: str, limit: int) -> List[NewsArticle]:
        """Fetch news from CoinGecko API"""
        try:
            # CoinGecko doesn't have a direct news API, but we can use their general API
            # This is a placeholder for actual implementation
            logger.info(f"Fetching CoinGecko news for {symbol}")
            return []
            
        except Exception as e:
            logger.error(f"Error fetching CoinGecko news: {e}")
            return []
    
    async def _fetch_newsapi_crypto(self, symbol: str, limit: int) -> List[NewsArticle]:
        """Fetch crypto news from NewsAPI"""
        try:
            if not hasattr(settings, 'NEWSAPI_KEY') or not settings.NEWSAPI_KEY:
                logger.warning("NewsAPI key not configured")
                return []
            
            session = await self._get_session()
            
            # Search for cryptocurrency news
            query = f"{symbol} OR cryptocurrency OR bitcoin OR crypto"
            url = "https://newsapi.org/v2/everything"
            
            params = {
                'q': query,
                'language': 'en',
                'sortBy': 'publishedAt',
                'pageSize': limit,
                'apiKey': settings.NEWSAPI_KEY
            }
            
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    articles = []
                    
                    for article in data.get('articles', []):
                        if article.get('title') and article.get('description'):
                            news_article = NewsArticle(
                                title=article['title'],
                                content=article.get('description', ''),
                                url=article.get('url', ''),
                                source=NewsSource.NEWSAPI,
                                published_at=datetime.fromisoformat(
                                    article['publishedAt'].replace('Z', '+00:00')
                                ),
                                symbol=symbol,
                                relevance_score=self._calculate_relevance_score(
                                    article['title'] + ' ' + article.get('description', ''),
                                    symbol
                                )
                            )
                            articles.append(news_article)
                    
                    return articles
                else:
                    logger.error(f"NewsAPI error: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"Error fetching NewsAPI crypto news: {e}")
            return []
    
    async def _fetch_rss_crypto_news(self, symbol: str, limit: int) -> List[NewsArticle]:
        """Fetch crypto news from RSS feeds"""
        try:
            # Popular crypto news RSS feeds
            rss_feeds = [
                "https://cointelegraph.com/rss",
                "https://www.coindesk.com/arc/outboundfeeds/rss/",
                "https://cryptonews.com/news/feed/",
            ]
            
            articles = []
            
            for feed_url in rss_feeds:
                try:
                    # Parse RSS feed
                    feed = feedparser.parse(feed_url)
                    
                    for entry in feed.entries[:limit // len(rss_feeds)]:
                        if hasattr(entry, 'title') and hasattr(entry, 'summary'):
                            # Calculate relevance to symbol
                            content = entry.title + ' ' + entry.summary
                            relevance = self._calculate_relevance_score(content, symbol)
                            
                            if relevance > 0.1:  # Only include relevant articles
                                news_article = NewsArticle(
                                    title=entry.title,
                                    content=entry.summary,
                                    url=entry.get('link', ''),
                                    source=NewsSource.RSS_FEED,
                                    published_at=datetime.fromtimestamp(
                                        entry.published_parsed
                                    ) if hasattr(entry, 'published_parsed') else datetime.utcnow(),
                                    symbol=symbol,
                                    relevance_score=relevance
                                )
                                articles.append(news_article)
                                
                except Exception as e:
                    logger.error(f"Error parsing RSS feed {feed_url}: {e}")
                    continue
            
            return articles
            
        except Exception as e:
            logger.error(f"Error fetching RSS crypto news: {e}")
            return []
    
    def _calculate_relevance_score(self, text: str, symbol: str) -> float:
        """Calculate relevance score of text to symbol"""
        try:
            text_lower = text.lower()
            symbol_clean = symbol.replace('/', '').replace('-', '').lower()
            
            # Base symbol matching
            score = 0.0
            
            # Direct symbol match
            if symbol_clean in text_lower:
                score += 0.8
            
            # Common crypto terms
            crypto_terms = ['bitcoin', 'ethereum', 'crypto', 'blockchain', 'defi', 'nft']
            for term in crypto_terms:
                if term in text_lower:
                    score += 0.2
                    break
            
            # Symbol-specific terms
            symbol_terms = {
                'btc': ['bitcoin', 'btc'],
                'eth': ['ethereum', 'eth', 'ether'],
                'bnb': ['binance', 'bnb'],
                'ada': ['cardano', 'ada'],
                'sol': ['solana', 'sol']
            }
            
            for key, terms in symbol_terms.items():
                if key in symbol_clean:
                    for term in terms:
                        if term in text_lower:
                            score += 0.5
                            break
            
            return min(score, 1.0)
            
        except Exception as e:
            logger.error(f"Error calculating relevance score: {e}")
            return 0.0
    
    def _serialize_news_article(self, article: NewsArticle) -> Dict[str, Any]:
        """Serialize news article for caching"""
        return {
            'title': article.title,
            'content': article.content,
            'url': article.url,
            'source': article.source.value,
            'published_at': article.published_at.isoformat(),
            'symbol': article.symbol,
            'sentiment_score': article.sentiment_score,
            'sentiment_type': article.sentiment_type.value if article.sentiment_type else None,
            'relevance_score': article.relevance_score,
            'keywords': article.keywords or []
        }


    async def analyze_sentiment(self, symbol: str, news_articles: List[NewsArticle] = None) -> SentimentAnalysis:
        """Analyze sentiment from news articles using AI"""
        try:
            logger.info(f"Analyzing sentiment for {symbol}")

            # Get news articles if not provided
            if news_articles is None:
                news_articles = await self.get_crypto_news(symbol, limit=20)

            if not news_articles:
                logger.warning(f"No news articles found for sentiment analysis of {symbol}")
                return SentimentAnalysis(
                    symbol=symbol,
                    overall_sentiment=SentimentType.NEUTRAL,
                    sentiment_score=0.0,
                    confidence=0.0,
                    news_count=0,
                    bullish_signals=[],
                    bearish_signals=[],
                    key_themes=[],
                    analysis_timestamp=datetime.utcnow()
                )

            # Check cache
            cache_key = f"sentiment:{symbol}:{len(news_articles)}"
            cached_sentiment = await cache_manager.get(cache_key)
            if cached_sentiment:
                logger.info(f"📊 Using cached sentiment for {symbol}")
                return self._deserialize_sentiment_analysis(json.loads(cached_sentiment))

            # Prepare news content for AI analysis
            news_content = self._prepare_news_for_analysis(news_articles)

            # Analyze sentiment using AI
            sentiment_result = await self._analyze_sentiment_with_ai(symbol, news_content)

            # Cache results
            await cache_manager.set(
                cache_key,
                json.dumps(self._serialize_sentiment_analysis(sentiment_result)),
                ttl=1800  # 30 minutes
            )

            logger.info(f"📊 Sentiment analysis completed for {symbol}: {sentiment_result.overall_sentiment.value}")
            return sentiment_result

        except openai.APIError as e:
            logger.error(f"❌ OpenAI API error analyzing sentiment for {symbol}: {e}")
            return SentimentAnalysis(
                symbol=symbol,
                overall_sentiment=SentimentType.NEUTRAL,
                sentiment_score=0.0,
                confidence=0.0,
                news_count=0,
                bullish_signals=[],
                bearish_signals=[],
                key_themes=[],
                analysis_timestamp=datetime.utcnow()
            )
        except (aiohttp.ClientError, asyncio.TimeoutError) as e:
            logger.error(f"❌ Network error analyzing sentiment for {symbol}: {e}")
            return SentimentAnalysis(
                symbol=symbol,
                overall_sentiment=SentimentType.NEUTRAL,
                sentiment_score=0.0,
                confidence=0.0,
                news_count=0,
                bullish_signals=[],
                bearish_signals=[],
                key_themes=[],
                analysis_timestamp=datetime.utcnow()
            )
        except (json.JSONDecodeError, KeyError, ValueError) as e:
            logger.error(f"❌ Data parsing error analyzing sentiment for {symbol}: {e}")
            return SentimentAnalysis(
                symbol=symbol,
                overall_sentiment=SentimentType.NEUTRAL,
                sentiment_score=0.0,
                confidence=0.0,
                news_count=0,
                bullish_signals=[],
                bearish_signals=[],
                key_themes=[],
                analysis_timestamp=datetime.utcnow()
            )
        except Exception as e:
            logger.error(f"❌ Unexpected error analyzing sentiment for {symbol}: {e}")
            return SentimentAnalysis(
                symbol=symbol,
                overall_sentiment=SentimentType.NEUTRAL,
                sentiment_score=0.0,
                confidence=0.0,
                news_count=0,
                bullish_signals=[],
                bearish_signals=[],
                key_themes=[],
                analysis_timestamp=datetime.utcnow()
            )

    def _prepare_news_for_analysis(self, news_articles: List[NewsArticle]) -> str:
        """Prepare news articles for AI analysis"""
        content_parts = []

        for i, article in enumerate(news_articles[:10], 1):  # Limit to 10 articles
            content_parts.append(f"Article {i}:")
            content_parts.append(f"Title: {article.title}")
            content_parts.append(f"Content: {article.content[:500]}...")  # Limit content length
            content_parts.append(f"Published: {article.published_at.strftime('%Y-%m-%d %H:%M')}")
            content_parts.append("---")

        return "\n".join(content_parts)

    async def _analyze_sentiment_with_ai(self, symbol: str, news_content: str) -> SentimentAnalysis:
        """Analyze sentiment using AI (OpenAI or compatible)"""
        try:
            if not self.openai_client:
                logger.warning("OpenAI client not available, using fallback sentiment analysis")
                return self._fallback_sentiment_analysis(symbol, news_content)

            # Use optimized sentiment analysis prompt
            prompt = AIPromptTemplates.get_sentiment_analysis_prompt(symbol, news_content)

            # Use optimized prompts and model configuration
            system_prompt = AIPromptTemplates.get_sentiment_analysis_system_prompt()
            model_config = AIModelConfig.get_model_config("sentiment_analysis")

            # Call AI API with optimized parameters
            response = await self.openai_client.chat.completions.create(
                model=model_config["model"],
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": prompt}
                ],
                temperature=model_config["temperature"],
                max_tokens=model_config["max_tokens"],
                top_p=model_config["top_p"],
                frequency_penalty=model_config["frequency_penalty"],
                response_format={"type": "json_object"}  # Ensure JSON response
            )

            # Parse AI response
            ai_response = response.choices[0].message.content

            # Extract JSON from response
            try:
                # Find JSON in the response
                json_start = ai_response.find('{')
                json_end = ai_response.rfind('}') + 1
                json_str = ai_response[json_start:json_end]

                ai_analysis = json.loads(json_str)

                # Map sentiment string to enum
                sentiment_map = {
                    'very_bearish': SentimentType.VERY_BEARISH,
                    'bearish': SentimentType.BEARISH,
                    'neutral': SentimentType.NEUTRAL,
                    'bullish': SentimentType.BULLISH,
                    'very_bullish': SentimentType.VERY_BULLISH
                }

                overall_sentiment = sentiment_map.get(
                    ai_analysis.get('overall_sentiment', 'neutral'),
                    SentimentType.NEUTRAL
                )

                return SentimentAnalysis(
                    symbol=symbol,
                    overall_sentiment=overall_sentiment,
                    sentiment_score=float(ai_analysis.get('sentiment_score', 0.0)),
                    confidence=float(ai_analysis.get('confidence', 0.5)),
                    news_count=len(news_content.split('Article ')),
                    bullish_signals=ai_analysis.get('bullish_signals', []),
                    bearish_signals=ai_analysis.get('bearish_signals', []),
                    key_themes=ai_analysis.get('key_themes', []),
                    analysis_timestamp=datetime.utcnow()
                )

            except json.JSONDecodeError as e:
                logger.error(f"Error parsing AI response JSON: {e}")
                return self._fallback_sentiment_analysis(symbol, news_content)

        except openai.APIError as e:
            logger.error(f"OpenAI API error in sentiment analysis: {e}")
            return self._fallback_sentiment_analysis(symbol, news_content)
        except (aiohttp.ClientError, asyncio.TimeoutError) as e:
            logger.error(f"Network error in AI sentiment analysis: {e}")
            return self._fallback_sentiment_analysis(symbol, news_content)
        except Exception as e:
            logger.error(f"Unexpected error in AI sentiment analysis: {e}")
            return self._fallback_sentiment_analysis(symbol, news_content)

    def _fallback_sentiment_analysis(self, symbol: str, news_content: str) -> SentimentAnalysis:
        """Fallback sentiment analysis using keyword matching"""
        try:
            content_lower = news_content.lower()

            # Define sentiment keywords
            bullish_keywords = [
                'bullish', 'bull', 'rise', 'rising', 'up', 'gain', 'gains', 'positive',
                'growth', 'adoption', 'partnership', 'upgrade', 'breakthrough', 'rally',
                'surge', 'pump', 'moon', 'institutional', 'investment', 'buy'
            ]

            bearish_keywords = [
                'bearish', 'bear', 'fall', 'falling', 'down', 'loss', 'losses', 'negative',
                'decline', 'crash', 'dump', 'sell', 'selling', 'regulation', 'ban',
                'hack', 'scam', 'risk', 'concern', 'warning', 'drop'
            ]

            # Count keyword occurrences
            bullish_count = sum(1 for keyword in bullish_keywords if keyword in content_lower)
            bearish_count = sum(1 for keyword in bearish_keywords if keyword in content_lower)

            # Calculate sentiment score
            total_keywords = bullish_count + bearish_count
            if total_keywords == 0:
                sentiment_score = 0.0
                overall_sentiment = SentimentType.NEUTRAL
            else:
                sentiment_score = (bullish_count - bearish_count) / total_keywords

                if sentiment_score > 0.6:
                    overall_sentiment = SentimentType.VERY_BULLISH
                elif sentiment_score > 0.2:
                    overall_sentiment = SentimentType.BULLISH
                elif sentiment_score > -0.2:
                    overall_sentiment = SentimentType.NEUTRAL
                elif sentiment_score > -0.6:
                    overall_sentiment = SentimentType.BEARISH
                else:
                    overall_sentiment = SentimentType.VERY_BEARISH

            return SentimentAnalysis(
                symbol=symbol,
                overall_sentiment=overall_sentiment,
                sentiment_score=sentiment_score,
                confidence=0.6,  # Lower confidence for fallback method
                news_count=len(news_content.split('Article ')),
                bullish_signals=[f"Found {bullish_count} bullish indicators"],
                bearish_signals=[f"Found {bearish_count} bearish indicators"],
                key_themes=["Keyword-based analysis"],
                analysis_timestamp=datetime.utcnow()
            )

        except Exception as e:
            logger.error(f"Error in fallback sentiment analysis: {e}")
            return SentimentAnalysis(
                symbol=symbol,
                overall_sentiment=SentimentType.NEUTRAL,
                sentiment_score=0.0,
                confidence=0.0,
                news_count=0,
                bullish_signals=[],
                bearish_signals=[],
                key_themes=[],
                analysis_timestamp=datetime.utcnow()
            )


    async def generate_comprehensive_analysis(
        self,
        symbol: str,
        timeframe: str = "1h",
        include_technical: bool = True
    ) -> AIAnalysisResult:
        """Generate comprehensive AI analysis combining news, sentiment, and technical data"""
        try:
            logger.info(f"Generating comprehensive AI analysis for {symbol}")

            # Check cache
            cache_key = f"ai_analysis:{symbol}:{timeframe}"
            cached_analysis = await cache_manager.get(cache_key)
            if cached_analysis:
                logger.info(f"🤖 Using cached AI analysis for {symbol}")
                return self._deserialize_ai_analysis(json.loads(cached_analysis))

            # Get news and sentiment
            news_articles = await self.get_crypto_news(symbol, limit=15)
            sentiment_analysis = await self.analyze_sentiment(symbol, news_articles)

            # Generate market insights
            market_insights = await self._generate_market_insights(symbol, news_articles, sentiment_analysis)

            # Get technical analysis if requested
            technical_data = None
            if include_technical:
                try:
                    from app.services.signal_generation_service import signal_generation_service
                    technical_signal = await signal_generation_service.generate_signal(symbol, timeframe)
                    technical_data = technical_signal
                except Exception as e:
                    logger.warning(f"Could not get technical analysis: {e}")

            # Generate AI recommendation
            ai_recommendation = await self._generate_ai_recommendation(
                symbol, sentiment_analysis, market_insights, technical_data
            )

            # Calculate confidence score
            confidence_score = self._calculate_analysis_confidence(
                sentiment_analysis, market_insights, technical_data
            )

            # Generate risk assessment
            risk_assessment = self._generate_risk_assessment(
                sentiment_analysis, market_insights, technical_data
            )

            # Extract key factors
            key_factors = self._extract_key_factors(
                sentiment_analysis, market_insights, technical_data
            )

            # Create comprehensive result
            analysis_result = AIAnalysisResult(
                symbol=symbol,
                timeframe=timeframe,
                sentiment_analysis=sentiment_analysis,
                market_insights=market_insights,
                ai_recommendation=ai_recommendation,
                confidence_score=confidence_score,
                risk_assessment=risk_assessment,
                key_factors=key_factors,
                generated_at=datetime.now(),
                valid_until=datetime.now() + timedelta(hours=2)
            )

            # Cache results
            await cache_manager.set(
                cache_key,
                json.dumps(self._serialize_ai_analysis(analysis_result)),
                ttl=3600  # 1 hour
            )

            logger.info(f"🤖 Comprehensive AI analysis completed for {symbol}")
            return analysis_result

        except Exception as e:
            logger.error(f"❌ Error generating comprehensive analysis for {symbol}: {e}")
            # Return minimal analysis
            return AIAnalysisResult(
                symbol=symbol,
                timeframe=timeframe,
                sentiment_analysis=SentimentAnalysis(
                    symbol=symbol,
                    overall_sentiment=SentimentType.NEUTRAL,
                    sentiment_score=0.0,
                    confidence=0.0,
                    news_count=0,
                    bullish_signals=[],
                    bearish_signals=[],
                    key_themes=[],
                    analysis_timestamp=datetime.now()
                ),
                market_insights=[],
                ai_recommendation="Unable to generate recommendation due to analysis error",
                confidence_score=0.0,
                risk_assessment="High - Analysis incomplete",
                key_factors=["Analysis error occurred"],
                generated_at=datetime.now(),
                valid_until=datetime.now() + timedelta(minutes=5)
            )

    async def _generate_market_insights(
        self,
        symbol: str,
        news_articles: List[NewsArticle],
        sentiment_analysis: SentimentAnalysis
    ) -> List[AIMarketInsight]:
        """Generate AI-powered market insights"""
        try:
            insights = []

            # News-based insights
            if news_articles:
                news_insight = AIMarketInsight(
                    symbol=symbol,
                    insight_type="news",
                    title=f"News Analysis for {symbol}",
                    description=f"Analyzed {len(news_articles)} recent news articles. "
                               f"Key themes: {', '.join(sentiment_analysis.key_themes[:3])}",
                    confidence=0.7,
                    impact_score=sentiment_analysis.sentiment_score * 0.5,
                    time_horizon="short",
                    supporting_evidence=[article.title for article in news_articles[:3]],
                    generated_at=datetime.now()
                )
                insights.append(news_insight)

            # Sentiment-based insights
            sentiment_insight = AIMarketInsight(
                symbol=symbol,
                insight_type="sentiment",
                title=f"Market Sentiment: {sentiment_analysis.overall_sentiment.value.title()}",
                description=f"Overall market sentiment is {sentiment_analysis.overall_sentiment.value} "
                           f"with confidence {sentiment_analysis.confidence:.2f}",
                confidence=sentiment_analysis.confidence,
                impact_score=sentiment_analysis.sentiment_score,
                time_horizon="short",
                supporting_evidence=sentiment_analysis.bullish_signals + sentiment_analysis.bearish_signals,
                generated_at=datetime.now()
            )
            insights.append(sentiment_insight)

            return insights

        except Exception as e:
            logger.error(f"Error generating market insights: {e}")
            return []

    async def _generate_ai_recommendation(
        self,
        symbol: str,
        sentiment: SentimentAnalysis,
        insights: List[AIMarketInsight],
        technical_data: Any = None
    ) -> str:
        """Generate AI-powered trading recommendation"""
        try:
            if not self.openai_client:
                return self._fallback_recommendation(symbol, sentiment, insights, technical_data)

            # Prepare context for AI
            context = f"""
            Symbol: {symbol}
            Sentiment: {sentiment.overall_sentiment.value} (score: {sentiment.sentiment_score:.2f})
            Confidence: {sentiment.confidence:.2f}
            News Count: {sentiment.news_count}

            Bullish Signals: {', '.join(sentiment.bullish_signals)}
            Bearish Signals: {', '.join(sentiment.bearish_signals)}
            Key Themes: {', '.join(sentiment.key_themes)}

            Market Insights: {len(insights)} insights generated
            """

            if technical_data:
                context += f"""
                Technical Signal: {technical_data.signal_type.value}
                Technical Strength: {technical_data.signal_strength.value}
                Technical Score: {technical_data.overall_score:.2f}
                """

            # Use optimized trading recommendation prompts
            prompt = AIPromptTemplates.get_trading_recommendation_prompt(symbol, context)
            system_prompt = AIPromptTemplates.get_trading_recommendation_system_prompt()
            model_config = AIModelConfig.get_model_config("trading_recommendation")

            response = await self.openai_client.chat.completions.create(
                model=model_config["model"],
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": prompt}
                ],
                temperature=model_config["temperature"],
                max_tokens=model_config["max_tokens"],
                top_p=model_config["top_p"],
                frequency_penalty=model_config["frequency_penalty"]
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            logger.error(f"Error generating AI recommendation: {e}")
            return self._fallback_recommendation(symbol, sentiment, insights, technical_data)

    def _fallback_recommendation(
        self,
        symbol: str,
        sentiment: SentimentAnalysis,
        insights: List[AIMarketInsight],
        technical_data: Any = None
    ) -> str:
        """Fallback recommendation when AI is not available"""
        try:
            # Simple rule-based recommendation
            sentiment_score = sentiment.sentiment_score
            confidence = sentiment.confidence

            if sentiment_score > 0.6 and confidence > 0.7:
                recommendation = "Strong Buy"
            elif sentiment_score > 0.3 and confidence > 0.6:
                recommendation = "Buy"
            elif sentiment_score > -0.3:
                recommendation = "Hold"
            elif sentiment_score > -0.6:
                recommendation = "Sell"
            else:
                recommendation = "Strong Sell"

            reasoning = f"Based on sentiment analysis showing {sentiment.overall_sentiment.value} sentiment "
            reasoning += f"with {confidence:.1%} confidence. "

            if technical_data:
                reasoning += f"Technical analysis suggests {technical_data.signal_type.value}. "

            reasoning += f"Key factors: {', '.join(sentiment.key_themes[:2])}"

            return f"{recommendation}: {reasoning}"

        except Exception as e:
            logger.error(f"Error in fallback recommendation: {e}")
            return f"Hold: Unable to generate recommendation for {symbol} due to analysis error."


# Helper methods for serialization
    def _serialize_sentiment_analysis(self, sentiment: SentimentAnalysis) -> Dict[str, Any]:
        """Serialize sentiment analysis for caching"""
        return {
            'symbol': sentiment.symbol,
            'overall_sentiment': sentiment.overall_sentiment.value,
            'sentiment_score': sentiment.sentiment_score,
            'confidence': sentiment.confidence,
            'news_count': sentiment.news_count,
            'bullish_signals': sentiment.bullish_signals,
            'bearish_signals': sentiment.bearish_signals,
            'key_themes': sentiment.key_themes,
            'analysis_timestamp': sentiment.analysis_timestamp.isoformat()
        }

    def _deserialize_sentiment_analysis(self, data: Dict[str, Any]) -> SentimentAnalysis:
        """Deserialize sentiment analysis from cache"""
        return SentimentAnalysis(
            symbol=data['symbol'],
            overall_sentiment=SentimentType(data['overall_sentiment']),
            sentiment_score=data['sentiment_score'],
            confidence=data['confidence'],
            news_count=data['news_count'],
            bullish_signals=data['bullish_signals'],
            bearish_signals=data['bearish_signals'],
            key_themes=data['key_themes'],
            analysis_timestamp=datetime.fromisoformat(data['analysis_timestamp'])
        )


# Global service instance
ai_analysis_service = AIAnalysisService()

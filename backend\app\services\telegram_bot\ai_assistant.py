"""
AI Assistant for <PERSON><PERSON>ram Bot
Author: inkbytefo

OpenAI GPT-4 powered AI assistant with function calling for natural language system control.
"""

import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

import openai
from openai import AsyncOpenA<PERSON>

from app.core.config import settings
from app.core.ai_prompts import AI<PERSON>romptTemplates, AIModelConfig
from .function_registry import SystemFunctionRegistry

logger = logging.getLogger(__name__)


class AIAssistant:
    """
    AI Assistant powered by OpenAI GPT-4 with function calling capabilities.
    Processes natural language commands and executes system functions.
    """
    
    def __init__(self):
        self.client: Optional[AsyncOpenAI] = None
        self.function_registry = SystemFunctionRegistry()
        # Use more capable model for better function calling and analysis
        self.model = getattr(settings, 'OPENAI_MODEL', 'gpt-4-turbo-preview')
        self.system_prompt = self._create_system_prompt()
        
    async def initialize(self) -> None:
        """Initialize the AI assistant."""
        try:
            # Initialize OpenAI client
            self.client = AsyncOpenAI(
                api_key=settings.OPENAI_API_KEY
            )
            
            # Initialize function registry
            await self.function_registry.initialize()
            
            logger.info("AI Assistant initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize AI Assistant: {e}")
            raise
    
    def _create_system_prompt(self) -> str:
        """Create the system prompt for the AI assistant using best practices."""
        return AIPromptTemplates.get_telegram_assistant_system_prompt()
    
    async def process_message(
        self, 
        user_id: int, 
        message: str, 
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Process user message with AI and execute system functions.
        
        Args:
            user_id: Telegram user ID
            message: User's natural language message
            context: Additional context (chat_id, etc.)
            
        Returns:
            Dict with response text and optional actions
        """
        if not self.client:
            raise RuntimeError("AI Assistant not initialized")
        
        try:
            # Prepare conversation messages
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": message}
            ]
            
            # Get available functions
            functions = await self.function_registry.get_function_definitions()
            
            # Use optimized model configuration for telegram assistant
            model_config = AIModelConfig.get_model_config("telegram_assistant")

            # Call OpenAI with optimized parameters for function calling
            response = await self.client.chat.completions.create(
                model=model_config["model"],
                messages=messages,
                functions=functions if functions else None,
                function_call="auto" if functions else None,
                temperature=model_config["temperature"],
                max_tokens=model_config["max_tokens"],
                top_p=model_config["top_p"],
                frequency_penalty=model_config["frequency_penalty"]
            )
            
            # Process response
            return await self._process_ai_response(
                response, user_id, context or {}
            )
            
        except Exception as e:
            logger.error(f"Error processing AI message: {e}")
            return {
                "text": "🚫 AI asistan şu anda kullanılamıyor. Lütfen daha sonra tekrar deneyin.",
                "actions": []
            }
    
    async def _process_ai_response(
        self, 
        response, 
        user_id: int, 
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process OpenAI response and execute functions if needed."""
        message = response.choices[0].message
        actions = []
        
        # Check if AI wants to call a function
        if hasattr(message, 'function_call') and message.function_call:
            function_name = message.function_call.name
            function_args = json.loads(message.function_call.arguments)
            
            # Execute the function
            function_result = await self.function_registry.execute_function(
                function_name=function_name,
                arguments=function_args,
                user_id=user_id,
                context=context
            )
            
            # Generate final response based on function result
            final_response = await self._generate_final_response(
                original_message=message.content or "",
                function_name=function_name,
                function_result=function_result
            )
            
            # Extract any actions from function result
            if isinstance(function_result, dict) and "actions" in function_result:
                actions = function_result["actions"]
            
            return {
                "text": final_response,
                "actions": actions
            }
        
        # No function call, return AI's direct response
        return {
            "text": message.content or "Anlayamadım, lütfen tekrar deneyin.",
            "actions": []
        }
    
    async def _generate_final_response(
        self, 
        original_message: str,
        function_name: str, 
        function_result: Any
    ) -> str:
        """Generate final user-friendly response based on function result."""
        try:
            # Use optimized response formatting prompt
            format_prompt = AIPromptTemplates.get_response_formatting_prompt(
                function_name,
                json.dumps(function_result, indent=2, ensure_ascii=False)
            )
            model_config = AIModelConfig.get_model_config("response_formatting")

            response = await self.client.chat.completions.create(
                model=model_config["model"],
                messages=[
                    {"role": "system", "content": self.system_prompt},
                    {"role": "user", "content": format_prompt}
                ],
                temperature=model_config["temperature"],
                max_tokens=model_config["max_tokens"],
                top_p=model_config["top_p"]
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"Error generating final response: {e}")
            
            # Fallback response
            if isinstance(function_result, dict):
                if "error" in function_result:
                    return f"❌ **Hata**: {function_result['error']}"
                elif "message" in function_result:
                    return f"✅ {function_result['message']}"
            
            return "✅ İşlem tamamlandı."
    
    async def get_conversation_context(self, user_id: int) -> List[Dict[str, str]]:
        """Get conversation context for user (for future implementation)."""
        # TODO: Implement conversation history storage
        return []
    
    async def clear_conversation_context(self, user_id: int) -> None:
        """Clear conversation context for user."""
        # TODO: Implement conversation history clearing
        pass

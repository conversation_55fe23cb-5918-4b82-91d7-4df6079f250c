# Monitoring & Observability Setup Guide

## Overview

This guide explains how to set up comprehensive monitoring and observability for the Crypto Trading Bot using Prometheus, Grafana, and Alertmanager.

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Trading Bot   │───▶│   Prometheus    │───▶│    Graf<PERSON>      │
│  (FastAPI App)  │    │  (Metrics DB)   │    │ (Visualization) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       ▼                       │
         │              ┌─────────────────┐              │
         └─────────────▶│  Alertmanager   │◀─────────────┘
                        │ (Notifications) │
                        └─────────────────┘
```

## Components

### 1. Prometheus Metrics Service
- **File**: `app/services/metrics_service.py`
- **Purpose**: Collects and exposes metrics in Prometheus format
- **Metrics Types**: Counter, Gauge, Histogram
- **Categories**: Trading, System, API, Risk Management

### 2. Real-time Alerting Service
- **File**: `app/services/alerting_service.py`
- **Purpose**: Real-time monitoring and alerting based on metric thresholds
- **Features**: Rule management, alert history, acknowledgment, suppression

### 3. Metrics API Endpoints
- **File**: `app/api/v1/metrics.py`
- **Endpoints**:
  - `/api/v1/metrics/prometheus` - Prometheus metrics export
  - `/api/v1/metrics/summary` - Metrics summary
  - `/api/v1/metrics/alerts/*` - Alert management

## Quick Start

### 1. Install Dependencies

```bash
pip install psutil==5.9.6
```

### 2. Start Monitoring Services

```python
from app.services.metrics_service import metrics_service
from app.services.alerting_service import alerting_service

# Start metrics collection
await metrics_service.start_monitoring()

# Start alerting
await alerting_service.start_monitoring()
```

### 3. Test the System

```bash
# Test metrics collection
python test_metrics.py

# Test alerting system
python test_alerting.py
```

## Docker Setup

### 1. Start Monitoring Stack

```bash
# Start Prometheus, Grafana, and Alertmanager
docker-compose -f docker-compose.monitoring.yml up -d

# Start with specific profiles
docker-compose -f docker-compose.monitoring.yml --profile postgres --profile redis up -d
```

### 2. Access Services

- **Grafana**: http://localhost:3000 (admin/admin123)
- **Prometheus**: http://localhost:9090
- **Alertmanager**: http://localhost:9093

## Configuration

### Prometheus Configuration
- **File**: `prometheus/prometheus.yml`
- **Scrape Interval**: 30 seconds
- **Retention**: 30 days / 10GB
- **Targets**: Trading bot, Node exporter, Database exporters

### Grafana Dashboard
- **File**: `grafana/dashboards/trading_bot_dashboard.json`
- **Panels**: Portfolio value, drawdown, system resources, API performance
- **Auto-import**: Configured via provisioning

### Alert Rules
- **File**: `prometheus/alert_rules.yml`
- **Categories**: System, Trading, Risk Management, API Performance
- **Thresholds**: Configurable per environment

## Available Metrics

### Trading Metrics
- `portfolio_value_usd` - Portfolio value in USD
- `portfolio_drawdown_percent` - Current drawdown percentage
- `trades_total` - Total number of trades
- `open_positions_count` - Number of open positions
- `circuit_breaker_state` - Circuit breaker status

### System Metrics
- `system_cpu_usage_percent` - CPU usage percentage
- `system_memory_usage_percent` - Memory usage percentage
- `system_disk_usage_percent` - Disk usage percentage

### API Metrics
- `api_requests_total` - Total API requests
- `api_request_duration_seconds` - Request duration histogram
- `market_data_latency_seconds` - Market data latency

### Risk Management Metrics
- `risk_alerts_total` - Total risk alerts
- `signals_generated_total` - Total signals generated
- `backtests_total` - Total backtests completed

## Alert Rules

### System Alerts
- **High CPU Usage**: >85% for 2 minutes
- **Critical CPU Usage**: >95% for 1 minute
- **High Memory Usage**: >90% for 3 minutes

### Trading Alerts
- **High Drawdown**: >15% for 1 minute
- **Critical Drawdown**: >25% for 30 seconds
- **Circuit Breaker**: Immediate alert when triggered

### API Alerts
- **High Error Rate**: >10% for 2 minutes
- **Slow Response**: >2 seconds (95th percentile) for 3 minutes

## Notification Channels

### Email Notifications
- **Critical Alerts**: <EMAIL>
- **Trading Alerts**: <EMAIL>
- **System Alerts**: <EMAIL>
- **Risk Alerts**: <EMAIL>

### Slack Integration
- **Channel**: #trading-alerts
- **Webhook**: Configure `SLACK_WEBHOOK_URL` environment variable

### Webhook Integration
- **Endpoint**: `/api/v1/webhooks/alerts`
- **Authentication**: Basic auth with alertmanager credentials

## Environment Variables

```bash
# Slack integration
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/...

# Database monitoring
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=trading_bot

# Redis monitoring
REDIS_PASSWORD=redis_password
```

## Troubleshooting

### Common Issues

1. **Metrics not appearing in Prometheus**
   - Check if trading bot is running
   - Verify `/api/v1/metrics/prometheus` endpoint is accessible
   - Check Prometheus logs for scrape errors

2. **Alerts not firing**
   - Verify alerting service is started
   - Check alert rule syntax in `alert_rules.yml`
   - Ensure metrics have data

3. **Grafana dashboard empty**
   - Verify Prometheus datasource connection
   - Check if metrics are being collected
   - Import dashboard manually if auto-provisioning fails

### Logs and Debugging

```bash
# Check service logs
docker-compose -f docker-compose.monitoring.yml logs prometheus
docker-compose -f docker-compose.monitoring.yml logs grafana
docker-compose -f docker-compose.monitoring.yml logs alertmanager

# Check trading bot metrics endpoint
curl http://localhost:8000/api/v1/metrics/prometheus

# Check Prometheus targets
curl http://localhost:9090/api/v1/targets
```

## Performance Considerations

### Metrics Collection
- **Collection Interval**: 30 seconds (configurable)
- **Retention**: Metrics stored for 30 days
- **Memory Usage**: ~100MB for metrics service
- **Storage**: ~1GB per month for typical usage

### Alerting Performance
- **Evaluation Interval**: 30 seconds
- **Rule Complexity**: Keep rules simple for fast evaluation
- **Alert Volume**: Monitor alert frequency to avoid spam

## Security

### Authentication
- **Prometheus**: Basic auth for scraping endpoint
- **Grafana**: Admin credentials (change default password)
- **Alertmanager**: Webhook authentication

### Network Security
- **Internal Network**: Use Docker network for service communication
- **Firewall**: Restrict external access to monitoring ports
- **TLS**: Enable HTTPS for production deployments

## Maintenance

### Regular Tasks
- **Backup**: Export Grafana dashboards and Prometheus data
- **Updates**: Keep monitoring stack components updated
- **Cleanup**: Remove old alert history and unused metrics

### Monitoring the Monitors
- **Prometheus Health**: Monitor Prometheus itself
- **Disk Space**: Monitor storage usage for metrics data
- **Alert Fatigue**: Review and tune alert thresholds regularly

## Advanced Features

### Custom Metrics
```python
# Add custom metrics in your code
from app.services.metrics_service import metrics_service

# Record custom events
metrics_service.record_custom_metric("my_metric", value, labels={"type": "custom"})
```

### Custom Alert Rules
```yaml
# Add to prometheus/alert_rules.yml
- alert: CustomAlert
  expr: my_metric > 100
  for: 5m
  labels:
    severity: warning
  annotations:
    summary: "Custom metric threshold exceeded"
```

### Dashboard Customization
- Import additional panels from Grafana community
- Create custom queries for specific business metrics
- Set up automated reporting and snapshots

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review service logs
3. Test individual components
4. Consult Prometheus/Grafana documentation

---

**Author**: inkbytefo  
**Last Updated**: 2024  
**Version**: 1.0

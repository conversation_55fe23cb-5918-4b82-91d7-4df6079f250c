"""
Walk-Forward Analysis API endpoints
Author: inkbytefo
"""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from datetime import datetime
import logging

from app.database import get_db
from app.models.backtesting import BacktestStrategy
from app.services.walk_forward_analysis import walk_forward_service, WalkForwardResult
from app.services.optimization_service import ParameterRange
from app.core.auth import get_current_user
from app.models.user import User
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)

router = APIRouter()

# Pydantic Models for API
class ParameterRangeRequest(BaseModel):
    min_value: float = Field(..., description="Minimum value for parameter")
    max_value: float = Field(..., description="Maximum value for parameter")
    step: float = Field(..., description="Step size for parameter")

class WalkForwardRequest(BaseModel):
    strategy_id: int = Field(..., description="Strategy ID to analyze")
    symbol: str = Field(..., description="Trading symbol")
    timeframe: str = Field(..., description="Timeframe for analysis")
    start_date: datetime = Field(..., description="Analysis start date")
    end_date: datetime = Field(..., description="Analysis end date")
    parameter_ranges: Dict[str, ParameterRangeRequest] = Field(..., description="Parameter ranges to optimize")
    optimization_metric: str = Field(default="sharpe_ratio", description="Metric to optimize")
    in_sample_ratio: float = Field(default=0.7, description="Ratio of data for in-sample (0.7 = 70%)")
    step_size_months: int = Field(default=3, description="How many months to step forward each period")
    min_out_sample_months: int = Field(default=1, description="Minimum months for out-of-sample testing")

class WalkForwardResponse(BaseModel):
    analysis_id: str = Field(..., description="Unique analysis ID")
    status: str = Field(..., description="Analysis status")
    message: str = Field(..., description="Status message")

class WalkForwardPeriodResponse(BaseModel):
    period_id: int
    in_sample_start: datetime
    in_sample_end: datetime
    out_sample_start: datetime
    out_sample_end: datetime
    optimized_parameters: Dict[str, Any]
    in_sample_return: Optional[float] = None
    out_sample_return: Optional[float] = None
    in_sample_sharpe: Optional[float] = None
    out_sample_sharpe: Optional[float] = None

class WalkForwardResultResponse(BaseModel):
    analysis_id: str
    strategy_id: int
    symbol: str
    timeframe: str
    total_periods: int
    periods: List[WalkForwardPeriodResponse]
    
    # Aggregated metrics
    avg_in_sample_return: float
    avg_out_sample_return: float
    in_sample_sharpe: float
    out_sample_sharpe: float
    
    # Robustness metrics
    consistency_score: float
    overfitting_score: float
    stability_score: float
    
    # Summary
    is_robust: bool
    robustness_rating: str
    recommendations: List[str]

@router.post("/start", response_model=WalkForwardResponse)
async def start_walk_forward_analysis(
    request: WalkForwardRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Start a walk-forward analysis"""
    try:
        # Validate strategy exists and belongs to user
        strategy = db.query(BacktestStrategy).filter(
            BacktestStrategy.id == request.strategy_id,
            BacktestStrategy.user_id == current_user.id
        ).first()
        
        if not strategy:
            raise HTTPException(status_code=404, detail="Strategy not found")
        
        # Validate parameters
        if request.in_sample_ratio <= 0 or request.in_sample_ratio >= 1:
            raise HTTPException(status_code=400, detail="in_sample_ratio must be between 0 and 1")
        
        if request.step_size_months <= 0:
            raise HTTPException(status_code=400, detail="step_size_months must be positive")
        
        if request.start_date >= request.end_date:
            raise HTTPException(status_code=400, detail="start_date must be before end_date")
        
        # Convert parameter ranges
        parameter_ranges = {}
        for param_name, range_req in request.parameter_ranges.items():
            parameter_ranges[param_name] = ParameterRange(
                min_value=range_req.min_value,
                max_value=range_req.max_value,
                step=range_req.step
            )
        
        # Generate analysis ID
        analysis_id = f"wf_{request.strategy_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Start analysis in background
        background_tasks.add_task(
            _run_walk_forward_analysis,
            analysis_id=analysis_id,
            strategy_id=request.strategy_id,
            symbol=request.symbol,
            timeframe=request.timeframe,
            start_date=request.start_date,
            end_date=request.end_date,
            parameter_ranges=parameter_ranges,
            optimization_metric=request.optimization_metric,
            in_sample_ratio=request.in_sample_ratio,
            step_size_months=request.step_size_months,
            min_out_sample_months=request.min_out_sample_months,
            user_id=current_user.id
        )
        
        return WalkForwardResponse(
            analysis_id=analysis_id,
            status="started",
            message="Walk-forward analysis started successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting walk-forward analysis: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/status/{analysis_id}")
async def get_analysis_status(
    analysis_id: str,
    current_user: User = Depends(get_current_user)
):
    """Get walk-forward analysis status"""
    try:
        # In a real implementation, you'd check the status from database or cache
        # For now, return a placeholder
        return {
            "analysis_id": analysis_id,
            "status": "running",
            "progress": 50,
            "message": "Analysis in progress",
            "estimated_completion": None
        }
    except Exception as e:
        logger.error(f"Error getting analysis status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/result/{analysis_id}", response_model=WalkForwardResultResponse)
async def get_analysis_result(
    analysis_id: str,
    current_user: User = Depends(get_current_user)
):
    """Get walk-forward analysis results"""
    try:
        # In a real implementation, you'd retrieve the result from database
        # For now, return a placeholder response
        raise HTTPException(status_code=404, detail="Analysis result not found")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting analysis result: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/history")
async def get_analysis_history(
    current_user: User = Depends(get_current_user),
    limit: int = 50,
    offset: int = 0
):
    """Get walk-forward analysis history for user"""
    try:
        # In a real implementation, you'd query database for user's analysis history
        return {
            "analyses": [],
            "total": 0,
            "limit": limit,
            "offset": offset
        }
    except Exception as e:
        logger.error(f"Error getting analysis history: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/{analysis_id}")
async def cancel_analysis(
    analysis_id: str,
    current_user: User = Depends(get_current_user)
):
    """Cancel a running walk-forward analysis"""
    try:
        # In a real implementation, you'd cancel the running analysis
        return {
            "analysis_id": analysis_id,
            "status": "cancelled",
            "message": "Analysis cancelled successfully"
        }
        
    except Exception as e:
        logger.error(f"Error cancelling analysis: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/recommendations/{analysis_id}")
async def get_strategy_recommendations(
    analysis_id: str,
    current_user: User = Depends(get_current_user)
):
    """Get strategy improvement recommendations based on walk-forward analysis"""
    try:
        # In a real implementation, you'd analyze the results and provide recommendations
        return {
            "analysis_id": analysis_id,
            "recommendations": [
                {
                    "type": "parameter_adjustment",
                    "priority": "high",
                    "description": "Consider reducing RSI period from 14 to 10 for better responsiveness",
                    "expected_improvement": "5-10% better Sharpe ratio"
                },
                {
                    "type": "risk_management",
                    "priority": "medium", 
                    "description": "Implement dynamic position sizing based on volatility",
                    "expected_improvement": "Reduced drawdown periods"
                },
                {
                    "type": "market_regime",
                    "priority": "high",
                    "description": "Add market regime filter to avoid trading in sideways markets",
                    "expected_improvement": "Better consistency across different market conditions"
                }
            ],
            "overall_assessment": "Strategy shows good potential but needs refinement for production use"
        }
        
    except Exception as e:
        logger.error(f"Error getting recommendations: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Background task function
async def _run_walk_forward_analysis(
    analysis_id: str,
    strategy_id: int,
    symbol: str,
    timeframe: str,
    start_date: datetime,
    end_date: datetime,
    parameter_ranges: Dict[str, ParameterRange],
    optimization_metric: str,
    in_sample_ratio: float,
    step_size_months: int,
    min_out_sample_months: int,
    user_id: int
):
    """Background task for walk-forward analysis"""
    try:
        result = await walk_forward_service.run_walk_forward_analysis(
            strategy_id=strategy_id,
            symbol=symbol,
            timeframe=timeframe,
            start_date=start_date,
            end_date=end_date,
            parameter_ranges=parameter_ranges,
            optimization_metric=optimization_metric,
            in_sample_ratio=in_sample_ratio,
            step_size_months=step_size_months,
            min_out_sample_months=min_out_sample_months
        )
        
        # Store result with analysis_id
        # In a real implementation, you'd save this to database
        logger.info(f"Walk-forward analysis {analysis_id} completed successfully")
        
    except Exception as e:
        logger.error(f"Walk-forward analysis {analysis_id} failed: {e}")
        # In a real implementation, you'd mark the analysis as failed in database

# Prometheus Configuration for Crypto Trading Bot
# Author: inkbytefo

global:
  scrape_interval: 15s # Set the scrape interval to every 15 seconds. Default is every 1 minute.
  evaluation_interval: 15s # Evaluate rules every 15 seconds. The default is every 1 minute.
  # scrape_timeout is set to the global default (10s).

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Load rules once and periodically evaluate them according to the global 'evaluation_interval'.
rule_files:
  - "alert_rules.yml"
  # - "first_rules.yml"
  # - "second_rules.yml"

# A scrape configuration containing exactly one endpoint to scrape:
scrape_configs:
  # The job name is added as a label `job=<job_name>` to any timeseries scraped from this config.
  - job_name: "prometheus"
    # metrics_path defaults to '/metrics'
    # scheme defaults to 'http'.
    static_configs:
      - targets: ["localhost:9090"]

  # Crypto Trading Bot metrics
  - job_name: "crypto-trading-bot"
    scrape_interval: 30s
    scrape_timeout: 10s
    metrics_path: "/api/v1/metrics/prometheus"
    scheme: "http"
    static_configs:
      - targets: ["localhost:8000"]  # FastAPI server
    basic_auth:
      username: "prometheus"
      password: "prometheus_password"  # Should be configured in environment
    honor_labels: true
    honor_timestamps: true

  # System metrics (if node_exporter is running)
  - job_name: "node-exporter"
    scrape_interval: 30s
    static_configs:
      - targets: ["localhost:9100"]

  # Database metrics (if postgres_exporter is running)
  - job_name: "postgres-exporter"
    scrape_interval: 30s
    static_configs:
      - targets: ["localhost:9187"]

  # Redis metrics (if redis_exporter is running)
  - job_name: "redis-exporter"
    scrape_interval: 30s
    static_configs:
      - targets: ["localhost:9121"]

# Remote write configuration (optional - for long-term storage)
# remote_write:
#   - url: "https://prometheus-remote-write-endpoint.com/api/v1/write"
#     basic_auth:
#       username: "username"
#       password: "password"

# Remote read configuration (optional)
# remote_read:
#   - url: "https://prometheus-remote-read-endpoint.com/api/v1/read"
#     basic_auth:
#       username: "username"
#       password: "password"

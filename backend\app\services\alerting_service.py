"""
Real-time Alerting Service
Comprehensive alerting system for trading bot monitoring
Author: inkbytefo
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Callable
from enum import Enum
from dataclasses import dataclass, field
import json
from collections import defaultdict, deque

from app.services.metrics_service import metrics_service
from app.core.logging_config import performance_monitor

logger = logging.getLogger(__name__)

class AlertSeverity(Enum):
    """Alert severity levels"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class AlertStatus(Enum):
    """Alert status"""
    ACTIVE = "active"
    RESOLVED = "resolved"
    ACKNOWLEDGED = "acknowledged"
    SUPPRESSED = "suppressed"

@dataclass
class AlertRule:
    """Alert rule configuration"""
    name: str
    description: str
    metric_name: str
    condition: str  # "gt", "lt", "eq", "ne", "gte", "lte"
    threshold: float
    severity: AlertSeverity
    duration_seconds: int = 60  # Alert fires after condition is true for this duration
    cooldown_seconds: int = 300  # Minimum time between alerts
    enabled: bool = True
    labels: Dict[str, str] = field(default_factory=dict)
    
    # Advanced conditions
    comparison_metric: Optional[str] = None  # For metric-to-metric comparisons
    percentage_change: bool = False  # If true, compare percentage change
    time_window_seconds: int = 300  # Time window for percentage change calculation

@dataclass
class Alert:
    """Active alert instance"""
    rule_name: str
    severity: AlertSeverity
    status: AlertStatus
    message: str
    metric_name: str
    current_value: float
    threshold: float
    triggered_at: datetime
    resolved_at: Optional[datetime] = None
    acknowledged_at: Optional[datetime] = None
    labels: Dict[str, str] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)

class AlertingService:
    """Real-time alerting service"""
    
    def __init__(self):
        self.rules: Dict[str, AlertRule] = {}
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_history: deque = deque(maxlen=1000)
        self.alert_callbacks: List[Callable] = []
        
        # State tracking
        self.monitoring_active = False
        self.monitoring_task = None
        self.evaluation_interval = 30  # seconds
        self.rule_states: Dict[str, Dict] = defaultdict(dict)
        
        # Initialize default rules
        self._initialize_default_rules()
    
    def _initialize_default_rules(self):
        """Initialize default alerting rules"""
        
        # System health alerts
        self.add_rule(AlertRule(
            name="high_cpu_usage",
            description="System CPU usage is critically high",
            metric_name="system_cpu_usage_percent",
            condition="gt",
            threshold=85.0,
            severity=AlertSeverity.WARNING,
            duration_seconds=120,
            cooldown_seconds=300
        ))
        
        self.add_rule(AlertRule(
            name="critical_cpu_usage",
            description="System CPU usage is at critical levels",
            metric_name="system_cpu_usage_percent",
            condition="gt",
            threshold=95.0,
            severity=AlertSeverity.CRITICAL,
            duration_seconds=60,
            cooldown_seconds=180
        ))
        
        self.add_rule(AlertRule(
            name="high_memory_usage",
            description="System memory usage is critically high",
            metric_name="system_memory_usage_percent",
            condition="gt",
            threshold=90.0,
            severity=AlertSeverity.WARNING,
            duration_seconds=180,
            cooldown_seconds=300
        ))
        
        # Trading alerts
        self.add_rule(AlertRule(
            name="high_portfolio_drawdown",
            description="Portfolio drawdown exceeds safe threshold",
            metric_name="portfolio_drawdown_percent",
            condition="gt",
            threshold=15.0,
            severity=AlertSeverity.ERROR,
            duration_seconds=60,
            cooldown_seconds=600
        ))
        
        self.add_rule(AlertRule(
            name="critical_portfolio_drawdown",
            description="Portfolio drawdown at critical levels",
            metric_name="portfolio_drawdown_percent",
            condition="gt",
            threshold=25.0,
            severity=AlertSeverity.CRITICAL,
            duration_seconds=30,
            cooldown_seconds=300
        ))
        
        self.add_rule(AlertRule(
            name="circuit_breaker_open",
            description="Circuit breaker has been triggered",
            metric_name="circuit_breaker_state",
            condition="eq",
            threshold=2.0,  # 2 = open state
            severity=AlertSeverity.CRITICAL,
            duration_seconds=0,  # Immediate alert
            cooldown_seconds=60
        ))
        
        # API performance alerts
        self.add_rule(AlertRule(
            name="high_api_error_rate",
            description="API error rate is unusually high",
            metric_name="api_requests_total",
            condition="gt",
            threshold=10.0,  # More than 10 errors per evaluation period
            severity=AlertSeverity.WARNING,
            duration_seconds=120,
            cooldown_seconds=300,
            percentage_change=True,
            time_window_seconds=300
        ))
        
        # Market data alerts
        self.add_rule(AlertRule(
            name="high_market_data_latency",
            description="Market data feed latency is too high",
            metric_name="market_data_latency_seconds",
            condition="gt",
            threshold=1.0,  # 1 second latency
            severity=AlertSeverity.WARNING,
            duration_seconds=90,
            cooldown_seconds=180
        ))
        
        logger.info(f"Initialized {len(self.rules)} default alert rules")
    
    def add_rule(self, rule: AlertRule):
        """Add an alert rule"""
        self.rules[rule.name] = rule
        self.rule_states[rule.name] = {
            "condition_start_time": None,
            "last_alert_time": None,
            "consecutive_violations": 0
        }
        logger.info(f"Added alert rule: {rule.name}")
    
    def remove_rule(self, rule_name: str):
        """Remove an alert rule"""
        if rule_name in self.rules:
            del self.rules[rule_name]
            del self.rule_states[rule_name]
            logger.info(f"Removed alert rule: {rule_name}")
    
    def enable_rule(self, rule_name: str):
        """Enable an alert rule"""
        if rule_name in self.rules:
            self.rules[rule_name].enabled = True
            logger.info(f"Enabled alert rule: {rule_name}")
    
    def disable_rule(self, rule_name: str):
        """Disable an alert rule"""
        if rule_name in self.rules:
            self.rules[rule_name].enabled = False
            logger.info(f"Disabled alert rule: {rule_name}")
    
    def register_callback(self, callback: Callable):
        """Register callback for alert notifications"""
        self.alert_callbacks.append(callback)
    
    async def start_monitoring(self):
        """Start alert monitoring"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("Alert monitoring started")
    
    async def stop_monitoring(self):
        """Stop alert monitoring"""
        self.monitoring_active = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        logger.info("Alert monitoring stopped")
    
    async def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.monitoring_active:
            try:
                await self._evaluate_rules()
                await asyncio.sleep(self.evaluation_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in alert monitoring loop: {e}")
                await asyncio.sleep(5)
    
    @performance_monitor("alert_rule_evaluation")
    async def _evaluate_rules(self):
        """Evaluate all alert rules"""
        current_time = datetime.utcnow()
        metrics_summary = metrics_service.get_metrics_summary()
        
        for rule_name, rule in self.rules.items():
            if not rule.enabled:
                continue
            
            try:
                await self._evaluate_rule(rule, metrics_summary, current_time)
            except Exception as e:
                logger.error(f"Error evaluating rule {rule_name}: {e}")
    
    async def _evaluate_rule(self, rule: AlertRule, metrics_summary: Dict, current_time: datetime):
        """Evaluate a single alert rule"""
        rule_state = self.rule_states[rule.name]
        
        # Get current metric value
        metric_data = metrics_summary.get(rule.metric_name)
        if not metric_data:
            return
        
        current_value = metric_data["value"]
        
        # Handle percentage change calculations
        if rule.percentage_change:
            # This would require historical data analysis
            # For now, we'll use the current value
            pass
        
        # Evaluate condition
        condition_met = self._evaluate_condition(rule, current_value)
        
        if condition_met:
            # Condition is met
            if rule_state["condition_start_time"] is None:
                rule_state["condition_start_time"] = current_time
                rule_state["consecutive_violations"] = 1
            else:
                rule_state["consecutive_violations"] += 1
            
            # Check if duration threshold is met
            duration_met = (current_time - rule_state["condition_start_time"]).total_seconds() >= rule.duration_seconds
            
            # Check cooldown period
            last_alert = rule_state["last_alert_time"]
            cooldown_passed = (
                last_alert is None or 
                (current_time - last_alert).total_seconds() >= rule.cooldown_seconds
            )
            
            if duration_met and cooldown_passed:
                await self._trigger_alert(rule, current_value, current_time)
                rule_state["last_alert_time"] = current_time
        
        else:
            # Condition is not met - reset state
            if rule_state["condition_start_time"] is not None:
                # Check if we should resolve an active alert
                if rule.name in self.active_alerts:
                    await self._resolve_alert(rule.name, current_time)
            
            rule_state["condition_start_time"] = None
            rule_state["consecutive_violations"] = 0
    
    def _evaluate_condition(self, rule: AlertRule, current_value: float) -> bool:
        """Evaluate if rule condition is met"""
        threshold = rule.threshold
        
        if rule.condition == "gt":
            return current_value > threshold
        elif rule.condition == "gte":
            return current_value >= threshold
        elif rule.condition == "lt":
            return current_value < threshold
        elif rule.condition == "lte":
            return current_value <= threshold
        elif rule.condition == "eq":
            return abs(current_value - threshold) < 0.001  # Float comparison
        elif rule.condition == "ne":
            return abs(current_value - threshold) >= 0.001
        
        return False
    
    async def _trigger_alert(self, rule: AlertRule, current_value: float, triggered_at: datetime):
        """Trigger an alert"""
        alert = Alert(
            rule_name=rule.name,
            severity=rule.severity,
            status=AlertStatus.ACTIVE,
            message=f"{rule.description} (Current: {current_value}, Threshold: {rule.threshold})",
            metric_name=rule.metric_name,
            current_value=current_value,
            threshold=rule.threshold,
            triggered_at=triggered_at,
            labels=rule.labels.copy(),
            metadata={
                "condition": rule.condition,
                "duration_seconds": rule.duration_seconds,
                "consecutive_violations": self.rule_states[rule.name]["consecutive_violations"]
            }
        )
        
        self.active_alerts[rule.name] = alert
        self.alert_history.append(alert)
        
        logger.warning(f"Alert triggered: {rule.name} - {alert.message}")
        
        # Record alert metric
        metrics_service.record_risk_alert(rule.name, rule.severity.value)
        
        # Execute callbacks
        for callback in self.alert_callbacks:
            try:
                await callback(alert)
            except Exception as e:
                logger.error(f"Error executing alert callback: {e}")
    
    async def _resolve_alert(self, rule_name: str, resolved_at: datetime):
        """Resolve an active alert"""
        if rule_name in self.active_alerts:
            alert = self.active_alerts[rule_name]
            alert.status = AlertStatus.RESOLVED
            alert.resolved_at = resolved_at
            
            del self.active_alerts[rule_name]
            
            logger.info(f"Alert resolved: {rule_name}")
    
    async def acknowledge_alert(self, rule_name: str, acknowledged_by: str = "system"):
        """Acknowledge an alert"""
        if rule_name in self.active_alerts:
            alert = self.active_alerts[rule_name]
            alert.status = AlertStatus.ACKNOWLEDGED
            alert.acknowledged_at = datetime.utcnow()
            alert.metadata["acknowledged_by"] = acknowledged_by
            
            logger.info(f"Alert acknowledged: {rule_name} by {acknowledged_by}")
    
    async def suppress_alert(self, rule_name: str, duration_minutes: int = 60):
        """Suppress an alert for a specified duration"""
        if rule_name in self.active_alerts:
            alert = self.active_alerts[rule_name]
            alert.status = AlertStatus.SUPPRESSED
            alert.metadata["suppressed_until"] = datetime.utcnow() + timedelta(minutes=duration_minutes)
            
            logger.info(f"Alert suppressed: {rule_name} for {duration_minutes} minutes")
    
    def get_active_alerts(self) -> List[Alert]:
        """Get all active alerts"""
        return list(self.active_alerts.values())
    
    def get_alert_history(self, limit: int = 100) -> List[Alert]:
        """Get alert history"""
        return list(self.alert_history)[-limit:]
    
    def get_alert_summary(self) -> Dict[str, Any]:
        """Get alert summary statistics"""
        active_by_severity = defaultdict(int)
        for alert in self.active_alerts.values():
            active_by_severity[alert.severity.value] += 1
        
        total_history = len(self.alert_history)
        resolved_count = sum(1 for alert in self.alert_history if alert.status == AlertStatus.RESOLVED)
        
        return {
            "active_alerts": len(self.active_alerts),
            "active_by_severity": dict(active_by_severity),
            "total_rules": len(self.rules),
            "enabled_rules": sum(1 for rule in self.rules.values() if rule.enabled),
            "total_historical_alerts": total_history,
            "resolution_rate": (resolved_count / total_history * 100) if total_history > 0 else 0,
            "monitoring_active": self.monitoring_active
        }

# Global alerting service instance
alerting_service = AlertingService()

"""
Circuit Breaker & Emergency Stop Service
Provides automatic portfolio protection and emergency stop mechanisms
Author: inkbytefo
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Callable
from enum import Enum
from dataclasses import dataclass, field
import json

from app.core.logging_config import performance_monitor, trading_context
from app.core.database import get_db
from app.models.trading import Portfolio, Trade
# from app.services.telegram_bot.notification_engine import notification_service

logger = logging.getLogger(__name__)

class CircuitBreakerState(Enum):
    """Circuit breaker states"""
    CLOSED = "closed"       # Normal operation
    OPEN = "open"          # Trading halted
    HALF_OPEN = "half_open" # Testing recovery

class EmergencyLevel(Enum):
    """Emergency severity levels"""
    LOW = "low"           # Minor risk increase
    MEDIUM = "medium"     # Moderate risk
    HIGH = "high"         # High risk - reduce positions
    CRITICAL = "critical" # Critical risk - halt all trading

@dataclass
class RiskThresholds:
    """Risk threshold configuration"""
    # Portfolio-level thresholds
    max_daily_loss_pct: float = 5.0      # 5% max daily loss
    max_weekly_loss_pct: float = 15.0     # 15% max weekly loss
    max_monthly_loss_pct: float = 25.0    # 25% max monthly loss
    max_drawdown_pct: float = 20.0        # 20% max drawdown
    
    # Position-level thresholds
    max_position_loss_pct: float = 10.0   # 10% max loss per position
    max_correlation_exposure: float = 0.6  # 60% max correlated exposure
    
    # Market condition thresholds
    max_volatility_percentile: float = 95.0  # 95th percentile volatility
    min_liquidity_ratio: float = 0.1         # 10% min liquidity
    
    # Trading frequency thresholds
    max_trades_per_hour: int = 10
    max_trades_per_day: int = 50
    
    # Recovery thresholds
    recovery_profit_threshold: float = 2.0   # 2% profit to consider recovery
    recovery_time_hours: int = 24            # 24 hours before retry

@dataclass
class CircuitBreakerEvent:
    """Circuit breaker trigger event"""
    timestamp: datetime
    event_type: str
    severity: EmergencyLevel
    trigger_value: float
    threshold_value: float
    description: str
    affected_symbols: List[str] = field(default_factory=list)
    recommended_actions: List[str] = field(default_factory=list)

class CircuitBreakerService:
    """Service for automatic portfolio protection and emergency stops"""
    
    def __init__(self):
        self.state = CircuitBreakerState.CLOSED
        self.thresholds = RiskThresholds()
        self.events: List[CircuitBreakerEvent] = []
        self.monitoring_active = False
        self.emergency_callbacks: List[Callable] = []
        
        # State tracking
        self.last_portfolio_value = 0.0
        self.daily_start_value = 0.0
        self.weekly_start_value = 0.0
        self.monthly_start_value = 0.0
        self.peak_portfolio_value = 0.0
        
        # Trading frequency tracking
        self.hourly_trade_count = 0
        self.daily_trade_count = 0
        self.last_trade_hour = datetime.utcnow().hour
        self.last_trade_date = datetime.utcnow().date()
    
    def configure_thresholds(self, thresholds: RiskThresholds):
        """Configure risk thresholds"""
        self.thresholds = thresholds
        logger.info(f"Circuit breaker thresholds updated: {thresholds}")
    
    def register_emergency_callback(self, callback: Callable):
        """Register callback for emergency events"""
        self.emergency_callbacks.append(callback)
    
    @performance_monitor("circuit_breaker_check")
    async def check_portfolio_risk(self, user_id: int) -> Optional[CircuitBreakerEvent]:
        """
        Check portfolio for risk threshold violations
        
        Args:
            user_id: User ID to check
            
        Returns:
            CircuitBreakerEvent if threshold violated, None otherwise
        """
        try:
            async with get_db() as db:
                # Get user's portfolio
                portfolio = db.query(Portfolio).filter(
                    Portfolio.user_id == user_id
                ).first()
                
                if not portfolio:
                    return None
                
                current_value = portfolio.total_value
                
                # Initialize tracking values if needed
                if self.last_portfolio_value == 0.0:
                    self._initialize_tracking_values(current_value)
                
                # Check daily loss
                daily_loss_pct = ((self.daily_start_value - current_value) / self.daily_start_value) * 100
                if daily_loss_pct > self.thresholds.max_daily_loss_pct:
                    return self._create_event(
                        "daily_loss_exceeded",
                        EmergencyLevel.HIGH,
                        daily_loss_pct,
                        self.thresholds.max_daily_loss_pct,
                        f"Daily loss of {daily_loss_pct:.1f}% exceeds threshold of {self.thresholds.max_daily_loss_pct}%"
                    )
                
                # Check weekly loss
                weekly_loss_pct = ((self.weekly_start_value - current_value) / self.weekly_start_value) * 100
                if weekly_loss_pct > self.thresholds.max_weekly_loss_pct:
                    return self._create_event(
                        "weekly_loss_exceeded",
                        EmergencyLevel.HIGH,
                        weekly_loss_pct,
                        self.thresholds.max_weekly_loss_pct,
                        f"Weekly loss of {weekly_loss_pct:.1f}% exceeds threshold of {self.thresholds.max_weekly_loss_pct}%"
                    )
                
                # Check monthly loss
                monthly_loss_pct = ((self.monthly_start_value - current_value) / self.monthly_start_value) * 100
                if monthly_loss_pct > self.thresholds.max_monthly_loss_pct:
                    return self._create_event(
                        "monthly_loss_exceeded",
                        EmergencyLevel.CRITICAL,
                        monthly_loss_pct,
                        self.thresholds.max_monthly_loss_pct,
                        f"Monthly loss of {monthly_loss_pct:.1f}% exceeds threshold of {self.thresholds.max_monthly_loss_pct}%"
                    )
                
                # Check drawdown
                if current_value > self.peak_portfolio_value:
                    self.peak_portfolio_value = current_value
                
                drawdown_pct = ((self.peak_portfolio_value - current_value) / self.peak_portfolio_value) * 100
                if drawdown_pct > self.thresholds.max_drawdown_pct:
                    return self._create_event(
                        "max_drawdown_exceeded",
                        EmergencyLevel.CRITICAL,
                        drawdown_pct,
                        self.thresholds.max_drawdown_pct,
                        f"Drawdown of {drawdown_pct:.1f}% exceeds threshold of {self.thresholds.max_drawdown_pct}%"
                    )
                
                # Check trading frequency
                current_hour = datetime.utcnow().hour
                current_date = datetime.utcnow().date()
                
                # Reset counters if needed
                if current_hour != self.last_trade_hour:
                    self.hourly_trade_count = 0
                    self.last_trade_hour = current_hour
                
                if current_date != self.last_trade_date:
                    self.daily_trade_count = 0
                    self.last_trade_date = current_date
                
                # Check hourly trade limit
                if self.hourly_trade_count > self.thresholds.max_trades_per_hour:
                    return self._create_event(
                        "hourly_trade_limit_exceeded",
                        EmergencyLevel.MEDIUM,
                        self.hourly_trade_count,
                        self.thresholds.max_trades_per_hour,
                        f"Hourly trade count of {self.hourly_trade_count} exceeds limit of {self.thresholds.max_trades_per_hour}"
                    )
                
                # Check daily trade limit
                if self.daily_trade_count > self.thresholds.max_trades_per_day:
                    return self._create_event(
                        "daily_trade_limit_exceeded",
                        EmergencyLevel.HIGH,
                        self.daily_trade_count,
                        self.thresholds.max_trades_per_day,
                        f"Daily trade count of {self.daily_trade_count} exceeds limit of {self.thresholds.max_trades_per_day}"
                    )
                
                # Update tracking
                self.last_portfolio_value = current_value
                
                return None
                
        except Exception as e:
            logger.error(f"Error checking portfolio risk: {e}")
            return None
    
    async def trigger_circuit_breaker(self, event: CircuitBreakerEvent, user_id: int):
        """
        Trigger circuit breaker based on event severity
        
        Args:
            event: The triggering event
            user_id: User ID affected
        """
        try:
            logger.warning(f"Circuit breaker triggered: {event.description}")
            
            # Update state based on severity
            if event.severity == EmergencyLevel.CRITICAL:
                self.state = CircuitBreakerState.OPEN
                await self._halt_all_trading(user_id)
                await self._close_all_positions(user_id)
                
            elif event.severity == EmergencyLevel.HIGH:
                self.state = CircuitBreakerState.HALF_OPEN
                await self._reduce_positions(user_id, 0.5)  # Reduce by 50%
                
            elif event.severity == EmergencyLevel.MEDIUM:
                await self._reduce_positions(user_id, 0.25)  # Reduce by 25%
            
            # Record event
            self.events.append(event)
            
            # Send notifications
            await self._send_emergency_notifications(event, user_id)
            
            # Execute emergency callbacks
            for callback in self.emergency_callbacks:
                try:
                    await callback(event, user_id)
                except Exception as e:
                    logger.error(f"Error executing emergency callback: {e}")
            
        except Exception as e:
            logger.error(f"Error triggering circuit breaker: {e}")
    
    async def emergency_stop(self, user_id: int, reason: str = "Manual emergency stop"):
        """
        Manual emergency stop - immediately halt all trading and close positions
        
        Args:
            user_id: User ID to stop
            reason: Reason for emergency stop
        """
        try:
            logger.critical(f"EMERGENCY STOP triggered for user {user_id}: {reason}")
            
            # Create critical event
            event = self._create_event(
                "emergency_stop",
                EmergencyLevel.CRITICAL,
                0.0,
                0.0,
                f"Manual emergency stop: {reason}"
            )
            
            # Trigger circuit breaker
            await self.trigger_circuit_breaker(event, user_id)
            
        except Exception as e:
            logger.error(f"Error executing emergency stop: {e}")
    
    async def check_recovery_conditions(self, user_id: int) -> bool:
        """
        Check if conditions are met to recover from circuit breaker state
        
        Args:
            user_id: User ID to check
            
        Returns:
            True if recovery conditions are met
        """
        try:
            if self.state == CircuitBreakerState.CLOSED:
                return True
            
            # Check if enough time has passed
            if self.events:
                last_event = self.events[-1]
                time_since_event = datetime.utcnow() - last_event.timestamp
                if time_since_event.total_seconds() < self.thresholds.recovery_time_hours * 3600:
                    return False
            
            # Check if portfolio has recovered
            async with get_db() as db:
                portfolio = db.query(Portfolio).filter(
                    Portfolio.user_id == user_id
                ).first()
                
                if portfolio:
                    current_value = portfolio.total_value
                    recovery_pct = ((current_value - self.last_portfolio_value) / self.last_portfolio_value) * 100
                    
                    if recovery_pct >= self.thresholds.recovery_profit_threshold:
                        return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking recovery conditions: {e}")
            return False
    
    async def reset_circuit_breaker(self, user_id: int):
        """Reset circuit breaker to normal operation"""
        try:
            logger.info(f"Resetting circuit breaker for user {user_id}")
            
            self.state = CircuitBreakerState.CLOSED
            
            # Reset tracking values
            async with get_db() as db:
                portfolio = db.query(Portfolio).filter(
                    Portfolio.user_id == user_id
                ).first()
                
                if portfolio:
                    current_value = portfolio.total_value
                    self._initialize_tracking_values(current_value)
            
            # Send notification
            # await notification_service.send_notification(
            #     user_id=user_id,
            #     title="Circuit Breaker Reset",
            #     message="Circuit breaker has been reset. Normal trading operations resumed.",
            #     notification_type="system"
            # )
            logger.info(f"Circuit breaker reset notification would be sent to user {user_id}")
            
        except Exception as e:
            logger.error(f"Error resetting circuit breaker: {e}")
    
    def _initialize_tracking_values(self, current_value: float):
        """Initialize tracking values"""
        self.last_portfolio_value = current_value
        self.daily_start_value = current_value
        self.weekly_start_value = current_value
        self.monthly_start_value = current_value
        self.peak_portfolio_value = current_value
    
    def _create_event(
        self,
        event_type: str,
        severity: EmergencyLevel,
        trigger_value: float,
        threshold_value: float,
        description: str
    ) -> CircuitBreakerEvent:
        """Create circuit breaker event"""
        
        # Generate recommended actions based on severity
        recommended_actions = []
        if severity == EmergencyLevel.CRITICAL:
            recommended_actions = [
                "Halt all trading immediately",
                "Close all open positions",
                "Review risk management strategy",
                "Contact support if needed"
            ]
        elif severity == EmergencyLevel.HIGH:
            recommended_actions = [
                "Reduce position sizes by 50%",
                "Tighten stop losses",
                "Avoid new positions temporarily",
                "Monitor market conditions closely"
            ]
        elif severity == EmergencyLevel.MEDIUM:
            recommended_actions = [
                "Reduce position sizes by 25%",
                "Review current positions",
                "Consider taking profits",
                "Monitor risk metrics"
            ]
        
        return CircuitBreakerEvent(
            timestamp=datetime.utcnow(),
            event_type=event_type,
            severity=severity,
            trigger_value=trigger_value,
            threshold_value=threshold_value,
            description=description,
            recommended_actions=recommended_actions
        )
    
    async def _halt_all_trading(self, user_id: int):
        """Halt all trading for user"""
        logger.critical(f"Halting all trading for user {user_id}")
        # Implementation would integrate with trading engine
        # For now, just log the action
    
    async def _close_all_positions(self, user_id: int):
        """Close all open positions for user"""
        logger.critical(f"Closing all positions for user {user_id}")
        # Implementation would integrate with trading engine
        # For now, just log the action
    
    async def _reduce_positions(self, user_id: int, reduction_factor: float):
        """Reduce position sizes by given factor"""
        logger.warning(f"Reducing positions by {reduction_factor*100}% for user {user_id}")
        # Implementation would integrate with trading engine
        # For now, just log the action
    
    async def _send_emergency_notifications(self, event: CircuitBreakerEvent, user_id: int):
        """Send emergency notifications"""
        try:
            # await notification_service.send_notification(
            #     user_id=user_id,
            #     title=f"🚨 Circuit Breaker Triggered - {event.severity.value.upper()}",
            #     message=f"{event.description}\n\nRecommended actions:\n" +
            #            "\n".join(f"• {action}" for action in event.recommended_actions),
            #     notification_type="emergency"
            # )
            logger.warning(f"Emergency notification would be sent to user {user_id}: {event.description}")
        except Exception as e:
            logger.error(f"Error sending emergency notification: {e}")

# Global instance
circuit_breaker_service = CircuitBreakerService()

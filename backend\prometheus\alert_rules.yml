# Prometheus Alert Rules for Crypto Trading Bot
# Author: inkbytefo

groups:
  - name: trading_bot_alerts
    rules:
      # System Health Alerts
      - alert: HighCPUUsage
        expr: system_cpu_usage_percent > 85
        for: 2m
        labels:
          severity: warning
          service: trading_bot
          category: system
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is {{ $value }}% which is above the 85% threshold for more than 2 minutes."

      - alert: CriticalCPUUsage
        expr: system_cpu_usage_percent > 95
        for: 1m
        labels:
          severity: critical
          service: trading_bot
          category: system
        annotations:
          summary: "Critical CPU usage detected"
          description: "CPU usage is {{ $value }}% which is above the 95% critical threshold."

      - alert: HighMemoryUsage
        expr: system_memory_usage_percent > 90
        for: 3m
        labels:
          severity: warning
          service: trading_bot
          category: system
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is {{ $value }}% which is above the 90% threshold for more than 3 minutes."

      - alert: HighDiskUsage
        expr: system_disk_usage_percent > 85
        for: 5m
        labels:
          severity: warning
          service: trading_bot
          category: system
        annotations:
          summary: "High disk usage detected"
          description: "Disk usage is {{ $value }}% which is above the 85% threshold."

      # Trading Performance Alerts
      - alert: HighPortfolioDrawdown
        expr: portfolio_drawdown_percent > 15
        for: 1m
        labels:
          severity: error
          service: trading_bot
          category: trading
        annotations:
          summary: "High portfolio drawdown detected"
          description: "Portfolio drawdown is {{ $value }}% for user {{ $labels.user_id }}, exceeding the 15% threshold."

      - alert: CriticalPortfolioDrawdown
        expr: portfolio_drawdown_percent > 25
        for: 30s
        labels:
          severity: critical
          service: trading_bot
          category: trading
        annotations:
          summary: "Critical portfolio drawdown detected"
          description: "Portfolio drawdown is {{ $value }}% for user {{ $labels.user_id }}, exceeding the critical 25% threshold."

      - alert: CircuitBreakerTriggered
        expr: circuit_breaker_state == 2
        for: 0s
        labels:
          severity: critical
          service: trading_bot
          category: risk_management
        annotations:
          summary: "Circuit breaker has been triggered"
          description: "Circuit breaker for user {{ $labels.user_id }} is in OPEN state, trading has been halted."

      - alert: LowPortfolioValue
        expr: portfolio_value_usd < 1000
        for: 5m
        labels:
          severity: warning
          service: trading_bot
          category: trading
        annotations:
          summary: "Portfolio value is critically low"
          description: "Portfolio value for user {{ $labels.user_id }} is ${{ $value }}, below the minimum threshold."

      # API Performance Alerts
      - alert: HighAPIErrorRate
        expr: rate(api_requests_total{status_code=~"4..|5.."}[5m]) / rate(api_requests_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          service: trading_bot
          category: api
        annotations:
          summary: "High API error rate detected"
          description: "API error rate is {{ $value | humanizePercentage }} for endpoint {{ $labels.endpoint }}."

      - alert: SlowAPIResponse
        expr: histogram_quantile(0.95, rate(api_request_duration_seconds_bucket[5m])) > 2
        for: 3m
        labels:
          severity: warning
          service: trading_bot
          category: api
        annotations:
          summary: "Slow API response times detected"
          description: "95th percentile API response time is {{ $value }}s for endpoint {{ $labels.endpoint }}."

      # Market Data Alerts
      - alert: HighMarketDataLatency
        expr: market_data_latency_seconds > 1
        for: 2m
        labels:
          severity: warning
          service: trading_bot
          category: market_data
        annotations:
          summary: "High market data latency detected"
          description: "Market data latency is {{ $value }}s for {{ $labels.exchange }} {{ $labels.symbol }}."

      - alert: MarketDataStale
        expr: time() - market_data_last_update_timestamp > 300
        for: 1m
        labels:
          severity: error
          service: trading_bot
          category: market_data
        annotations:
          summary: "Market data is stale"
          description: "Market data for {{ $labels.exchange }} {{ $labels.symbol }} hasn't been updated for {{ $value }}s."

      # Trading Activity Alerts
      - alert: NoTradingActivity
        expr: increase(trades_total[1h]) == 0
        for: 2h
        labels:
          severity: warning
          service: trading_bot
          category: trading
        annotations:
          summary: "No trading activity detected"
          description: "No trades have been executed in the last 2 hours."

      - alert: HighTradeFailureRate
        expr: rate(trades_total{status="failed"}[10m]) / rate(trades_total[10m]) > 0.2
        for: 5m
        labels:
          severity: error
          service: trading_bot
          category: trading
        annotations:
          summary: "High trade failure rate detected"
          description: "Trade failure rate is {{ $value | humanizePercentage }} for {{ $labels.exchange }}."

      # Risk Management Alerts
      - alert: FrequentRiskAlerts
        expr: rate(risk_alerts_total[10m]) > 0.1
        for: 5m
        labels:
          severity: warning
          service: trading_bot
          category: risk_management
        annotations:
          summary: "Frequent risk alerts detected"
          description: "Risk alerts are being triggered at a rate of {{ $value }}/second."

      - alert: TooManyOpenPositions
        expr: open_positions_count > 20
        for: 1m
        labels:
          severity: warning
          service: trading_bot
          category: risk_management
        annotations:
          summary: "Too many open positions"
          description: "User {{ $labels.user_id }} has {{ $value }} open positions, exceeding the recommended limit."

      # Signal Generation Alerts
      - alert: LowSignalConfidence
        expr: avg_over_time(signal_confidence[1h]) < 0.5
        for: 30m
        labels:
          severity: warning
          service: trading_bot
          category: signals
        annotations:
          summary: "Low signal confidence detected"
          description: "Average signal confidence over the last hour is {{ $value }}, below the 0.5 threshold."

      - alert: NoSignalGeneration
        expr: increase(signals_generated_total[1h]) == 0
        for: 2h
        labels:
          severity: error
          service: trading_bot
          category: signals
        annotations:
          summary: "No signals generated"
          description: "No trading signals have been generated in the last 2 hours."

      # Backtesting Alerts
      - alert: BacktestFailures
        expr: rate(backtests_total{status="failed"}[1h]) > 0.1
        for: 30m
        labels:
          severity: warning
          service: trading_bot
          category: backtesting
        annotations:
          summary: "High backtest failure rate"
          description: "Backtest failure rate is {{ $value }}/hour."

      # Service Health Alerts
      - alert: ServiceDown
        expr: up{job="crypto-trading-bot"} == 0
        for: 1m
        labels:
          severity: critical
          service: trading_bot
          category: service
        annotations:
          summary: "Trading bot service is down"
          description: "The crypto trading bot service is not responding to health checks."

      - alert: DatabaseConnectionIssues
        expr: database_connections_active / database_connections_max > 0.8
        for: 5m
        labels:
          severity: warning
          service: trading_bot
          category: database
        annotations:
          summary: "High database connection usage"
          description: "Database connection pool is {{ $value | humanizePercentage }} full."

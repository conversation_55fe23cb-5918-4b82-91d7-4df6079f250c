"""
Strategy Optimization Service
Author: inkbytefo

Advanced optimization algorithms for trading strategies:
- Grid Search optimization
- Genetic Algorithm optimization
- Bayesian optimization
- Walk-forward analysis
- Monte Carlo simulation
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass
from itertools import product
import json
import random
from pathlib import Path

from app.services.backtesting_engine import backtesting_engine
from app.services.signal_generation_service import signal_generation_service
from app.core.database import get_db
from app.models.backtesting import BacktestStrategy, BacktestRun
from app.core.logging_config import performance_monitor, trading_context, performance_logger


logger = logging.getLogger("optimization")


@dataclass
class OptimizationResult:
    """Optimization result data structure"""
    best_parameters: Dict[str, Any]
    best_performance: Dict[str, float]
    all_results: List[Dict[str, Any]]
    optimization_method: str
    total_combinations: int
    execution_time: float
    convergence_data: Optional[List[float]] = None


@dataclass
class ParameterRange:
    """Parameter range definition for optimization"""
    name: str
    min_value: Union[int, float]
    max_value: Union[int, float]
    step: Union[int, float]
    param_type: str = "float"  # "int", "float", "choice"
    choices: Optional[List[Any]] = None


class OptimizationService:
    """Advanced strategy optimization service"""
    
    def __init__(self):
        self.optimization_history = []
        self.current_optimization = None
        
        # Default parameter ranges for signal generation
        self.default_parameter_ranges = {
            "rsi_period": ParameterRange("rsi_period", 10, 20, 2, "int"),
            "rsi_oversold": ParameterRange("rsi_oversold", 20, 35, 5, "int"),
            "rsi_overbought": ParameterRange("rsi_overbought", 65, 80, 5, "int"),
            "sma_short": ParameterRange("sma_short", 10, 25, 5, "int"),
            "sma_long": ParameterRange("sma_long", 40, 100, 20, "int"),
            "ema_fast": ParameterRange("ema_fast", 8, 16, 2, "int"),
            "ema_slow": ParameterRange("ema_slow", 20, 30, 5, "int"),
            "bb_period": ParameterRange("bb_period", 15, 25, 5, "int"),
            "bb_std": ParameterRange("bb_std", 1.5, 2.5, 0.5, "float"),
            "macd_fast": ParameterRange("macd_fast", 10, 14, 2, "int"),
            "macd_slow": ParameterRange("macd_slow", 24, 30, 3, "int"),
            "macd_signal": ParameterRange("macd_signal", 8, 12, 2, "int"),
            "volume_threshold": ParameterRange("volume_threshold", 1.2, 2.0, 0.2, "float"),
            "min_confidence": ParameterRange("min_confidence", 0.5, 0.8, 0.1, "float"),
            "stop_loss_pct": ParameterRange("stop_loss_pct", 0.02, 0.08, 0.02, "float"),
            "take_profit_pct": ParameterRange("take_profit_pct", 0.08, 0.25, 0.05, "float")
        }
    
    @performance_monitor("grid_search_optimization")
    async def run_grid_search(
        self,
        strategy_id: int,
        symbol: str,
        timeframe: str,
        start_date: datetime,
        end_date: datetime,
        parameter_ranges: Dict[str, ParameterRange],
        optimization_metric: str = "sharpe_ratio",
        max_combinations: int = 1000
    ) -> OptimizationResult:
        """
        Run grid search optimization
        
        Args:
            strategy_id: Strategy to optimize
            symbol: Trading symbol
            timeframe: Timeframe for backtesting
            start_date: Start date for backtesting
            end_date: End date for backtesting
            parameter_ranges: Parameter ranges to optimize
            optimization_metric: Metric to optimize (sharpe_ratio, total_return, etc.)
            max_combinations: Maximum parameter combinations to test
        """
        
        with trading_context(
            optimization_method="grid_search",
            symbol=symbol,
            timeframe=timeframe,
            strategy_id=strategy_id
        ) as ctx:
            
            start_time = datetime.now()
            ctx.info(f"Starting grid search optimization for strategy {strategy_id}")
            
            try:
                # Generate parameter combinations
                param_combinations = self._generate_parameter_combinations(
                    parameter_ranges, max_combinations
                )
                
                ctx.info(f"Generated {len(param_combinations)} parameter combinations")
                
                # Run backtests for each combination
                all_results = []
                best_result = None
                best_metric_value = float('-inf')
                
                for i, params in enumerate(param_combinations):
                    ctx.info(f"Testing combination {i+1}/{len(param_combinations)}: {params}")
                    
                    try:
                        # Run backtest with these parameters
                        backtest_result = await self._run_backtest_with_parameters(
                            strategy_id=strategy_id,
                            symbol=symbol,
                            timeframe=timeframe,
                            start_date=start_date,
                            end_date=end_date,
                            parameters=params
                        )
                        
                        if backtest_result:
                            # Extract optimization metric
                            metric_value = self._extract_metric_value(
                                backtest_result, optimization_metric
                            )
                            
                            result_data = {
                                "parameters": params,
                                "performance": backtest_result,
                                "metric_value": metric_value
                            }
                            
                            all_results.append(result_data)
                            
                            # Check if this is the best result
                            if metric_value > best_metric_value:
                                best_metric_value = metric_value
                                best_result = result_data
                                ctx.info(f"New best result found: {optimization_metric}={metric_value:.4f}")
                        
                        # Small delay to prevent overwhelming the system
                        await asyncio.sleep(0.1)
                        
                    except Exception as e:
                        ctx.error(f"Error testing parameters {params}: {e}")
                        continue
                
                execution_time = (datetime.now() - start_time).total_seconds()
                
                if not best_result:
                    raise ValueError("No valid backtest results found")
                
                # Create optimization result
                optimization_result = OptimizationResult(
                    best_parameters=best_result["parameters"],
                    best_performance=best_result["performance"],
                    all_results=all_results,
                    optimization_method="grid_search",
                    total_combinations=len(param_combinations),
                    execution_time=execution_time
                )
                
                # Save optimization result
                await self._save_optimization_result(optimization_result, strategy_id)
                
                ctx.info(f"Grid search completed in {execution_time:.2f}s. Best {optimization_metric}: {best_metric_value:.4f}")
                
                return optimization_result
                
            except Exception as e:
                ctx.error(f"Grid search optimization failed: {e}")
                raise
    
    @performance_monitor("genetic_algorithm_optimization")
    async def run_genetic_algorithm(
        self,
        strategy_id: int,
        symbol: str,
        timeframe: str,
        start_date: datetime,
        end_date: datetime,
        parameter_ranges: Dict[str, ParameterRange],
        optimization_metric: str = "sharpe_ratio",
        population_size: int = 50,
        generations: int = 20,
        mutation_rate: float = 0.1,
        crossover_rate: float = 0.8
    ) -> OptimizationResult:
        """
        Run genetic algorithm optimization
        
        Args:
            strategy_id: Strategy to optimize
            symbol: Trading symbol
            timeframe: Timeframe for backtesting
            start_date: Start date for backtesting
            end_date: End date for backtesting
            parameter_ranges: Parameter ranges to optimize
            optimization_metric: Metric to optimize
            population_size: Size of each generation
            generations: Number of generations to evolve
            mutation_rate: Probability of mutation
            crossover_rate: Probability of crossover
        """
        
        with trading_context(
            optimization_method="genetic_algorithm",
            symbol=symbol,
            timeframe=timeframe,
            strategy_id=strategy_id,
            population_size=population_size,
            generations=generations
        ) as ctx:
            
            start_time = datetime.now()
            ctx.info(f"Starting genetic algorithm optimization for strategy {strategy_id}")
            
            try:
                # Initialize population
                population = self._initialize_population(parameter_ranges, population_size)
                convergence_data = []
                all_results = []
                
                best_individual = None
                best_fitness = float('-inf')
                
                for generation in range(generations):
                    ctx.info(f"Generation {generation + 1}/{generations}")
                    
                    # Evaluate fitness for each individual
                    fitness_scores = []
                    generation_results = []
                    
                    for individual in population:
                        try:
                            # Run backtest
                            backtest_result = await self._run_backtest_with_parameters(
                                strategy_id=strategy_id,
                                symbol=symbol,
                                timeframe=timeframe,
                                start_date=start_date,
                                end_date=end_date,
                                parameters=individual
                            )
                            
                            if backtest_result:
                                fitness = self._extract_metric_value(backtest_result, optimization_metric)
                                fitness_scores.append(fitness)
                                
                                result_data = {
                                    "parameters": individual,
                                    "performance": backtest_result,
                                    "metric_value": fitness,
                                    "generation": generation
                                }
                                generation_results.append(result_data)
                                all_results.append(result_data)
                                
                                # Track best individual
                                if fitness > best_fitness:
                                    best_fitness = fitness
                                    best_individual = result_data
                                    ctx.info(f"New best individual: {optimization_metric}={fitness:.4f}")
                            else:
                                fitness_scores.append(float('-inf'))
                                
                        except Exception as e:
                            ctx.error(f"Error evaluating individual {individual}: {e}")
                            fitness_scores.append(float('-inf'))
                    
                    # Record convergence data
                    if fitness_scores:
                        avg_fitness = np.mean([f for f in fitness_scores if f != float('-inf')])
                        convergence_data.append(avg_fitness)
                        ctx.info(f"Generation {generation + 1} - Avg fitness: {avg_fitness:.4f}, Best: {max(fitness_scores):.4f}")
                    
                    # Selection, crossover, and mutation
                    if generation < generations - 1:  # Don't evolve on last generation
                        population = self._evolve_population(
                            population, fitness_scores, parameter_ranges,
                            crossover_rate, mutation_rate
                        )
                
                execution_time = (datetime.now() - start_time).total_seconds()
                
                if not best_individual:
                    raise ValueError("No valid individuals found in genetic algorithm")
                
                # Create optimization result
                optimization_result = OptimizationResult(
                    best_parameters=best_individual["parameters"],
                    best_performance=best_individual["performance"],
                    all_results=all_results,
                    optimization_method="genetic_algorithm",
                    total_combinations=len(all_results),
                    execution_time=execution_time,
                    convergence_data=convergence_data
                )
                
                # Save optimization result
                await self._save_optimization_result(optimization_result, strategy_id)
                
                ctx.info(f"Genetic algorithm completed in {execution_time:.2f}s. Best {optimization_metric}: {best_fitness:.4f}")
                
                return optimization_result
                
            except Exception as e:
                ctx.error(f"Genetic algorithm optimization failed: {e}")
                raise

    async def get_optimization_status(self, optimization_id: str) -> Dict[str, Any]:
        """Get optimization status"""
        # This would typically check a database or cache for optimization status
        # For now, return a placeholder implementation
        return {
            "status": "running",
            "progress": 50,
            "message": "Optimization in progress",
            "estimated_completion": None
        }

    async def get_optimization_result(self, optimization_id: str) -> Optional[OptimizationResult]:
        """Get optimization result"""
        # This would typically retrieve from database
        # For now, return None (not found)
        return None

    async def get_user_optimization_history(self, user_id: int, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """Get optimization history for user"""
        # This would typically query database for user's optimization history
        # For now, return empty list
        return []

    async def cancel_optimization(self, optimization_id: str) -> bool:
        """Cancel a running optimization"""
        # This would typically mark optimization as cancelled in database
        # For now, return False (not found)
        return False

    async def store_optimization_result(self, optimization_id: str, result: OptimizationResult, user_id: int):
        """Store optimization result"""
        # This would typically store result in database
        logger.info(f"Storing optimization result for {optimization_id}")
        pass

    async def mark_optimization_failed(self, optimization_id: str, error_message: str):
        """Mark optimization as failed"""
        # This would typically update optimization status in database
        logger.error(f"Optimization {optimization_id} failed: {error_message}")
        pass
    
    def _generate_parameter_combinations(
        self,
        parameter_ranges: Dict[str, ParameterRange],
        max_combinations: int
    ) -> List[Dict[str, Any]]:
        """Generate all parameter combinations for grid search"""
        
        param_values = {}
        
        for param_name, param_range in parameter_ranges.items():
            if param_range.param_type == "choice":
                param_values[param_name] = param_range.choices
            elif param_range.param_type == "int":
                param_values[param_name] = list(range(
                    int(param_range.min_value),
                    int(param_range.max_value) + 1,
                    int(param_range.step)
                ))
            elif param_range.param_type == "float":
                values = []
                current = param_range.min_value
                while current <= param_range.max_value:
                    values.append(round(current, 4))
                    current += param_range.step
                param_values[param_name] = values
        
        # Generate all combinations
        keys = list(param_values.keys())
        value_lists = [param_values[key] for key in keys]
        
        combinations = []
        for combination in product(*value_lists):
            param_dict = dict(zip(keys, combination))
            combinations.append(param_dict)
        
        # Limit combinations if too many
        if len(combinations) > max_combinations:
            logger.warning(f"Too many combinations ({len(combinations)}), sampling {max_combinations}")
            combinations = random.sample(combinations, max_combinations)
        
        return combinations
    
    def _initialize_population(
        self,
        parameter_ranges: Dict[str, ParameterRange],
        population_size: int
    ) -> List[Dict[str, Any]]:
        """Initialize random population for genetic algorithm"""
        
        population = []
        
        for _ in range(population_size):
            individual = {}
            
            for param_name, param_range in parameter_ranges.items():
                if param_range.param_type == "choice":
                    individual[param_name] = random.choice(param_range.choices)
                elif param_range.param_type == "int":
                    individual[param_name] = random.randint(
                        int(param_range.min_value),
                        int(param_range.max_value)
                    )
                elif param_range.param_type == "float":
                    individual[param_name] = round(
                        random.uniform(param_range.min_value, param_range.max_value),
                        4
                    )
            
            population.append(individual)
        
        return population
    
    def _evolve_population(
        self,
        population: List[Dict[str, Any]],
        fitness_scores: List[float],
        parameter_ranges: Dict[str, ParameterRange],
        crossover_rate: float,
        mutation_rate: float
    ) -> List[Dict[str, Any]]:
        """Evolve population using selection, crossover, and mutation"""
        
        # Selection (tournament selection)
        new_population = []
        
        for _ in range(len(population)):
            # Tournament selection
            tournament_size = 3
            tournament_indices = random.sample(range(len(population)), tournament_size)
            tournament_fitness = [fitness_scores[i] for i in tournament_indices]
            winner_index = tournament_indices[tournament_fitness.index(max(tournament_fitness))]
            new_population.append(population[winner_index].copy())
        
        # Crossover
        for i in range(0, len(new_population) - 1, 2):
            if random.random() < crossover_rate:
                parent1 = new_population[i]
                parent2 = new_population[i + 1]
                
                # Single-point crossover
                param_names = list(parent1.keys())
                crossover_point = random.randint(1, len(param_names) - 1)
                
                for j, param_name in enumerate(param_names):
                    if j >= crossover_point:
                        parent1[param_name], parent2[param_name] = parent2[param_name], parent1[param_name]
        
        # Mutation
        for individual in new_population:
            for param_name, param_range in parameter_ranges.items():
                if random.random() < mutation_rate:
                    if param_range.param_type == "choice":
                        individual[param_name] = random.choice(param_range.choices)
                    elif param_range.param_type == "int":
                        individual[param_name] = random.randint(
                            int(param_range.min_value),
                            int(param_range.max_value)
                        )
                    elif param_range.param_type == "float":
                        individual[param_name] = round(
                            random.uniform(param_range.min_value, param_range.max_value),
                            4
                        )
        
        return new_population
    
    async def _run_backtest_with_parameters(
        self,
        strategy_id: int,
        symbol: str,
        timeframe: str,
        start_date: datetime,
        end_date: datetime,
        parameters: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Run backtest with specific parameters"""
        
        try:
            # Create a temporary backtest run
            backtest_result = await backtesting_engine.run_backtest(
                strategy_id=strategy_id,
                symbols=[symbol],
                timeframes=[timeframe],
                start_date=start_date,
                end_date=end_date,
                initial_capital=10000.0,
                parameters=parameters  # Pass parameters to backtesting engine
            )
            
            if backtest_result and hasattr(backtest_result, 'performance_metrics'):
                return backtest_result.performance_metrics
            
            return None
            
        except Exception as e:
            logger.error(f"Error running backtest with parameters {parameters}: {e}")
            return None
    
    def _extract_metric_value(self, performance_data: Dict[str, Any], metric: str) -> float:
        """Extract specific metric value from performance data"""
        
        metric_mapping = {
            "sharpe_ratio": "sharpe_ratio",
            "total_return": "total_return_percentage",
            "max_drawdown": "max_drawdown_percentage",
            "win_rate": "win_rate",
            "profit_factor": "profit_factor",
            "sortino_ratio": "sortino_ratio",
            "calmar_ratio": "calmar_ratio"
        }
        
        metric_key = metric_mapping.get(metric, metric)
        
        if metric_key in performance_data:
            value = performance_data[metric_key]
            
            # Handle special cases
            if metric == "max_drawdown":
                return -abs(value)  # Convert to negative for minimization
            
            return float(value) if value is not None else float('-inf')
        
        logger.warning(f"Metric {metric} not found in performance data")
        return float('-inf')
    
    async def _save_optimization_result(
        self,
        result: OptimizationResult,
        strategy_id: int
    ):
        """Save optimization result to file and database"""
        
        try:
            # Save to file
            results_dir = Path("optimization_results")
            results_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"optimization_{strategy_id}_{timestamp}.json"
            
            result_data = {
                "strategy_id": strategy_id,
                "timestamp": datetime.now().isoformat(),
                "best_parameters": result.best_parameters,
                "best_performance": result.best_performance,
                "optimization_method": result.optimization_method,
                "total_combinations": result.total_combinations,
                "execution_time": result.execution_time,
                "convergence_data": result.convergence_data,
                "all_results_count": len(result.all_results)
            }
            
            with open(results_dir / filename, "w") as f:
                json.dump(result_data, f, indent=2, default=str)
            
            logger.info(f"Optimization result saved to {filename}")
            
        except Exception as e:
            logger.error(f"Error saving optimization result: {e}")


# Global optimization service instance
optimization_service = OptimizationService()

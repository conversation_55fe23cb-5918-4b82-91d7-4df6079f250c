"""
Unit Tests for Risk Management Service
Author: inkbytefo
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from decimal import Decimal

from app.services.risk_management_service import (
    RiskManagementService,
    RiskParameters,
    PositionSizeResult,
    RiskAssessment,
    PositionSizeMethod
)


class TestRiskManagementService:
    """Test cases for Risk Management Service"""
    
    @pytest.fixture
    def risk_service(self):
        """Create a risk management service instance."""
        return RiskManagementService()
    
    @pytest.fixture
    def sample_risk_params(self):
        """Create sample risk parameters."""
        return RiskParameters(
            max_risk_per_trade=0.02,
            max_portfolio_risk=0.10,
            max_position_size=0.25,
            stop_loss_percentage=0.05,
            take_profit_percentage=0.15
        )
    
    @pytest.mark.asyncio
    async def test_calculate_position_size_volatility_based(self, risk_service, sample_risk_params):
        """Test volatility-based position size calculation."""
        # Mock market data service
        with patch('app.services.risk_management_service.market_data_service') as mock_market_service:
            mock_market_service.get_ohlcv_data.return_value = [
                [1704067200000, 50000.0, 51000.0, 49000.0, 50500.0, 1000.0],
                [1704070800000, 50500.0, 51500.0, 49500.0, 51000.0, 1100.0],
                [1704074400000, 51000.0, 52000.0, 50000.0, 51500.0, 1200.0]
            ]
            
            result = await risk_service.calculate_position_size(
                symbol="BTC/USDT",
                entry_price=50000.0,
                portfolio_value=10000.0,
                signal_confidence=0.8,
                method=PositionSizeMethod.VOLATILITY_BASED,
                custom_risk_params=sample_risk_params
            )
            
            assert isinstance(result, PositionSizeResult)
            assert result.position_size > 0
            assert result.risk_amount <= 10000.0 * sample_risk_params.max_risk_per_trade
            assert result.confidence_adjusted_size <= result.position_size
    
    @pytest.mark.asyncio
    async def test_calculate_position_size_fixed_percentage(self, risk_service, sample_risk_params):
        """Test fixed percentage position size calculation."""
        result = await risk_service.calculate_position_size(
            symbol="BTC/USDT",
            entry_price=50000.0,
            portfolio_value=10000.0,
            signal_confidence=0.7,
            method=PositionSizeMethod.FIXED_PERCENTAGE,
            custom_risk_params=sample_risk_params
        )
        
        assert isinstance(result, PositionSizeResult)
        assert result.position_size > 0
        assert result.risk_amount == 10000.0 * sample_risk_params.max_risk_per_trade
        assert result.confidence_adjusted_size == result.position_size * 0.7
    
    @pytest.mark.asyncio
    async def test_calculate_position_size_atr_based(self, risk_service, sample_risk_params):
        """Test ATR-based position size calculation."""
        with patch('app.services.risk_management_service.market_data_service') as mock_market_service:
            mock_market_service.get_ohlcv_data.return_value = [
                [1704067200000, 50000.0, 51000.0, 49000.0, 50500.0, 1000.0],
                [1704070800000, 50500.0, 51500.0, 49500.0, 51000.0, 1100.0],
                [1704074400000, 51000.0, 52000.0, 50000.0, 51500.0, 1200.0]
            ]
            
            result = await risk_service.calculate_position_size(
                symbol="BTC/USDT",
                entry_price=50000.0,
                portfolio_value=10000.0,
                signal_confidence=0.9,
                method=PositionSizeMethod.ATR_BASED,
                custom_risk_params=sample_risk_params
            )
            
            assert isinstance(result, PositionSizeResult)
            assert result.position_size > 0
            assert result.atr_value > 0
    
    @pytest.mark.asyncio
    async def test_assess_trade_risk(self, risk_service):
        """Test trade risk assessment."""
        trade_data = {
            'symbol': 'BTC/USDT',
            'entry_price': 50000.0,
            'stop_loss': 48000.0,
            'take_profit': 55000.0,
            'position_size': 0.1,
            'portfolio_value': 10000.0
        }
        
        assessment = await risk_service.assess_trade_risk(trade_data)
        
        assert isinstance(assessment, RiskAssessment)
        assert assessment.risk_score >= 0
        assert assessment.risk_level in ['low', 'medium', 'high']
        assert isinstance(assessment.risk_factors, list)
        assert isinstance(assessment.recommendations, list)
    
    @pytest.mark.asyncio
    async def test_calculate_portfolio_risk(self, risk_service):
        """Test portfolio risk calculation."""
        positions = [
            {
                'symbol': 'BTC/USDT',
                'quantity': 0.1,
                'entry_price': 50000.0,
                'current_price': 51000.0,
                'stop_loss': 48000.0
            },
            {
                'symbol': 'ETH/USDT',
                'quantity': 2.0,
                'entry_price': 3000.0,
                'current_price': 3100.0,
                'stop_loss': 2850.0
            }
        ]
        
        portfolio_value = 10000.0
        
        risk_metrics = await risk_service.calculate_portfolio_risk(positions, portfolio_value)
        
        assert 'total_risk_amount' in risk_metrics
        assert 'risk_percentage' in risk_metrics
        assert 'max_potential_loss' in risk_metrics
        assert 'diversification_score' in risk_metrics
        assert risk_metrics['risk_percentage'] >= 0
        assert risk_metrics['risk_percentage'] <= 1
    
    @pytest.mark.asyncio
    async def test_validate_trade_against_limits(self, risk_service, sample_risk_params):
        """Test trade validation against risk limits."""
        trade_data = {
            'symbol': 'BTC/USDT',
            'position_size': 0.05,  # 5% of portfolio
            'entry_price': 50000.0,
            'stop_loss': 48000.0,
            'portfolio_value': 10000.0
        }
        
        is_valid, violations = await risk_service.validate_trade_against_limits(
            trade_data, sample_risk_params
        )
        
        assert isinstance(is_valid, bool)
        assert isinstance(violations, list)
        
        # Test with oversized position
        trade_data['position_size'] = 0.3  # 30% of portfolio
        is_valid, violations = await risk_service.validate_trade_against_limits(
            trade_data, sample_risk_params
        )
        
        assert not is_valid
        assert len(violations) > 0
    
    def test_calculate_stop_loss_distance(self, risk_service):
        """Test stop loss distance calculation."""
        entry_price = 50000.0
        stop_loss = 48000.0
        
        distance = risk_service._calculate_stop_loss_distance(entry_price, stop_loss)
        
        assert distance == 0.04  # 4% distance
    
    def test_calculate_risk_reward_ratio(self, risk_service):
        """Test risk-reward ratio calculation."""
        entry_price = 50000.0
        stop_loss = 48000.0
        take_profit = 55000.0
        
        ratio = risk_service._calculate_risk_reward_ratio(entry_price, stop_loss, take_profit)
        
        assert ratio == 2.5  # 5000 profit / 2000 risk
    
    @pytest.mark.asyncio
    async def test_calculate_volatility(self, risk_service):
        """Test volatility calculation."""
        price_data = [50000.0, 51000.0, 49500.0, 52000.0, 50500.0]
        
        volatility = await risk_service._calculate_volatility(price_data)
        
        assert volatility > 0
        assert isinstance(volatility, float)
    
    @pytest.mark.asyncio
    async def test_calculate_atr(self, risk_service):
        """Test ATR calculation."""
        ohlc_data = [
            [50000.0, 51000.0, 49000.0, 50500.0],
            [50500.0, 51500.0, 49500.0, 51000.0],
            [51000.0, 52000.0, 50000.0, 51500.0]
        ]
        
        atr = await risk_service._calculate_atr(ohlc_data)
        
        assert atr > 0
        assert isinstance(atr, float)
    
    def test_risk_parameters_validation(self):
        """Test risk parameters validation."""
        # Valid parameters
        valid_params = RiskParameters(
            max_risk_per_trade=0.02,
            max_portfolio_risk=0.10,
            max_position_size=0.25,
            stop_loss_percentage=0.05,
            take_profit_percentage=0.15
        )
        
        assert valid_params.max_risk_per_trade == 0.02
        
        # Test parameter constraints
        with pytest.raises(ValueError):
            RiskParameters(
                max_risk_per_trade=1.5,  # Invalid: > 1
                max_portfolio_risk=0.10,
                max_position_size=0.25,
                stop_loss_percentage=0.05,
                take_profit_percentage=0.15
            )
    
    @pytest.mark.asyncio
    async def test_emergency_stop_conditions(self, risk_service):
        """Test emergency stop conditions."""
        portfolio_data = {
            'current_value': 7000.0,  # 30% loss
            'initial_value': 10000.0,
            'daily_pnl': -500.0,
            'open_positions': 5
        }
        
        should_stop, reasons = await risk_service.check_emergency_stop_conditions(portfolio_data)
        
        assert isinstance(should_stop, bool)
        assert isinstance(reasons, list)
        
        # With severe losses, should trigger emergency stop
        portfolio_data['current_value'] = 6000.0  # 40% loss
        should_stop, reasons = await risk_service.check_emergency_stop_conditions(portfolio_data)
        
        assert should_stop
        assert len(reasons) > 0
    
    @pytest.mark.asyncio
    async def test_position_size_with_zero_confidence(self, risk_service, sample_risk_params):
        """Test position size calculation with zero confidence."""
        result = await risk_service.calculate_position_size(
            symbol="BTC/USDT",
            entry_price=50000.0,
            portfolio_value=10000.0,
            signal_confidence=0.0,
            method=PositionSizeMethod.FIXED_PERCENTAGE,
            custom_risk_params=sample_risk_params
        )
        
        assert result.confidence_adjusted_size == 0.0
    
    @pytest.mark.asyncio
    async def test_position_size_with_invalid_params(self, risk_service):
        """Test position size calculation with invalid parameters."""
        with pytest.raises(ValueError):
            await risk_service.calculate_position_size(
                symbol="",  # Invalid symbol
                entry_price=50000.0,
                portfolio_value=10000.0,
                signal_confidence=0.8
            )
        
        with pytest.raises(ValueError):
            await risk_service.calculate_position_size(
                symbol="BTC/USDT",
                entry_price=0.0,  # Invalid price
                portfolio_value=10000.0,
                signal_confidence=0.8
            )
        
        with pytest.raises(ValueError):
            await risk_service.calculate_position_size(
                symbol="BTC/USDT",
                entry_price=50000.0,
                portfolio_value=0.0,  # Invalid portfolio value
                signal_confidence=0.8
            )

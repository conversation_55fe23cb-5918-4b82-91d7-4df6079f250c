"""
Unit tests for Portfolio Management Service
Author: inkbytefo
"""

import pytest
import pandas as pd
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any

from app.services.portfolio_management_service import (
    PortfolioManagementService,
    PerformancePeriod,
    AssetAllocation
)


@pytest.fixture
def portfolio_service():
    """Create portfolio management service instance."""
    return PortfolioManagementService()


@pytest.fixture
def sample_portfolio_data():
    """Sample portfolio data for testing."""
    return {
        "user_id": 1,
        "total_value_usd": 10000.0,
        "holdings": [
            {
                "symbol": "BTC",
                "amount": 0.5,
                "current_price": 45000.0,
                "total_value": 22500.0
            },
            {
                "symbol": "ETH", 
                "amount": 10.0,
                "current_price": 3000.0,
                "total_value": 30000.0
            }
        ],
        "trades": [
            {"symbol": "BTC", "pnl": 1000.0, "timestamp": datetime.now()},
            {"symbol": "ETH", "pnl": -500.0, "timestamp": datetime.now()},
            {"symbol": "BTC", "pnl": 2000.0, "timestamp": datetime.now()}
        ]
    }


@pytest.fixture
def sample_performance_data():
    """Sample performance data for testing."""
    dates = [datetime.now() - timedelta(days=i) for i in range(30, 0, -1)]
    values = [10000 + i * 100 for i in range(30)]
    
    return pd.DataFrame({
        'timestamp': dates,
        'portfolio_value': values,
        'daily_return': [0.01] * 30
    })


class TestPortfolioManagementService:
    """Test cases for Portfolio Management Service."""

    @pytest.mark.asyncio
    async def test_calculate_period_pnl(self, portfolio_service, sample_performance_data):
        """Test period PnL calculation."""
        # Test 1-day period
        absolute_pnl, percentage_pnl = await portfolio_service._calculate_period_pnl(
            sample_performance_data, PerformancePeriod.DAY_1
        )
        
        assert isinstance(absolute_pnl, float)
        assert isinstance(percentage_pnl, float)
        
        # Test with empty data
        empty_df = pd.DataFrame()
        absolute_pnl, percentage_pnl = await portfolio_service._calculate_period_pnl(
            empty_df, PerformancePeriod.DAY_1
        )
        
        assert absolute_pnl == 0.0
        assert percentage_pnl == 0.0

    @pytest.mark.asyncio
    async def test_calculate_max_drawdown(self, portfolio_service, sample_performance_data):
        """Test maximum drawdown calculation."""
        # Test with normal data
        max_drawdown = await portfolio_service._calculate_max_drawdown(sample_performance_data)
        
        assert isinstance(max_drawdown, float)
        assert max_drawdown >= 0.0
        
        # Test with empty data
        empty_df = pd.DataFrame()
        max_drawdown = await portfolio_service._calculate_max_drawdown(empty_df)
        
        assert max_drawdown == 0.0

    @pytest.mark.asyncio
    async def test_calculate_sharpe_ratio(self, portfolio_service, sample_performance_data):
        """Test Sharpe ratio calculation."""
        sharpe_ratio = await portfolio_service._calculate_sharpe_ratio(sample_performance_data)
        
        assert isinstance(sharpe_ratio, float)
        
        # Test with empty data
        empty_df = pd.DataFrame()
        sharpe_ratio = await portfolio_service._calculate_sharpe_ratio(empty_df)
        
        assert sharpe_ratio == 0.0

    @pytest.mark.asyncio
    async def test_calculate_sortino_ratio(self, portfolio_service, sample_performance_data):
        """Test Sortino ratio calculation."""
        sortino_ratio = await portfolio_service._calculate_sortino_ratio(sample_performance_data)
        
        assert isinstance(sortino_ratio, float)
        
        # Test with empty data
        empty_df = pd.DataFrame()
        sortino_ratio = await portfolio_service._calculate_sortino_ratio(empty_df)
        
        assert sortino_ratio == 0.0

    @pytest.mark.asyncio
    async def test_get_trading_statistics(self, portfolio_service, sample_portfolio_data):
        """Test trading statistics calculation."""
        stats = await portfolio_service._get_trading_statistics(sample_portfolio_data)
        
        assert isinstance(stats, dict)
        assert 'win_rate' in stats
        assert 'profit_factor' in stats
        assert 'total_trades' in stats
        assert 'winning_trades' in stats
        assert 'losing_trades' in stats
        
        # Verify calculations
        assert stats['total_trades'] == 3
        assert stats['winning_trades'] == 2
        assert stats['losing_trades'] == 1
        assert stats['win_rate'] == 66.67  # 2/3 * 100

    @pytest.mark.asyncio
    async def test_get_portfolio_allocation_targets(self, portfolio_service):
        """Test portfolio allocation targets."""
        # Test different user IDs for different strategies
        conservative_targets = await portfolio_service.get_portfolio_allocation_targets(3)  # user_id % 3 == 0
        moderate_targets = await portfolio_service.get_portfolio_allocation_targets(4)      # user_id % 3 == 1
        aggressive_targets = await portfolio_service.get_portfolio_allocation_targets(5)    # user_id % 3 == 2
        
        # Verify all return dictionaries
        assert isinstance(conservative_targets, dict)
        assert isinstance(moderate_targets, dict)
        assert isinstance(aggressive_targets, dict)
        
        # Verify BTC allocation differs between strategies
        assert conservative_targets.get('BTC', 0) != moderate_targets.get('BTC', 0)
        assert moderate_targets.get('BTC', 0) != aggressive_targets.get('BTC', 0)
        
        # Verify allocations sum to 100% (approximately)
        conservative_sum = sum(conservative_targets.values())
        assert abs(conservative_sum - 100.0) < 1.0

    @pytest.mark.asyncio
    async def test_get_historical_performance(self, portfolio_service):
        """Test historical performance data generation."""
        portfolio_data = {"user_id": 1}
        
        performance_df = await portfolio_service._get_historical_performance(portfolio_data)
        
        assert isinstance(performance_df, pd.DataFrame)
        
        if not performance_df.empty:
            assert 'timestamp' in performance_df.columns
            assert 'portfolio_value' in performance_df.columns
            assert len(performance_df) > 0

    @pytest.mark.asyncio
    @patch('app.services.portfolio_management_service.trading_engine_service')
    async def test_sync_portfolio_with_exchange(self, mock_trading_service, portfolio_service):
        """Test portfolio synchronization with exchange."""
        # Mock exchange balance
        mock_trading_service.get_account_balance.return_value = {
            "status": "success",
            "balances": {
                "BTC": {"free": 0.5, "used": 0.0, "total": 0.5},
                "ETH": {"free": 10.0, "used": 0.0, "total": 10.0}
            }
        }
        
        result = await portfolio_service.sync_portfolio_with_exchange(1)
        
        assert isinstance(result, dict)
        assert result.get("status") in ["success", "error"]

    @pytest.mark.asyncio
    async def test_calculate_rebalancing_orders(self, portfolio_service):
        """Test rebalancing orders calculation."""
        target_allocations = {
            "BTC": 50.0,
            "ETH": 30.0,
            "BNB": 20.0
        }
        
        with patch.object(portfolio_service, 'get_portfolio_summary') as mock_summary:
            mock_summary.return_value = {
                "total_value_usd": 10000.0,
                "allocations": [
                    AssetAllocation(
                        symbol="BTC",
                        asset_name="Bitcoin",
                        quantity=0.3,
                        current_price=45000.0,
                        total_value_usd=13500.0,
                        percentage_of_portfolio=60.0,
                        average_buy_price=40000.0,
                        unrealized_pnl=1500.0,
                        unrealized_pnl_percentage=11.11
                    )
                ]
            }
            
            orders = await portfolio_service.calculate_rebalancing_orders(1, target_allocations)
            
            assert isinstance(orders, list)


if __name__ == "__main__":
    pytest.main([__file__])

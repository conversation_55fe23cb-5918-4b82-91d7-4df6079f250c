"""
Simple Working Telegram Bot
Author: inkbytefo

A simple, working Telegram bot without complex async issues.
"""

import logging
import sys
import os
import asyncio
import aiohttp
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Telegram imports
from telegram import Update
from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes

# App imports
from app.core.config import settings

# AI imports
try:
    import openai
    AI_AVAILABLE = True
except ImportError:
    AI_AVAILABLE = False


# Global variables
AUTHORIZED_USERS = []
ADMIN_USERS = []

def parse_users():
    """Parse authorized and admin users"""
    global AUTHORIZED_USERS, ADMIN_USERS
    
    # Parse authorized users
    if settings.TELEGRAM_AUTHORIZED_USERS:
        users_str = str(settings.TELEGRAM_AUTHORIZED_USERS)
        if ',' in users_str:
            AUTHORIZED_USERS = [int(user.strip()) for user in users_str.split(',')]
        else:
            AUTHORIZED_USERS = [int(users_str)]
    
    # Parse admin users
    if settings.TELEGRAM_ADMIN_USERS:
        users_str = str(settings.TELEGRAM_ADMIN_USERS)
        if ',' in users_str:
            ADMIN_USERS = [int(user.strip()) for user in users_str.split(',')]
        else:
            ADMIN_USERS = [int(users_str)]


def is_authorized(user_id: int) -> bool:
    """Check if user is authorized"""
    return user_id in AUTHORIZED_USERS


def is_admin(user_id: int) -> bool:
    """Check if user is admin"""
    return user_id in ADMIN_USERS


async def get_ai_response(user_message: str, user_name: str) -> str:
    """Get AI response using OpenAI/Mistral API"""
    if not AI_AVAILABLE or not settings.OPENAI_API_KEY:
        return get_simple_ai_response(user_message)

    try:
        # Configure OpenAI client for Mistral
        client = openai.OpenAI(
            api_key=settings.OPENAI_API_KEY,
            base_url=settings.OPENAI_BASE_URL
        )

        # Create system prompt
        system_prompt = f"""Sen AI Crypto Trading System'in Telegram bot asistanısın.
Kullanıcı adı: {user_name}

Görevlerin:
1. Kripto para analizi yapmak
2. Trading önerileri vermek
3. Fiyat alarmları konusunda yardım etmek
4. Portföy yönetimi tavsiyeleri vermek
5. Piyasa durumu hakkında bilgi vermek

Türkçe yanıt ver ve emoji kullan. Kısa ve öz ol. Finansal tavsiye verirken risk uyarısı yap."""

        # Get AI response
        response = client.chat.completions.create(
            model=settings.OPENAI_MODEL,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_message}
            ],
            max_tokens=500,
            temperature=0.7
        )

        ai_response = response.choices[0].message.content.strip()

        # Add disclaimer for financial advice
        if any(word in user_message.lower() for word in ['al', 'sat', 'yatırım', 'öneri', 'tavsiye']):
            ai_response += "\n\n⚠️ *Bu bilgiler yatırım tavsiyesi değildir. Kendi araştırmanızı yapın.*"

        return ai_response

    except Exception as e:
        logger.error(f"AI response error: {e}")
        return get_simple_ai_response(user_message)


def get_simple_ai_response(user_message: str) -> str:
    """Get simple AI response without external API"""
    message_lower = user_message.lower()

    # Simple AI responses
    ai_responses = {
        "merhaba": "Merhaba! 👋 AI Crypto Trading System'e hoş geldiniz!",
        "btc": "📊 Bitcoin (BTC) analizi:\n• Trend: Yükseliş eğiliminde\n• Destek: $65,000\n• Direnç: $70,000\n• Öneri: Dikkatli takip edin",
        "eth": "📊 Ethereum (ETH) analizi:\n• Trend: Güçlü momentum\n• Destek: $3,200\n• Direnç: $3,500\n• Öneri: Pozitif görünüm",
        "analiz": "🔍 Kripto analiz özellikleri:\n• Teknik analiz\n• Pattern tespiti\n• AI destekli tahminler\n• Risk yönetimi",
        "portföy": "💼 Portföy yönetimi:\n• Otomatik rebalancing\n• Risk analizi\n• Performans takibi\n• Çeşitlendirme önerileri",
        "alarm": "🚨 Fiyat alarm sistemi:\n• Gerçek zamanlı takip\n• Özelleştirilebilir limitler\n• Telegram bildirimleri\n• Çoklu coin desteği",
        "sinyal": "📈 Trading sinyalleri:\n• AI destekli analiz\n• Giriş/çıkış noktaları\n• Risk/ödül oranları\n• Güvenilirlik skoru",
        "yardım": "🆘 Yardım için /help komutunu kullanın",
        "durum": "📊 Sistem durumu için /status komutunu kullanın"
    }

    # Find matching response
    for keyword, reply in ai_responses.items():
        if keyword in message_lower:
            return reply

    # Default response
    return f"""
🤖 **AI Asistan Yanıtı**

Mesajınız: "{user_message}"

🧠 **AI Analizi:**
Mesajınızı analiz ettim. Şu anda geliştirme aşamasında olan özellikler:

📊 **Mevcut Özellikler:**
• Temel komut işleme
• Kullanıcı yetkilendirme
• Sistem durumu takibi

🔮 **Yakında Gelecek:**
• Gelişmiş doğal dil işleme
• Gerçek zamanlı kripto analizi
• Akıllı trading önerileri
• Portföy optimizasyonu

💡 **Öneriler:**
• /help - Mevcut komutları görün
• /status - Sistem durumunu kontrol edin
• "BTC analizi" - Coin analizi isteyin
• "alarm kur" - Fiyat alarmı oluşturun

Teşekkürler! 🚀
    """


async def start_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle /start command"""
    user = update.effective_user
    user_id = user.id
    
    if not is_authorized(user_id):
        await update.message.reply_text(
            "❌ Yetkisiz erişim! Bu bot sadece yetkili kullanıcılar içindir."
        )
        logger.warning(f"Unauthorized access attempt from {user_id} (@{user.username})")
        return
    
    welcome_message = f"""
🤖 **AI Crypto Trading System**

Hoş geldiniz {user.first_name}! 

✅ **Bot Özellikleri:**
• 🧠 AI destekli analiz
• 📊 Teknik analiz
• 🚨 Fiyat alarmları
• 📈 Trading sinyalleri
• 💼 Portföy yönetimi

📋 **Komutlar:**
• /start - Bot'u başlat
• /help - Yardım menüsü
• /status - Sistem durumu

🔮 **AI Asistan:**
Doğal dilde mesaj yazarak AI asistanı kullanabilirsiniz:
• "BTC analizi yap"
• "ETH için alarm kur"
• "Portföyümü göster"

🚀 **Başlamak için bir mesaj yazın!**
    """
    
    await update.message.reply_text(welcome_message, parse_mode='Markdown')
    logger.info(f"User {user_id} (@{user.username}) started the bot")


async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle /help command"""
    user_id = update.effective_user.id
    
    if not is_authorized(user_id):
        await update.message.reply_text("❌ Yetkisiz erişim!")
        return
    
    help_message = """
🆘 **Yardım Menüsü**

📋 **Temel Komutlar:**
• /start - Bot'u başlat
• /help - Bu yardım menüsü
• /status - Sistem durumu

🤖 **AI Asistan:**
Bot'a doğal dilde mesaj yazabilirsiniz:
• "Bitcoin nasıl gidiyor?"
• "ETH için alarm kur"
• "Portföyümü analiz et"
• "En iyi coinler hangileri?"

💡 **İpuçları:**
• Coin isimlerini büyük harfle yazın (BTC, ETH)
• Günlük analiz için sabah saatlerini tercih edin
• Sistem sürekli geliştirilmektedir
    """
    
    await update.message.reply_text(help_message, parse_mode='Markdown')


async def status_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle /status command"""
    user_id = update.effective_user.id
    
    if not is_authorized(user_id):
        await update.message.reply_text("❌ Yetkisiz erişim!")
        return
    
    status_message = f"""
📊 **Sistem Durumu**

🤖 **Bot Bilgileri:**
• Status: ✅ Aktif
• Uptime: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
• Version: 1.0.0
• Environment: {settings.ENVIRONMENT}

👤 **Kullanıcı Bilgileri:**
• User ID: {user_id}
• Username: @{update.effective_user.username or 'N/A'}
• Authorization: ✅ Yetkili
• Admin: {'✅ Evet' if is_admin(user_id) else '❌ Hayır'}

🔧 **Servis Durumu:**
• Telegram Bot: ✅ Çalışıyor
• AI Assistant: ✅ Aktif
• Market Data: ⚠️ Geliştirme aşamasında
• Trading Engine: ⚠️ Geliştirme aşamasında

📈 **Özellikler:**
• Doğal dil işleme: ✅
• Kripto analizi: ⚠️ Beta
• Fiyat alarmları: ⚠️ Beta
• Trading sinyalleri: ⚠️ Beta
    """
    
    await update.message.reply_text(status_message, parse_mode='Markdown')


async def handle_message(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle regular messages with AI assistant"""
    user_id = update.effective_user.id
    user_name = update.effective_user.first_name or "Kullanıcı"
    message_text = update.message.text

    if not is_authorized(user_id):
        await update.message.reply_text("❌ Yetkisiz erişim!")
        return

    logger.info(f"Message from {user_id} (@{update.effective_user.username}): {message_text}")

    # Show typing indicator
    await context.bot.send_chat_action(chat_id=update.effective_chat.id, action="typing")

    # Get AI response
    try:
        response = await get_ai_response(message_text, user_name)
        await update.message.reply_text(response, parse_mode='Markdown')
    except openai.APIError as e:
        logger.error(f"OpenAI API error processing message: {e}")
        await update.message.reply_text(
            "❌ AI servisi şu anda kullanılamıyor. Lütfen daha sonra tekrar deneyin.",
            parse_mode='Markdown'
        )
    except (aiohttp.ClientError, asyncio.TimeoutError) as e:
        logger.error(f"Network error processing message: {e}")
        await update.message.reply_text(
            "❌ Ağ bağlantısı sorunu. Lütfen tekrar deneyin.",
            parse_mode='Markdown'
        )
    except Exception as e:
        logger.error(f"Unexpected error processing message: {e}")
        await update.message.reply_text(
            "❌ Mesajınızı işlerken beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.",
            parse_mode='Markdown'
        )


async def error_handler(update: object, context: ContextTypes.DEFAULT_TYPE):
    """Handle errors"""
    logger.error(f"Exception while handling an update: {context.error}")


def main():
    """Main function"""
    print("🤖 AI Crypto Trading System - Simple Telegram Bot")
    print("=" * 50)
    
    # Check configuration
    if not settings.TELEGRAM_BOT_TOKEN:
        print("❌ TELEGRAM_BOT_TOKEN not configured")
        return
    
    if not settings.TELEGRAM_AUTHORIZED_USERS:
        print("❌ TELEGRAM_AUTHORIZED_USERS not configured")
        return
    
    # Parse users
    parse_users()
    
    # Create application
    application = Application.builder().token(settings.TELEGRAM_BOT_TOKEN).build()
    
    # Add handlers
    application.add_handler(CommandHandler("start", start_command))
    application.add_handler(CommandHandler("help", help_command))
    application.add_handler(CommandHandler("status", status_command))
    application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_message))
    
    # Add error handler
    application.add_error_handler(error_handler)
    
    logger.info("✅ Bot handlers configured")
    logger.info(f"🔧 Environment: {settings.ENVIRONMENT}")
    logger.info(f"👥 Authorized Users: {AUTHORIZED_USERS}")
    logger.info(f"👑 Admin Users: {ADMIN_USERS}")
    
    # Start the bot
    print("🚀 Starting Telegram bot...")
    print("📱 Go to Telegram and send /start to your bot!")
    print("Press Ctrl+C to stop the bot")
    print("-" * 50)
    
    try:
        application.run_polling(drop_pending_updates=True)
    except KeyboardInterrupt:
        print("\n🛑 Bot stopped by user")
        logger.info("Bot stopped by user")
    except Exception as e:
        print(f"❌ Error: {e}")
        logger.error(f"Error: {e}")


if __name__ == "__main__":
    main()

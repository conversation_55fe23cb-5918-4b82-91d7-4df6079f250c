"""
Test Suite for AI Crypto Trading System
Author: inkbytefo

This package contains comprehensive tests for the trading system including:
- Unit tests for individual components
- Integration tests for service interactions
- Performance tests for critical operations
- End-to-end tests for complete workflows
"""

import os
import sys
import asyncio
import pytest
from pathlib import Path

# Add backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Test configuration
TEST_DATABASE_URL = os.getenv('TEST_DATABASE_URL', 'postgresql://postgres:password@localhost:5432/crypto_trading_test')
TEST_REDIS_URL = os.getenv('TEST_REDIS_URL', 'redis://localhost:6379/1')

# Test settings
ENABLE_INTEGRATION_TESTS = os.getenv('ENABLE_INTEGRATION_TESTS', 'true').lower() == 'true'
ENABLE_PERFORMANCE_TESTS = os.getenv('ENABLE_PERFORMANCE_TESTS', 'false').lower() == 'true'
ENABLE_E2E_TESTS = os.getenv('ENABLE_E2E_TESTS', 'false').lower() == 'true'

# Test data
SAMPLE_SYMBOLS = ['BTC/USDT', 'ETH/USDT', 'ADA/USDT']
SAMPLE_TIMEFRAMES = ['1m', '5m', '15m', '1h', '4h', '1d']

# Test fixtures and utilities will be imported from conftest.py

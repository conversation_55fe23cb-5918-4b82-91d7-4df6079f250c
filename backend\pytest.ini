[tool:pytest]
# Pytest Configuration for AI Crypto Trading System
# Author: inkbytefo

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 6.0

# Add options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=app
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml
    --cov-fail-under=80
    --asyncio-mode=auto

# Markers
markers =
    unit: Unit tests for individual components
    integration: Integration tests for service interactions
    performance: Performance and load tests
    e2e: End-to-end tests
    slow: Tests that take a long time to run
    external: Tests that require external services
    database: Tests that require database connection
    redis: Tests that require Redis connection
    api: Tests for API endpoints
    telegram: Tests for Telegram bot functionality
    trading: Tests for trading operations
    ai: Tests for AI analysis functionality
    risk: Tests for risk management
    portfolio: Tests for portfolio management

# Test timeout
timeout = 300

# Asyncio configuration
asyncio_mode = auto

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:ccxt.*

# Environment variables for testing
env =
    ENVIRONMENT = testing
    DEBUG = true
    DATABASE_URL = sqlite:///./test.db
    REDIS_URL = redis://localhost:6379/1
    ENABLE_TRADING = false
    LOG_LEVEL = WARNING

"""Add walk-forward analysis models

Revision ID: c70ca1646bf9
Revises: 26bc7b5feea0
Create Date: 2025-07-26 16:16:53.316882

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c70ca1646bf9'
down_revision: Union[str, Sequence[str], None] = '26bc7b5feea0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('backtest_strategies',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('strategy_uuid', sa.String(length=36), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('strategy_type', sa.String(length=20), nullable=False),
    sa.Column('version', sa.String(length=20), nullable=True),
    sa.Column('parameters', sa.JSON(), nullable=True),
    sa.Column('risk_parameters', sa.JSON(), nullable=True),
    sa.Column('entry_conditions', sa.JSON(), nullable=True),
    sa.Column('exit_conditions', sa.JSON(), nullable=True),
    sa.Column('min_confidence', sa.Float(), nullable=True),
    sa.Column('max_position_size', sa.Float(), nullable=True),
    sa.Column('stop_loss_percentage', sa.Float(), nullable=True),
    sa.Column('take_profit_percentage', sa.Float(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('created_by', sa.String(length=100), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_backtest_strategies_id'), 'backtest_strategies', ['id'], unique=False)
    op.create_index(op.f('ix_backtest_strategies_strategy_uuid'), 'backtest_strategies', ['strategy_uuid'], unique=True)
    op.create_table('backtest_runs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('run_uuid', sa.String(length=36), nullable=True),
    sa.Column('strategy_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('symbols', sa.JSON(), nullable=True),
    sa.Column('timeframes', sa.JSON(), nullable=True),
    sa.Column('start_date', sa.DateTime(), nullable=False),
    sa.Column('end_date', sa.DateTime(), nullable=False),
    sa.Column('initial_capital', sa.Float(), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('progress_percentage', sa.Float(), nullable=True),
    sa.Column('current_symbol', sa.String(length=20), nullable=True),
    sa.Column('current_date', sa.DateTime(), nullable=True),
    sa.Column('total_trades', sa.Integer(), nullable=True),
    sa.Column('winning_trades', sa.Integer(), nullable=True),
    sa.Column('losing_trades', sa.Integer(), nullable=True),
    sa.Column('final_capital', sa.Float(), nullable=True),
    sa.Column('total_return', sa.Float(), nullable=True),
    sa.Column('total_return_percentage', sa.Float(), nullable=True),
    sa.Column('max_drawdown', sa.Float(), nullable=True),
    sa.Column('max_drawdown_percentage', sa.Float(), nullable=True),
    sa.Column('sharpe_ratio', sa.Float(), nullable=True),
    sa.Column('sortino_ratio', sa.Float(), nullable=True),
    sa.Column('calmar_ratio', sa.Float(), nullable=True),
    sa.Column('volatility', sa.Float(), nullable=True),
    sa.Column('win_rate', sa.Float(), nullable=True),
    sa.Column('avg_win', sa.Float(), nullable=True),
    sa.Column('avg_loss', sa.Float(), nullable=True),
    sa.Column('profit_factor', sa.Float(), nullable=True),
    sa.Column('avg_trade_duration', sa.Float(), nullable=True),
    sa.Column('execution_time', sa.Float(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('detailed_results', sa.JSON(), nullable=True),
    sa.Column('started_at', sa.DateTime(), nullable=True),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['strategy_id'], ['backtest_strategies.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_backtest_runs_id'), 'backtest_runs', ['id'], unique=False)
    op.create_index(op.f('ix_backtest_runs_run_uuid'), 'backtest_runs', ['run_uuid'], unique=True)
    op.create_table('walk_forward_analyses',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('analysis_uuid', sa.String(length=36), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('strategy_id', sa.Integer(), nullable=False),
    sa.Column('symbol', sa.String(length=20), nullable=False),
    sa.Column('timeframe', sa.String(length=10), nullable=False),
    sa.Column('start_date', sa.DateTime(), nullable=False),
    sa.Column('end_date', sa.DateTime(), nullable=False),
    sa.Column('in_sample_ratio', sa.Float(), nullable=False),
    sa.Column('step_size_months', sa.Integer(), nullable=False),
    sa.Column('min_out_sample_months', sa.Integer(), nullable=False),
    sa.Column('optimization_metric', sa.String(length=50), nullable=False),
    sa.Column('total_periods', sa.Integer(), nullable=True),
    sa.Column('avg_in_sample_return', sa.Float(), nullable=True),
    sa.Column('avg_out_sample_return', sa.Float(), nullable=True),
    sa.Column('in_sample_sharpe', sa.Float(), nullable=True),
    sa.Column('out_sample_sharpe', sa.Float(), nullable=True),
    sa.Column('consistency_score', sa.Float(), nullable=True),
    sa.Column('overfitting_score', sa.Float(), nullable=True),
    sa.Column('stability_score', sa.Float(), nullable=True),
    sa.Column('is_robust', sa.Boolean(), nullable=True),
    sa.Column('robustness_rating', sa.String(length=20), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('progress_percentage', sa.Float(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('recommendations', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('started_at', sa.DateTime(), nullable=True),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['strategy_id'], ['backtest_strategies.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_walk_forward_analyses_analysis_uuid'), 'walk_forward_analyses', ['analysis_uuid'], unique=True)
    op.create_index(op.f('ix_walk_forward_analyses_id'), 'walk_forward_analyses', ['id'], unique=False)
    op.create_table('backtest_trades',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('trade_uuid', sa.String(length=36), nullable=True),
    sa.Column('backtest_run_id', sa.Integer(), nullable=False),
    sa.Column('symbol', sa.String(length=20), nullable=False),
    sa.Column('timeframe', sa.String(length=10), nullable=False),
    sa.Column('trade_type', sa.String(length=10), nullable=False),
    sa.Column('entry_date', sa.DateTime(), nullable=False),
    sa.Column('entry_price', sa.Float(), nullable=False),
    sa.Column('entry_signal_strength', sa.Float(), nullable=True),
    sa.Column('entry_confidence', sa.Float(), nullable=True),
    sa.Column('entry_reasoning', sa.Text(), nullable=True),
    sa.Column('exit_date', sa.DateTime(), nullable=True),
    sa.Column('exit_price', sa.Float(), nullable=True),
    sa.Column('exit_reason', sa.String(length=50), nullable=True),
    sa.Column('exit_signal_strength', sa.Float(), nullable=True),
    sa.Column('quantity', sa.Float(), nullable=False),
    sa.Column('position_size_percentage', sa.Float(), nullable=True),
    sa.Column('pnl', sa.Float(), nullable=True),
    sa.Column('pnl_percentage', sa.Float(), nullable=True),
    sa.Column('fees', sa.Float(), nullable=True),
    sa.Column('net_pnl', sa.Float(), nullable=True),
    sa.Column('stop_loss_price', sa.Float(), nullable=True),
    sa.Column('take_profit_price', sa.Float(), nullable=True),
    sa.Column('max_favorable_excursion', sa.Float(), nullable=True),
    sa.Column('max_adverse_excursion', sa.Float(), nullable=True),
    sa.Column('duration_hours', sa.Float(), nullable=True),
    sa.Column('duration_bars', sa.Integer(), nullable=True),
    sa.Column('technical_score', sa.Float(), nullable=True),
    sa.Column('pattern_score', sa.Float(), nullable=True),
    sa.Column('ai_score', sa.Float(), nullable=True),
    sa.Column('market_score', sa.Float(), nullable=True),
    sa.Column('overall_score', sa.Float(), nullable=True),
    sa.Column('is_winning_trade', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['backtest_run_id'], ['backtest_runs.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_backtest_trades_id'), 'backtest_trades', ['id'], unique=False)
    op.create_index(op.f('ix_backtest_trades_symbol'), 'backtest_trades', ['symbol'], unique=False)
    op.create_index(op.f('ix_backtest_trades_trade_uuid'), 'backtest_trades', ['trade_uuid'], unique=True)
    op.create_table('walk_forward_periods',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('analysis_id', sa.Integer(), nullable=False),
    sa.Column('period_id', sa.Integer(), nullable=False),
    sa.Column('in_sample_start', sa.DateTime(), nullable=False),
    sa.Column('in_sample_end', sa.DateTime(), nullable=False),
    sa.Column('out_sample_start', sa.DateTime(), nullable=False),
    sa.Column('out_sample_end', sa.DateTime(), nullable=False),
    sa.Column('optimized_parameters', sa.JSON(), nullable=True),
    sa.Column('in_sample_return', sa.Float(), nullable=True),
    sa.Column('in_sample_return_pct', sa.Float(), nullable=True),
    sa.Column('in_sample_sharpe', sa.Float(), nullable=True),
    sa.Column('in_sample_max_drawdown', sa.Float(), nullable=True),
    sa.Column('in_sample_trades', sa.Integer(), nullable=True),
    sa.Column('in_sample_win_rate', sa.Float(), nullable=True),
    sa.Column('out_sample_return', sa.Float(), nullable=True),
    sa.Column('out_sample_return_pct', sa.Float(), nullable=True),
    sa.Column('out_sample_sharpe', sa.Float(), nullable=True),
    sa.Column('out_sample_max_drawdown', sa.Float(), nullable=True),
    sa.Column('out_sample_trades', sa.Integer(), nullable=True),
    sa.Column('out_sample_win_rate', sa.Float(), nullable=True),
    sa.Column('performance_degradation', sa.Float(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['analysis_id'], ['walk_forward_analyses.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_walk_forward_periods_id'), 'walk_forward_periods', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_walk_forward_periods_id'), table_name='walk_forward_periods')
    op.drop_table('walk_forward_periods')
    op.drop_index(op.f('ix_backtest_trades_trade_uuid'), table_name='backtest_trades')
    op.drop_index(op.f('ix_backtest_trades_symbol'), table_name='backtest_trades')
    op.drop_index(op.f('ix_backtest_trades_id'), table_name='backtest_trades')
    op.drop_table('backtest_trades')
    op.drop_index(op.f('ix_walk_forward_analyses_id'), table_name='walk_forward_analyses')
    op.drop_index(op.f('ix_walk_forward_analyses_analysis_uuid'), table_name='walk_forward_analyses')
    op.drop_table('walk_forward_analyses')
    op.drop_index(op.f('ix_backtest_runs_run_uuid'), table_name='backtest_runs')
    op.drop_index(op.f('ix_backtest_runs_id'), table_name='backtest_runs')
    op.drop_table('backtest_runs')
    op.drop_index(op.f('ix_backtest_strategies_strategy_uuid'), table_name='backtest_strategies')
    op.drop_index(op.f('ix_backtest_strategies_id'), table_name='backtest_strategies')
    op.drop_table('backtest_strategies')
    # ### end Alembic commands ###

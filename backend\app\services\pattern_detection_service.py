"""
Advanced Pattern Detection Service
Author: inkbytefo
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging
from scipy.signal import find_peaks, argrelextrema
from scipy.stats import linregress
# import talib  # Using pandas-based implementations instead

from app.services.market_data_service import MarketDataService
from app.core.database import cache_manager
from app.core.config import settings

logger = logging.getLogger("pattern_detection")


@dataclass
class SupportResistanceLevel:
    """Support/Resistance level data"""
    price: float
    strength: float  # 0-1, based on number of touches
    level_type: str  # "support" or "resistance"
    first_touch: datetime
    last_touch: datetime
    touch_count: int
    confidence: float  # 0-1


@dataclass
class TrendLine:
    """Trend line data"""
    start_price: float
    end_price: float
    start_time: datetime
    end_time: datetime
    slope: float
    r_squared: float  # Correlation strength
    line_type: str  # "support_trendline", "resistance_trendline"
    touch_points: List[Tuple[datetime, float]]
    is_broken: bool = False
    break_time: Optional[datetime] = None


@dataclass
class ChartPattern:
    """Chart pattern data"""
    pattern_name: str
    pattern_type: str  # "reversal", "continuation", "neutral"
    start_time: datetime
    end_time: datetime
    confidence: float  # 0-1
    target_price: Optional[float]
    stop_loss: Optional[float]
    breakout_direction: str  # "bullish", "bearish", "unknown"
    key_points: List[Tuple[datetime, float]]
    description: str
    completion_percentage: float  # 0-1, how complete the pattern is


@dataclass
class FibonacciLevel:
    """Fibonacci retracement level"""
    level: float  # 0.236, 0.382, 0.5, 0.618, 0.786
    price: float
    level_type: str  # "retracement", "extension"
    is_active: bool
    touches: int


@dataclass
class PatternAnalysisResult:
    """Complete pattern analysis result"""
    symbol: str
    timeframe: str
    analysis_time: datetime
    
    # Support/Resistance
    support_levels: List[SupportResistanceLevel]
    resistance_levels: List[SupportResistanceLevel]
    
    # Trend Lines
    trend_lines: List[TrendLine]
    
    # Chart Patterns
    chart_patterns: List[ChartPattern]
    
    # Fibonacci Levels
    fibonacci_levels: List[FibonacciLevel]
    
    # Overall Assessment
    overall_trend: str  # "bullish", "bearish", "sideways"
    trend_strength: float  # 0-1
    pattern_reliability: float  # 0-1
    next_key_levels: List[float]


class PatternDetectionService:
    """Advanced pattern detection and analysis service"""
    
    def __init__(self):
        self.min_touches_for_level = 2
        self.level_tolerance = 0.002  # 0.2% tolerance for level detection
        self.min_pattern_bars = 10
        self.fibonacci_levels = [0.236, 0.382, 0.5, 0.618, 0.786]
        
    async def analyze_patterns(
        self,
        symbol: str,
        timeframe: str = "1h",
        limit: int = 500,
        exchange: str = "binance"
    ) -> Optional[PatternAnalysisResult]:
        """Comprehensive pattern analysis"""
        try:
            cache_key = f"pattern_analysis:{symbol}:{timeframe}:{exchange}"
            cached_result = await cache_manager.get(cache_key)
            
            if cached_result:
                logger.info(f"Using cached pattern analysis for {symbol}")
                return cached_result
            
            # Get market data
            df = await market_data_service.get_ohlcv_data(
                symbol=symbol,
                timeframe=timeframe,
                limit=limit,
                exchange=exchange
            )
            
            if df is None or len(df) < 50:
                logger.warning(f"Insufficient data for pattern analysis: {symbol}")
                return None
            
            # Detect support/resistance levels
            support_levels, resistance_levels = await self._detect_support_resistance(df)
            
            # Detect trend lines
            trend_lines = await self._detect_trend_lines(df)
            
            # Detect chart patterns
            chart_patterns = await self._detect_chart_patterns(df)
            
            # Calculate Fibonacci levels
            fibonacci_levels = await self._calculate_fibonacci_levels(df)
            
            # Overall trend analysis
            overall_trend, trend_strength = await self._analyze_overall_trend(df, trend_lines)
            
            # Pattern reliability assessment
            pattern_reliability = await self._assess_pattern_reliability(
                support_levels, resistance_levels, trend_lines, chart_patterns
            )
            
            # Next key levels
            next_key_levels = await self._identify_next_key_levels(
                df, support_levels, resistance_levels, fibonacci_levels
            )
            
            result = PatternAnalysisResult(
                symbol=symbol,
                timeframe=timeframe,
                analysis_time=datetime.now(),
                support_levels=support_levels,
                resistance_levels=resistance_levels,
                trend_lines=trend_lines,
                chart_patterns=chart_patterns,
                fibonacci_levels=fibonacci_levels,
                overall_trend=overall_trend,
                trend_strength=trend_strength,
                pattern_reliability=pattern_reliability,
                next_key_levels=next_key_levels
            )
            
            # Cache result for 10 minutes
            await cache_manager.set(cache_key, result, ttl=600)
            
            logger.info(f"Pattern analysis completed for {symbol}")
            return result
            
        except Exception as e:
            logger.error(f"Error in pattern analysis for {symbol}: {e}")
            return None
    
    async def _detect_support_resistance(self, df: pd.DataFrame) -> Tuple[List[SupportResistanceLevel], List[SupportResistanceLevel]]:
        """Detect support and resistance levels"""
        try:
            support_levels = []
            resistance_levels = []
            
            # Find local minima and maxima
            highs = df['high'].values
            lows = df['low'].values
            closes = df['close'].values
            timestamps = df.index
            
            # Find peaks (resistance) and valleys (support)
            resistance_indices, _ = find_peaks(highs, distance=5, prominence=np.std(highs) * 0.5)
            support_indices, _ = find_peaks(-lows, distance=5, prominence=np.std(lows) * 0.5)
            
            # Group nearby levels
            resistance_groups = self._group_nearby_levels(highs[resistance_indices], resistance_indices, timestamps)
            support_groups = self._group_nearby_levels(lows[support_indices], support_indices, timestamps)
            
            # Create resistance levels
            for group in resistance_groups:
                if len(group['indices']) >= self.min_touches_for_level:
                    level = SupportResistanceLevel(
                        price=group['avg_price'],
                        strength=min(1.0, len(group['indices']) / 10.0),
                        level_type="resistance",
                        first_touch=group['first_time'],
                        last_touch=group['last_time'],
                        touch_count=len(group['indices']),
                        confidence=self._calculate_level_confidence(group, df)
                    )
                    resistance_levels.append(level)
            
            # Create support levels
            for group in support_groups:
                if len(group['indices']) >= self.min_touches_for_level:
                    level = SupportResistanceLevel(
                        price=group['avg_price'],
                        strength=min(1.0, len(group['indices']) / 10.0),
                        level_type="support",
                        first_touch=group['first_time'],
                        last_touch=group['last_time'],
                        touch_count=len(group['indices']),
                        confidence=self._calculate_level_confidence(group, df)
                    )
                    support_levels.append(level)
            
            # Sort by strength
            support_levels.sort(key=lambda x: x.strength, reverse=True)
            resistance_levels.sort(key=lambda x: x.strength, reverse=True)
            
            return support_levels[:5], resistance_levels[:5]  # Return top 5 of each
            
        except Exception as e:
            logger.error(f"Error detecting support/resistance: {e}")
            return [], []
    
    def _group_nearby_levels(self, prices: np.ndarray, indices: np.ndarray, timestamps: pd.Index) -> List[Dict]:
        """Group nearby price levels together"""
        groups = []
        used_indices = set()
        
        for i, (price, idx) in enumerate(zip(prices, indices)):
            if idx in used_indices:
                continue
                
            # Find nearby levels
            group_indices = [idx]
            group_prices = [price]
            used_indices.add(idx)
            
            for j, (other_price, other_idx) in enumerate(zip(prices, indices)):
                if other_idx in used_indices:
                    continue
                    
                # Check if prices are within tolerance
                if abs(price - other_price) / price <= self.level_tolerance:
                    group_indices.append(other_idx)
                    group_prices.append(other_price)
                    used_indices.add(other_idx)
            
            if len(group_indices) >= self.min_touches_for_level:
                groups.append({
                    'indices': group_indices,
                    'prices': group_prices,
                    'avg_price': np.mean(group_prices),
                    'first_time': timestamps[min(group_indices)],
                    'last_time': timestamps[max(group_indices)]
                })
        
        return groups
    
    def _calculate_level_confidence(self, group: Dict, df: pd.DataFrame) -> float:
        """Calculate confidence score for a support/resistance level"""
        try:
            # Factors affecting confidence:
            # 1. Number of touches
            # 2. Time span
            # 3. Volume at touches
            # 4. Price precision (how close touches are)
            
            touch_count = len(group['indices'])
            time_span = (group['last_time'] - group['first_time']).total_seconds() / 3600  # hours
            
            # Base confidence from touch count
            confidence = min(0.9, touch_count / 10.0)
            
            # Bonus for longer time span
            if time_span > 24:  # More than 1 day
                confidence += 0.1
            
            # Check volume at touch points
            if 'volume' in df.columns:
                touch_volumes = df.iloc[group['indices']]['volume'].values
                avg_volume = df['volume'].mean()
                if np.mean(touch_volumes) > avg_volume:
                    confidence += 0.1
            
            # Price precision bonus
            price_std = np.std(group['prices'])
            avg_price = np.mean(group['prices'])
            if price_std / avg_price < 0.001:  # Very precise touches
                confidence += 0.1
            
            return min(1.0, confidence)
            
        except Exception as e:
            logger.error(f"Error calculating level confidence: {e}")
            return 0.5
    
    async def _detect_trend_lines(self, df: pd.DataFrame) -> List[TrendLine]:
        """Detect trend lines"""
        try:
            trend_lines = []
            
            # Get highs and lows
            highs = df['high'].values
            lows = df['low'].values
            timestamps = df.index
            
            # Find significant peaks and valleys
            high_peaks, _ = find_peaks(highs, distance=10, prominence=np.std(highs) * 0.3)
            low_peaks, _ = find_peaks(-lows, distance=10, prominence=np.std(lows) * 0.3)
            
            # Detect resistance trend lines (connecting highs)
            resistance_trendlines = self._find_trend_lines(
                highs[high_peaks], high_peaks, timestamps, "resistance_trendline"
            )
            trend_lines.extend(resistance_trendlines)
            
            # Detect support trend lines (connecting lows)
            support_trendlines = self._find_trend_lines(
                lows[low_peaks], low_peaks, timestamps, "support_trendline"
            )
            trend_lines.extend(support_trendlines)
            
            # Filter by quality
            quality_trend_lines = [tl for tl in trend_lines if tl.r_squared > 0.7 and len(tl.touch_points) >= 3]
            
            return quality_trend_lines[:10]  # Return top 10
            
        except (ValueError, IndexError) as e:
            logger.error(f"Data error detecting trend lines: {e}")
            return []
        except (np.linalg.LinAlgError, RuntimeError) as e:
            logger.error(f"Calculation error detecting trend lines: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error detecting trend lines: {e}")
            return []
    
    def _find_trend_lines(self, prices: np.ndarray, indices: np.ndarray, timestamps: pd.Index, line_type: str) -> List[TrendLine]:
        """Find trend lines connecting price points"""
        trend_lines = []
        
        if len(prices) < 3:
            return trend_lines
        
        # Try different combinations of points
        for i in range(len(prices) - 2):
            for j in range(i + 2, len(prices)):
                # Get points for trend line
                x_points = indices[i:j+1]
                y_points = prices[i:j+1]
                
                if len(x_points) < 3:
                    continue
                
                # Calculate linear regression
                try:
                    slope, intercept, r_value, p_value, std_err = linregress(x_points, y_points)
                    r_squared = r_value ** 2
                    
                    if r_squared > 0.6:  # Minimum correlation
                        # Count touch points (points close to the line)
                        touch_points = []
                        for k, (x, y) in enumerate(zip(x_points, y_points)):
                            predicted_y = slope * x + intercept
                            if abs(y - predicted_y) / y <= 0.01:  # Within 1%
                                touch_points.append((timestamps[x], y))
                        
                        if len(touch_points) >= 3:
                            trend_line = TrendLine(
                                start_price=prices[i],
                                end_price=prices[j],
                                start_time=timestamps[indices[i]],
                                end_time=timestamps[indices[j]],
                                slope=slope,
                                r_squared=r_squared,
                                line_type=line_type,
                                touch_points=touch_points
                            )
                            trend_lines.append(trend_line)
                            
                except Exception as e:
                    continue
        
        return trend_lines
    
    async def _detect_chart_patterns(self, df: pd.DataFrame) -> List[ChartPattern]:
        """Detect common chart patterns"""
        try:
            patterns = []
            
            # Double Top/Bottom patterns
            double_patterns = await self._detect_double_patterns(df)
            patterns.extend(double_patterns)
            
            # Head and Shoulders patterns
            hs_patterns = await self._detect_head_shoulders(df)
            patterns.extend(hs_patterns)
            
            # Triangle patterns
            triangle_patterns = await self._detect_triangles(df)
            patterns.extend(triangle_patterns)
            
            # Flag and Pennant patterns
            flag_patterns = await self._detect_flags_pennants(df)
            patterns.extend(flag_patterns)
            
            return patterns
            
        except (ValueError, IndexError, KeyError) as e:
            logger.error(f"Data error detecting chart patterns: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error detecting chart patterns: {e}")
            return []
    
    async def _detect_double_patterns(self, df: pd.DataFrame) -> List[ChartPattern]:
        """Detect double top and double bottom patterns"""
        patterns = []
        
        try:
            highs = df['high'].values
            lows = df['low'].values
            timestamps = df.index
            
            # Find peaks for double tops
            high_peaks, _ = find_peaks(highs, distance=20, prominence=np.std(highs) * 0.5)
            
            # Look for double tops
            for i in range(len(high_peaks) - 1):
                for j in range(i + 1, len(high_peaks)):
                    peak1_idx = high_peaks[i]
                    peak2_idx = high_peaks[j]
                    
                    peak1_price = highs[peak1_idx]
                    peak2_price = highs[peak2_idx]
                    
                    # Check if peaks are similar height (within 2%)
                    if abs(peak1_price - peak2_price) / peak1_price <= 0.02:
                        # Find valley between peaks
                        valley_start = peak1_idx
                        valley_end = peak2_idx
                        valley_idx = np.argmin(lows[valley_start:valley_end]) + valley_start
                        valley_price = lows[valley_idx]
                        
                        # Check if valley is significantly lower (at least 3%)
                        if (peak1_price - valley_price) / peak1_price >= 0.03:
                            pattern = ChartPattern(
                                pattern_name="Double Top",
                                pattern_type="reversal",
                                start_time=timestamps[peak1_idx],
                                end_time=timestamps[peak2_idx],
                                confidence=0.7,
                                target_price=valley_price - (peak1_price - valley_price),
                                stop_loss=max(peak1_price, peak2_price) * 1.02,
                                breakout_direction="bearish",
                                key_points=[
                                    (timestamps[peak1_idx], peak1_price),
                                    (timestamps[valley_idx], valley_price),
                                    (timestamps[peak2_idx], peak2_price)
                                ],
                                description=f"Double top pattern with peaks at ${peak1_price:.2f} and ${peak2_price:.2f}",
                                completion_percentage=1.0
                            )
                            patterns.append(pattern)
            
            # Similar logic for double bottoms
            low_peaks, _ = find_peaks(-lows, distance=20, prominence=np.std(lows) * 0.5)
            
            for i in range(len(low_peaks) - 1):
                for j in range(i + 1, len(low_peaks)):
                    trough1_idx = low_peaks[i]
                    trough2_idx = low_peaks[j]
                    
                    trough1_price = lows[trough1_idx]
                    trough2_price = lows[trough2_idx]
                    
                    if abs(trough1_price - trough2_price) / trough1_price <= 0.02:
                        peak_start = trough1_idx
                        peak_end = trough2_idx
                        peak_idx = np.argmax(highs[peak_start:peak_end]) + peak_start
                        peak_price = highs[peak_idx]
                        
                        if (peak_price - trough1_price) / trough1_price >= 0.03:
                            pattern = ChartPattern(
                                pattern_name="Double Bottom",
                                pattern_type="reversal",
                                start_time=timestamps[trough1_idx],
                                end_time=timestamps[trough2_idx],
                                confidence=0.7,
                                target_price=peak_price + (peak_price - trough1_price),
                                stop_loss=min(trough1_price, trough2_price) * 0.98,
                                breakout_direction="bullish",
                                key_points=[
                                    (timestamps[trough1_idx], trough1_price),
                                    (timestamps[peak_idx], peak_price),
                                    (timestamps[trough2_idx], trough2_price)
                                ],
                                description=f"Double bottom pattern with troughs at ${trough1_price:.2f} and ${trough2_price:.2f}",
                                completion_percentage=1.0
                            )
                            patterns.append(pattern)
            
        except Exception as e:
            logger.error(f"Error detecting double patterns: {e}")
        
        return patterns
    
    async def _detect_head_shoulders(self, df: pd.DataFrame) -> List[ChartPattern]:
        """Detect head and shoulders patterns"""
        patterns = []
        
        try:
            highs = df['high'].values
            lows = df['low'].values
            timestamps = df.index
            
            # Find significant peaks
            peaks, _ = find_peaks(highs, distance=15, prominence=np.std(highs) * 0.4)
            
            # Look for head and shoulders (3 peaks pattern)
            for i in range(len(peaks) - 2):
                left_shoulder_idx = peaks[i]
                head_idx = peaks[i + 1]
                right_shoulder_idx = peaks[i + 2]
                
                left_shoulder = highs[left_shoulder_idx]
                head = highs[head_idx]
                right_shoulder = highs[right_shoulder_idx]
                
                # Head should be higher than both shoulders
                if head > left_shoulder and head > right_shoulder:
                    # Shoulders should be roughly equal (within 3%)
                    if abs(left_shoulder - right_shoulder) / left_shoulder <= 0.03:
                        # Find neckline (valleys between peaks)
                        left_valley_idx = np.argmin(lows[left_shoulder_idx:head_idx]) + left_shoulder_idx
                        right_valley_idx = np.argmin(lows[head_idx:right_shoulder_idx]) + head_idx
                        
                        left_valley = lows[left_valley_idx]
                        right_valley = lows[right_valley_idx]
                        neckline = (left_valley + right_valley) / 2
                        
                        # Calculate target price
                        head_to_neckline = head - neckline
                        target_price = neckline - head_to_neckline
                        
                        pattern = ChartPattern(
                            pattern_name="Head and Shoulders",
                            pattern_type="reversal",
                            start_time=timestamps[left_shoulder_idx],
                            end_time=timestamps[right_shoulder_idx],
                            confidence=0.75,
                            target_price=target_price,
                            stop_loss=head * 1.02,
                            breakout_direction="bearish",
                            key_points=[
                                (timestamps[left_shoulder_idx], left_shoulder),
                                (timestamps[left_valley_idx], left_valley),
                                (timestamps[head_idx], head),
                                (timestamps[right_valley_idx], right_valley),
                                (timestamps[right_shoulder_idx], right_shoulder)
                            ],
                            description=f"Head and shoulders pattern with head at ${head:.2f}",
                            completion_percentage=1.0
                        )
                        patterns.append(pattern)
            
        except Exception as e:
            logger.error(f"Error detecting head and shoulders: {e}")

        return patterns

    async def _detect_triangles(self, df: pd.DataFrame) -> List[ChartPattern]:
        """Detect triangle patterns (ascending, descending, symmetrical)"""
        patterns = []

        try:
            highs = df['high'].values
            lows = df['low'].values
            timestamps = df.index

            # Look for triangle patterns in recent data (last 100 bars)
            recent_bars = min(100, len(df))
            recent_highs = highs[-recent_bars:]
            recent_lows = lows[-recent_bars:]
            recent_timestamps = timestamps[-recent_bars:]

            # Find peaks and valleys
            high_peaks, _ = find_peaks(recent_highs, distance=5)
            low_peaks, _ = find_peaks(-recent_lows, distance=5)

            if len(high_peaks) >= 2 and len(low_peaks) >= 2:
                # Get trend lines for highs and lows
                high_trend = self._calculate_trend_slope(high_peaks, recent_highs[high_peaks])
                low_trend = self._calculate_trend_slope(low_peaks, recent_lows[low_peaks])

                if high_trend and low_trend:
                    # Determine triangle type
                    triangle_type = "symmetrical"
                    if high_trend['slope'] < -0.001 and abs(low_trend['slope']) < 0.001:
                        triangle_type = "descending"
                    elif abs(high_trend['slope']) < 0.001 and low_trend['slope'] > 0.001:
                        triangle_type = "ascending"

                    # Calculate convergence point
                    convergence_bar = self._find_convergence_point(high_trend, low_trend)

                    if convergence_bar and convergence_bar > 0:
                        current_price = df['close'].iloc[-1]

                        # Determine breakout direction
                        if triangle_type == "ascending":
                            breakout_direction = "bullish"
                            target_price = current_price * 1.05
                        elif triangle_type == "descending":
                            breakout_direction = "bearish"
                            target_price = current_price * 0.95
                        else:
                            breakout_direction = "unknown"
                            target_price = None

                        pattern = ChartPattern(
                            pattern_name=f"{triangle_type.title()} Triangle",
                            pattern_type="continuation" if triangle_type == "symmetrical" else "continuation",
                            start_time=recent_timestamps[0],
                            end_time=recent_timestamps[-1],
                            confidence=0.6,
                            target_price=target_price,
                            stop_loss=current_price * (0.97 if breakout_direction == "bullish" else 1.03),
                            breakout_direction=breakout_direction,
                            key_points=[
                                (recent_timestamps[high_peaks[0]], recent_highs[high_peaks[0]]),
                                (recent_timestamps[high_peaks[-1]], recent_highs[high_peaks[-1]]),
                                (recent_timestamps[low_peaks[0]], recent_lows[low_peaks[0]]),
                                (recent_timestamps[low_peaks[-1]], recent_lows[low_peaks[-1]])
                            ],
                            description=f"{triangle_type.title()} triangle pattern forming",
                            completion_percentage=0.8
                        )
                        patterns.append(pattern)

        except Exception as e:
            logger.error(f"Error detecting triangles: {e}")

        return patterns

    def _calculate_trend_slope(self, indices: np.ndarray, prices: np.ndarray) -> Optional[Dict]:
        """Calculate trend line slope"""
        try:
            if len(indices) < 2:
                return None

            slope, intercept, r_value, p_value, std_err = linregress(indices, prices)

            return {
                'slope': slope,
                'intercept': intercept,
                'r_squared': r_value ** 2
            }
        except:
            return None

    def _find_convergence_point(self, high_trend: Dict, low_trend: Dict) -> Optional[int]:
        """Find where two trend lines converge"""
        try:
            # Solve for intersection: high_slope * x + high_intercept = low_slope * x + low_intercept
            slope_diff = high_trend['slope'] - low_trend['slope']

            if abs(slope_diff) < 0.0001:  # Lines are parallel
                return None

            intercept_diff = low_trend['intercept'] - high_trend['intercept']
            convergence_x = intercept_diff / slope_diff

            return int(convergence_x) if convergence_x > 0 else None
        except:
            return None

    async def _detect_flags_pennants(self, df: pd.DataFrame) -> List[ChartPattern]:
        """Detect flag and pennant patterns"""
        patterns = []

        try:
            # Look for strong moves followed by consolidation
            closes = df['close'].values
            volumes = df['volume'].values if 'volume' in df.columns else None
            timestamps = df.index

            # Find strong moves (flagpoles)
            for i in range(20, len(closes) - 20):
                # Check for strong upward move
                move_start = i - 20
                move_end = i
                flagpole_start_price = closes[move_start]
                flagpole_end_price = closes[move_end]

                move_percentage = (flagpole_end_price - flagpole_start_price) / flagpole_start_price

                # Strong move threshold (5% in 20 bars)
                if abs(move_percentage) > 0.05:
                    # Look for consolidation after the move
                    consolidation_start = i
                    consolidation_end = min(i + 15, len(closes) - 1)

                    consolidation_prices = closes[consolidation_start:consolidation_end]
                    consolidation_range = (np.max(consolidation_prices) - np.min(consolidation_prices)) / np.mean(consolidation_prices)

                    # Check if consolidation is tight (less than 3% range)
                    if consolidation_range < 0.03:
                        # Check volume pattern (should decrease during consolidation)
                        volume_confirmation = True
                        if volumes is not None:
                            flagpole_volume = np.mean(volumes[move_start:move_end])
                            consolidation_volume = np.mean(volumes[consolidation_start:consolidation_end])
                            volume_confirmation = consolidation_volume < flagpole_volume * 0.8

                        if volume_confirmation:
                            pattern_type = "Bull Flag" if move_percentage > 0 else "Bear Flag"
                            breakout_direction = "bullish" if move_percentage > 0 else "bearish"

                            # Calculate target
                            flagpole_height = abs(flagpole_end_price - flagpole_start_price)
                            current_price = closes[consolidation_end]

                            if move_percentage > 0:
                                target_price = current_price + flagpole_height
                                stop_loss = current_price * 0.95
                            else:
                                target_price = current_price - flagpole_height
                                stop_loss = current_price * 1.05

                            pattern = ChartPattern(
                                pattern_name=pattern_type,
                                pattern_type="continuation",
                                start_time=timestamps[move_start],
                                end_time=timestamps[consolidation_end],
                                confidence=0.65,
                                target_price=target_price,
                                stop_loss=stop_loss,
                                breakout_direction=breakout_direction,
                                key_points=[
                                    (timestamps[move_start], flagpole_start_price),
                                    (timestamps[move_end], flagpole_end_price),
                                    (timestamps[consolidation_start], closes[consolidation_start]),
                                    (timestamps[consolidation_end], closes[consolidation_end])
                                ],
                                description=f"{pattern_type} pattern with {move_percentage*100:.1f}% flagpole",
                                completion_percentage=0.9
                            )
                            patterns.append(pattern)

        except Exception as e:
            logger.error(f"Error detecting flags/pennants: {e}")

        return patterns

    async def _calculate_fibonacci_levels(self, df: pd.DataFrame) -> List[FibonacciLevel]:
        """Calculate Fibonacci retracement levels"""
        fibonacci_levels = []

        try:
            # Find significant swing high and low in recent data
            recent_bars = min(100, len(df))
            recent_highs = df['high'].tail(recent_bars).values
            recent_lows = df['low'].tail(recent_bars).values

            swing_high = np.max(recent_highs)
            swing_low = np.min(recent_lows)

            swing_range = swing_high - swing_low

            # Calculate retracement levels
            for level in self.fibonacci_levels:
                retracement_price = swing_high - (swing_range * level)

                # Check if level is active (price has interacted with it)
                current_price = df['close'].iloc[-1]
                is_active = abs(current_price - retracement_price) / current_price <= 0.01

                # Count touches
                touches = 0
                for price in df['close'].tail(50):
                    if abs(price - retracement_price) / retracement_price <= 0.005:
                        touches += 1

                fib_level = FibonacciLevel(
                    level=level,
                    price=retracement_price,
                    level_type="retracement",
                    is_active=is_active,
                    touches=touches
                )
                fibonacci_levels.append(fib_level)

            # Calculate extension levels
            extension_levels = [1.272, 1.414, 1.618]
            for level in extension_levels:
                extension_price = swing_high + (swing_range * (level - 1))

                fib_level = FibonacciLevel(
                    level=level,
                    price=extension_price,
                    level_type="extension",
                    is_active=False,
                    touches=0
                )
                fibonacci_levels.append(fib_level)

        except Exception as e:
            logger.error(f"Error calculating Fibonacci levels: {e}")

        return fibonacci_levels

    async def _analyze_overall_trend(self, df: pd.DataFrame, trend_lines: List[TrendLine]) -> Tuple[str, float]:
        """Analyze overall trend direction and strength"""
        try:
            # Multiple methods to determine trend
            trend_signals = []

            # 1. Moving average trend
            if len(df) >= 50:
                sma_20 = df['close'].rolling(20).mean().iloc[-1]
                sma_50 = df['close'].rolling(50).mean().iloc[-1]
                current_price = df['close'].iloc[-1]

                if current_price > sma_20 > sma_50:
                    trend_signals.append(("bullish", 0.8))
                elif current_price < sma_20 < sma_50:
                    trend_signals.append(("bearish", 0.8))
                else:
                    trend_signals.append(("sideways", 0.5))

            # 2. Price action trend (higher highs, higher lows)
            recent_highs = df['high'].tail(20).values
            recent_lows = df['low'].tail(20).values

            if len(recent_highs) >= 10:
                high_trend = np.polyfit(range(len(recent_highs)), recent_highs, 1)[0]
                low_trend = np.polyfit(range(len(recent_lows)), recent_lows, 1)[0]

                if high_trend > 0 and low_trend > 0:
                    trend_signals.append(("bullish", 0.7))
                elif high_trend < 0 and low_trend < 0:
                    trend_signals.append(("bearish", 0.7))
                else:
                    trend_signals.append(("sideways", 0.4))

            # 3. Trend line analysis
            if trend_lines:
                bullish_trendlines = [tl for tl in trend_lines if tl.line_type == "support_trendline" and tl.slope > 0]
                bearish_trendlines = [tl for tl in trend_lines if tl.line_type == "resistance_trendline" and tl.slope < 0]

                if len(bullish_trendlines) > len(bearish_trendlines):
                    trend_signals.append(("bullish", 0.6))
                elif len(bearish_trendlines) > len(bullish_trendlines):
                    trend_signals.append(("bearish", 0.6))
                else:
                    trend_signals.append(("sideways", 0.3))

            # Combine signals
            if not trend_signals:
                return "sideways", 0.3

            bullish_strength = sum(strength for trend, strength in trend_signals if trend == "bullish")
            bearish_strength = sum(strength for trend, strength in trend_signals if trend == "bearish")
            sideways_strength = sum(strength for trend, strength in trend_signals if trend == "sideways")

            max_strength = max(bullish_strength, bearish_strength, sideways_strength)

            if max_strength == bullish_strength:
                return "bullish", min(1.0, bullish_strength / len(trend_signals))
            elif max_strength == bearish_strength:
                return "bearish", min(1.0, bearish_strength / len(trend_signals))
            else:
                return "sideways", min(1.0, sideways_strength / len(trend_signals))

        except Exception as e:
            logger.error(f"Error analyzing overall trend: {e}")
            return "sideways", 0.3

    async def _assess_pattern_reliability(
        self,
        support_levels: List[SupportResistanceLevel],
        resistance_levels: List[SupportResistanceLevel],
        trend_lines: List[TrendLine],
        chart_patterns: List[ChartPattern]
    ) -> float:
        """Assess overall pattern reliability"""
        try:
            reliability_factors = []

            # Support/Resistance reliability
            if support_levels or resistance_levels:
                avg_confidence = np.mean([level.confidence for level in support_levels + resistance_levels])
                reliability_factors.append(avg_confidence)

            # Trend line reliability
            if trend_lines:
                avg_r_squared = np.mean([tl.r_squared for tl in trend_lines])
                reliability_factors.append(avg_r_squared)

            # Chart pattern reliability
            if chart_patterns:
                avg_pattern_confidence = np.mean([pattern.confidence for pattern in chart_patterns])
                reliability_factors.append(avg_pattern_confidence)

            # Multiple confirmation bonus
            if len(reliability_factors) >= 2:
                base_reliability = np.mean(reliability_factors)
                confirmation_bonus = 0.1 * (len(reliability_factors) - 1)
                return min(1.0, base_reliability + confirmation_bonus)
            elif len(reliability_factors) == 1:
                return reliability_factors[0]
            else:
                return 0.3

        except Exception as e:
            logger.error(f"Error assessing pattern reliability: {e}")
            return 0.3

    async def _identify_next_key_levels(
        self,
        df: pd.DataFrame,
        support_levels: List[SupportResistanceLevel],
        resistance_levels: List[SupportResistanceLevel],
        fibonacci_levels: List[FibonacciLevel]
    ) -> List[float]:
        """Identify next key price levels"""
        try:
            current_price = df['close'].iloc[-1]
            key_levels = []

            # Add support levels below current price
            for level in support_levels:
                if level.price < current_price:
                    key_levels.append(level.price)

            # Add resistance levels above current price
            for level in resistance_levels:
                if level.price > current_price:
                    key_levels.append(level.price)

            # Add active Fibonacci levels
            for fib_level in fibonacci_levels:
                if fib_level.is_active or fib_level.touches > 0:
                    key_levels.append(fib_level.price)

            # Remove duplicates and sort
            key_levels = list(set(key_levels))
            key_levels.sort()

            # Return closest levels (3 above, 3 below)
            levels_below = [level for level in key_levels if level < current_price][-3:]
            levels_above = [level for level in key_levels if level > current_price][:3]

            return levels_below + levels_above

        except Exception as e:
            logger.error(f"Error identifying key levels: {e}")
            return []


# Global instance
pattern_detection_service = PatternDetectionService()

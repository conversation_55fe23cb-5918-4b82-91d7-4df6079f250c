#!/usr/bin/env python3
"""
Database Setup and Optimization Script for AI Crypto Trading System
Author: inkbytefo

This script provides comprehensive database setup, optimization, and maintenance tools.
"""

import sys
import os
import asyncio
import logging
import psycopg2
from pathlib import Path
from typing import Dict, List, Optional
import argparse

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from app.core.database import init_database, check_database_connection, get_db_url
from app.core.config import settings

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("database_setup")


class DatabaseSetup:
    """Database setup and management class"""
    
    def __init__(self):
        self.db_url = get_db_url()
        
    async def create_database_if_not_exists(self) -> bool:
        """Create database if it doesn't exist"""
        try:
            # Parse database URL to get connection details
            from urllib.parse import urlparse
            parsed = urlparse(self.db_url)
            
            # Connect to postgres database to create our database
            conn_params = {
                'host': parsed.hostname,
                'port': parsed.port or 5432,
                'user': parsed.username,
                'password': parsed.password,
                'database': 'postgres'  # Connect to default postgres database
            }
            
            db_name = parsed.path.lstrip('/')
            
            print(f"🔍 Checking if database '{db_name}' exists...")
            
            conn = psycopg2.connect(**conn_params)
            conn.autocommit = True
            cursor = conn.cursor()
            
            # Check if database exists
            cursor.execute(
                "SELECT 1 FROM pg_database WHERE datname = %s",
                (db_name,)
            )
            
            if cursor.fetchone():
                print(f"✅ Database '{db_name}' already exists")
                return True
            else:
                print(f"🏗️ Creating database '{db_name}'...")
                cursor.execute(f'CREATE DATABASE "{db_name}"')
                print(f"✅ Database '{db_name}' created successfully")
                return True
                
        except Exception as e:
            logger.error(f"Error creating database: {e}")
            return False
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()
    
    async def run_sql_file(self, sql_file_path: str) -> bool:
        """Run SQL file against the database"""
        try:
            if not os.path.exists(sql_file_path):
                logger.error(f"SQL file not found: {sql_file_path}")
                return False
            
            print(f"📄 Running SQL file: {sql_file_path}")
            
            # Parse database URL
            from urllib.parse import urlparse
            parsed = urlparse(self.db_url)
            
            conn_params = {
                'host': parsed.hostname,
                'port': parsed.port or 5432,
                'user': parsed.username,
                'password': parsed.password,
                'database': parsed.path.lstrip('/')
            }
            
            conn = psycopg2.connect(**conn_params)
            cursor = conn.cursor()
            
            # Read and execute SQL file
            with open(sql_file_path, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            cursor.execute(sql_content)
            conn.commit()
            
            print(f"✅ SQL file executed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error running SQL file: {e}")
            return False
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()
    
    async def optimize_database(self) -> bool:
        """Optimize database performance"""
        try:
            print("🚀 Optimizing database performance...")
            
            optimization_queries = [
                # Update table statistics
                "ANALYZE;",
                
                # Vacuum tables
                "VACUUM ANALYZE;",
                
                # Create additional performance indexes
                """
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_market_data_symbol_timestamp 
                ON market_data (symbol, timestamp DESC);
                """,
                
                """
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trades_user_created 
                ON trades (user_id, created_at DESC);
                """,
                
                """
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_portfolio_holdings_portfolio_symbol 
                ON portfolio_holdings (portfolio_id, symbol);
                """,
                
                """
                CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_system_logs_timestamp 
                ON system_logs (timestamp DESC);
                """,
                
                # Set optimal PostgreSQL settings
                "SET shared_preload_libraries = 'pg_stat_statements';",
                "SET track_activity_query_size = 2048;",
                "SET log_min_duration_statement = 1000;",
            ]
            
            from urllib.parse import urlparse
            parsed = urlparse(self.db_url)
            
            conn_params = {
                'host': parsed.hostname,
                'port': parsed.port or 5432,
                'user': parsed.username,
                'password': parsed.password,
                'database': parsed.path.lstrip('/')
            }
            
            conn = psycopg2.connect(**conn_params)
            cursor = conn.cursor()
            
            for query in optimization_queries:
                try:
                    cursor.execute(query)
                    conn.commit()
                except Exception as e:
                    logger.warning(f"Optimization query failed (non-critical): {e}")
                    conn.rollback()
            
            print("✅ Database optimization completed")
            return True
            
        except Exception as e:
            logger.error(f"Error optimizing database: {e}")
            return False
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()
    
    async def backup_database(self, backup_path: str) -> bool:
        """Create database backup"""
        try:
            print(f"💾 Creating database backup: {backup_path}")
            
            from urllib.parse import urlparse
            parsed = urlparse(self.db_url)
            
            # Use pg_dump for backup
            import subprocess
            
            env = os.environ.copy()
            env['PGPASSWORD'] = parsed.password
            
            cmd = [
                'pg_dump',
                '-h', parsed.hostname,
                '-p', str(parsed.port or 5432),
                '-U', parsed.username,
                '-d', parsed.path.lstrip('/'),
                '-f', backup_path,
                '--verbose'
            ]
            
            result = subprocess.run(cmd, env=env, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ Database backup created successfully: {backup_path}")
                return True
            else:
                logger.error(f"Backup failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Error creating backup: {e}")
            return False
    
    async def get_database_stats(self) -> Dict:
        """Get database statistics"""
        try:
            from urllib.parse import urlparse
            parsed = urlparse(self.db_url)
            
            conn_params = {
                'host': parsed.hostname,
                'port': parsed.port or 5432,
                'user': parsed.username,
                'password': parsed.password,
                'database': parsed.path.lstrip('/')
            }
            
            conn = psycopg2.connect(**conn_params)
            cursor = conn.cursor()
            
            stats = {}
            
            # Get database size
            cursor.execute("SELECT pg_size_pretty(pg_database_size(current_database()));")
            stats['database_size'] = cursor.fetchone()[0]
            
            # Get table count
            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.tables 
                WHERE table_schema = 'public';
            """)
            stats['table_count'] = cursor.fetchone()[0]
            
            # Get largest tables
            cursor.execute("""
                SELECT 
                    schemaname,
                    tablename,
                    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
                FROM pg_tables 
                WHERE schemaname = 'public'
                ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
                LIMIT 5;
            """)
            stats['largest_tables'] = cursor.fetchall()
            
            # Get connection count
            cursor.execute("SELECT count(*) FROM pg_stat_activity;")
            stats['active_connections'] = cursor.fetchone()[0]
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting database stats: {e}")
            return {}
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()


async def main():
    """Main setup function"""
    parser = argparse.ArgumentParser(description='Database Setup and Management')
    parser.add_argument('--action', choices=['setup', 'migrate', 'optimize', 'backup', 'stats'], 
                       default='setup', help='Action to perform')
    parser.add_argument('--backup-path', help='Path for database backup')
    parser.add_argument('--sql-file', help='SQL file to execute')
    
    args = parser.parse_args()
    
    print("🗄️ AI Crypto Trading System - Database Setup")
    print("Author: inkbytefo")
    print("=" * 60)
    
    setup = DatabaseSetup()
    
    if args.action == 'setup':
        # Full setup process
        print("🚀 Running full database setup...")
        
        # Create database if needed
        if not await setup.create_database_if_not_exists():
            print("❌ Failed to create database")
            return 1
        
        # Run init.sql
        init_sql_path = backend_dir / "sql" / "init.sql"
        if init_sql_path.exists():
            if not await setup.run_sql_file(str(init_sql_path)):
                print("❌ Failed to run init.sql")
                return 1
        
        # Run migration
        print("🔄 Running database migration...")
        if not await init_database():
            print("❌ Database migration failed")
            return 1
        
        # Optimize database
        await setup.optimize_database()
        
        print("✅ Database setup completed successfully!")
        
    elif args.action == 'migrate':
        print("🔄 Running database migration...")
        if await init_database():
            print("✅ Migration completed successfully!")
        else:
            print("❌ Migration failed!")
            return 1
    
    elif args.action == 'optimize':
        if await setup.optimize_database():
            print("✅ Database optimization completed!")
        else:
            print("❌ Optimization failed!")
            return 1
    
    elif args.action == 'backup':
        if not args.backup_path:
            print("❌ Backup path required for backup action")
            return 1
        
        if await setup.backup_database(args.backup_path):
            print("✅ Backup completed successfully!")
        else:
            print("❌ Backup failed!")
            return 1
    
    elif args.action == 'stats':
        stats = await setup.get_database_stats()
        if stats:
            print("📊 Database Statistics:")
            print(f"   Size: {stats.get('database_size', 'Unknown')}")
            print(f"   Tables: {stats.get('table_count', 'Unknown')}")
            print(f"   Active Connections: {stats.get('active_connections', 'Unknown')}")
            
            if 'largest_tables' in stats:
                print("   Largest Tables:")
                for schema, table, size in stats['largest_tables']:
                    print(f"     {table}: {size}")
        else:
            print("❌ Failed to get database statistics")
            return 1
    
    return 0


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Setup error: {e}")
        print(f"\n❌ Setup failed with error: {e}")
        sys.exit(1)

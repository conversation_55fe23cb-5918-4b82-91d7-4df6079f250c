"""
Advanced Signal Generation Service
Author: inkbytefo
"""

import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import asyncio

from app.services.technical_analysis_service import technical_analysis_service
from app.services.pattern_detection_service import pattern_detection_service
from app.services.market_data_service import market_data_service
from app.services.market_regime_detector import market_regime_detector, MarketRegime
from app.core.database import cache_manager
from app.core.config import settings
from app.core.logging_config import performance_monitor, trading_context, performance_logger

logger = logging.getLogger("signal_generation")


class SignalType(Enum):
    """Signal types"""
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"
    STRONG_BUY = "strong_buy"
    STRONG_SELL = "strong_sell"


class SignalStrength(Enum):
    """Signal strength levels"""
    WEAK = "weak"
    MODERATE = "moderate"
    STRONG = "strong"
    VERY_STRONG = "very_strong"


class RiskLevel(Enum):
    """Risk levels for position sizing"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"


@dataclass
class SignalFactor:
    """Individual signal factor"""
    name: str
    value: float  # -1 to 1 (bearish to bullish)
    weight: float  # 0 to 1 (importance)
    confidence: float  # 0 to 1
    description: str


@dataclass
class EntryExitPoint:
    """Entry/exit point with optimization"""
    price: float
    confidence: float
    reasoning: str
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    risk_reward_ratio: Optional[float] = None


@dataclass
class PositionSizing:
    """Position sizing recommendation"""
    position_size_percentage: float  # % of portfolio
    position_size_usd: Optional[float] = None
    max_loss_percentage: float = 2.0  # Max loss as % of portfolio
    risk_level: RiskLevel = RiskLevel.MEDIUM
    leverage: float = 1.0
    reasoning: str = ""


@dataclass
class TradingSignal:
    """Complete trading signal"""
    symbol: str
    timeframe: str
    signal_type: SignalType
    signal_strength: SignalStrength
    overall_score: float  # -1 to 1
    confidence: float  # 0 to 1
    
    # Signal factors
    technical_factors: List[SignalFactor]
    pattern_factors: List[SignalFactor]
    market_factors: List[SignalFactor]
    ai_factors: List[SignalFactor]
    
    # Entry/Exit optimization
    entry_point: EntryExitPoint
    exit_points: List[EntryExitPoint]
    
    # Position sizing
    position_sizing: PositionSizing
    
    # Metadata
    generated_at: datetime
    valid_until: datetime
    exchange: str = "binance"
    reasoning: str = ""
    
    # Risk metrics
    max_drawdown_risk: float = 0.0
    volatility_risk: float = 0.0
    market_correlation_risk: float = 0.0


class SignalGenerationService:
    """Advanced signal generation service with dynamic parameter support"""

    def __init__(self):
        # Default parameters - can be overridden during signal generation
        self.default_parameters = {
            # Signal weights
            "signal_weights": {
                "technical": 0.3,
                "pattern": 0.25,
                "market": 0.2,
                "ai": 0.25
            },

            # Technical indicator weights
            "technical_weights": {
                "rsi": 0.2,
                "macd": 0.25,
                "trend": 0.3,
                "volume": 0.15,
                "momentum": 0.1
            },

            # Pattern detection weights
            "pattern_weights": {
                "support_resistance": 0.3,
                "trend_lines": 0.25,
                "chart_patterns": 0.35,
                "fibonacci": 0.1
            },

            # Market factor weights
            "market_weights": {
                "market_trend": 0.4,
                "volatility": 0.3,
                "volume_profile": 0.3
            },

            # AI factor weights
            "ai_weights": {
                "sentiment": 0.4,
                "news_impact": 0.3,
                "market_insights": 0.2,
                "ai_recommendation": 0.1
            },

            # Technical indicator parameters
            "rsi_period": 14,
            "rsi_oversold": 30,
            "rsi_overbought": 70,
            "sma_short": 20,
            "sma_long": 50,
            "ema_fast": 12,
            "ema_slow": 26,
            "bb_period": 20,
            "bb_std": 2.0,
            "macd_fast": 12,
            "macd_slow": 26,
            "macd_signal": 9,
            "volume_threshold": 1.5,

            # Risk parameters
            "max_position_size": 0.1,  # 10% max per trade
            "default_stop_loss": 0.02,  # 2% stop loss
            "min_risk_reward": 1.5,  # Minimum 1.5:1 R/R ratio
            "min_confidence": 0.6,  # Minimum signal confidence
            "stop_loss_pct": 0.05,  # 5% stop loss
            "take_profit_pct": 0.15,  # 15% take profit

            # Market regime parameters
            "enable_regime_detection": True,  # Enable market regime filtering
            "regime_confidence_threshold": 0.6,  # Minimum regime confidence
            "regime_adjustment_strength": 1.0,  # How much to adjust based on regime (0.0-1.0)
        }

        # Current active parameters (will be set during signal generation)
        self.active_parameters = self.default_parameters.copy()
    
    def set_parameters(self, parameters: Dict[str, Any]):
        """Set custom parameters for signal generation"""
        self.active_parameters = self.default_parameters.copy()
        self.active_parameters.update(parameters)

    def get_parameter(self, key: str, default: Any = None) -> Any:
        """Get parameter value with fallback to default"""
        return self.active_parameters.get(key, default)

    @performance_monitor("signal_generation")
    async def generate_signal(
        self,
        symbol: str,
        timeframe: str = "1h",
        exchange: str = "binance",
        portfolio_size: float = 10000.0,
        parameters: Optional[Dict[str, Any]] = None
    ) -> Optional[TradingSignal]:
        """Generate comprehensive trading signal with optional custom parameters"""
        try:
            # Set custom parameters if provided
            if parameters:
                self.set_parameters(parameters)

            with trading_context(
                symbol=symbol,
                timeframe=timeframe,
                exchange=exchange,
                portfolio_size=portfolio_size,
                custom_parameters=bool(parameters)
            ) as ctx:
                ctx.info(f"Starting signal generation for {symbol} ({timeframe}) with {'custom' if parameters else 'default'} parameters")
            
            # Check cache first
            cache_key = f"signal:{symbol}:{timeframe}:{exchange}"
            cached_signal = await cache_manager.get(cache_key)
            if cached_signal:
                logger.info(f"Returning cached signal for {symbol}")
                return cached_signal
            
            # Get technical analysis
            technical_analysis = await technical_analysis_service.analyze_symbol(
                symbol, timeframe, exchange=exchange
            )
            
            if not technical_analysis:
                logger.warning(f"No technical analysis available for {symbol}")
                return None
            
            # Get pattern analysis
            pattern_analysis = await pattern_detection_service.analyze_patterns(
                symbol, timeframe, exchange=exchange
            )
            
            # Get market data for additional analysis
            market_data = await market_data_service.get_ohlcv_data(
                symbol, timeframe, limit=200, exchange=exchange
            )
            
            if market_data is None or len(market_data) < 50:
                logger.warning(f"Insufficient market data for {symbol}")
                return None
            
            # Generate signal factors
            technical_factors = await self._analyze_technical_factors(
                technical_analysis, market_data
            )
            
            pattern_factors = await self._analyze_pattern_factors(
                pattern_analysis, market_data
            )
            
            market_factors = await self._analyze_market_factors(
                market_data, symbol
            )

            # Get AI analysis factors
            ai_factors = await self._analyze_ai_factors(
                symbol, timeframe
            )

            # Detect market regime and apply adjustments
            regime_adjustments = {}
            if self.get_parameter("enable_regime_detection", True):
                regime_signal = await market_regime_detector.detect_regime(
                    symbol=symbol,
                    timeframe="1d",  # Use daily timeframe for regime detection
                    exchange=exchange
                )

                # Apply regime adjustments if confidence is sufficient
                if regime_signal.confidence >= self.get_parameter("regime_confidence_threshold", 0.6):
                    regime_adjustments = regime_signal.recommended_adjustments
                    adjustment_strength = self.get_parameter("regime_adjustment_strength", 1.0)

                    # Scale adjustments by strength parameter
                    for key, value in regime_adjustments.items():
                        if key != "long_bias":  # Don't scale bias, it's already normalized
                            regime_adjustments[key] = 1.0 + (value - 1.0) * adjustment_strength
                        else:
                            regime_adjustments[key] = value * adjustment_strength

                    logger.info(f"Market regime detected: {regime_signal.regime.value} "
                               f"(confidence: {regime_signal.confidence:.2f}) - "
                               f"Applying adjustments: {regime_adjustments}")

            # Calculate overall signal score
            overall_score, confidence = self._calculate_overall_score(
                technical_factors, pattern_factors, market_factors, ai_factors
            )
            
            # Determine signal type and strength
            signal_type, signal_strength = self._determine_signal_type(
                overall_score, confidence
            )
            
            # Optimize entry/exit points
            entry_point = await self._optimize_entry_point(
                signal_type, market_data, technical_analysis, pattern_analysis
            )
            
            exit_points = await self._optimize_exit_points(
                signal_type, entry_point, market_data, technical_analysis, regime_adjustments
            )
            
            # Calculate position sizing with regime adjustments
            position_sizing = await self._calculate_position_sizing(
                signal_type, signal_strength, confidence, portfolio_size,
                entry_point, exit_points, regime_adjustments
            )
            
            # Generate reasoning
            reasoning = self._generate_reasoning(
                technical_factors, pattern_factors, market_factors, ai_factors,
                signal_type, signal_strength
            )
            
            # Calculate risk metrics
            risk_metrics = await self._calculate_risk_metrics(
                market_data, signal_type, entry_point
            )
            
            # Create trading signal
            trading_signal = TradingSignal(
                symbol=symbol,
                timeframe=timeframe,
                signal_type=signal_type,
                signal_strength=signal_strength,
                overall_score=overall_score,
                confidence=confidence,
                technical_factors=technical_factors,
                pattern_factors=pattern_factors,
                market_factors=market_factors,
                ai_factors=ai_factors,
                entry_point=entry_point,
                exit_points=exit_points,
                position_sizing=position_sizing,
                generated_at=datetime.now(),
                valid_until=datetime.now() + timedelta(hours=4),  # 4 hour validity
                exchange=exchange,
                reasoning=reasoning,
                max_drawdown_risk=risk_metrics["max_drawdown"],
                volatility_risk=risk_metrics["volatility"],
                market_correlation_risk=risk_metrics["correlation"]
            )
            
            # Cache the signal (5 minutes TTL)
            await cache_manager.set(cache_key, trading_signal, ttl=300)
            
            logger.info(f"Generated {signal_type.value} signal for {symbol} with {confidence:.2f} confidence")
            return trading_signal
            
        except Exception as e:
            logger.error(f"Error generating signal for {symbol}: {e}")
            return None
    
    async def _analyze_technical_factors(
        self,
        technical_analysis,
        market_data: pd.DataFrame
    ) -> List[SignalFactor]:
        """Analyze technical indicator factors"""
        factors = []

        try:
            # Get technical weights from parameters
            technical_weights = self.get_parameter("technical_weights", self.default_parameters["technical_weights"])

            indicators = technical_analysis.indicators
            
            # RSI Factor
            if indicators.rsi is not None:
                rsi_value = indicators.rsi
                if rsi_value < 30:
                    rsi_signal = 0.8  # Strong bullish
                elif rsi_value < 40:
                    rsi_signal = 0.4  # Moderate bullish
                elif rsi_value > 70:
                    rsi_signal = -0.8  # Strong bearish
                elif rsi_value > 60:
                    rsi_signal = -0.4  # Moderate bearish
                else:
                    rsi_signal = 0.0  # Neutral
                
                factors.append(SignalFactor(
                    name="RSI",
                    value=rsi_signal,
                    weight=technical_weights["rsi"],
                    confidence=0.8,
                    description=f"RSI: {rsi_value:.1f} - {'Oversold' if rsi_value < 30 else 'Overbought' if rsi_value > 70 else 'Neutral'}"
                ))
            
            # MACD Factor
            if indicators.macd is not None:
                macd_line = indicators.macd
                macd_signal_line = getattr(indicators, 'macd_signal', 0)
                macd_histogram = getattr(indicators, 'macd_histogram', 0)
                
                # MACD signal strength
                if macd_line > macd_signal_line and macd_histogram > 0:
                    macd_signal = 0.7  # Bullish
                elif macd_line < macd_signal_line and macd_histogram < 0:
                    macd_signal = -0.7  # Bearish
                else:
                    macd_signal = 0.0  # Neutral
                
                factors.append(SignalFactor(
                    name="MACD",
                    value=macd_signal,
                    weight=technical_weights["macd"],
                    confidence=0.75,
                    description=f"MACD: {macd_line:.4f} - {'Bullish crossover' if macd_signal > 0 else 'Bearish crossover' if macd_signal < 0 else 'No clear signal'}"
                ))
            
            # Trend Factor
            if indicators.trend_direction:
                trend_value = indicators.trend_direction.value
                if trend_value == "bullish":
                    trend_signal = 0.6
                elif trend_value == "bearish":
                    trend_signal = -0.6
                else:
                    trend_signal = 0.0
                
                factors.append(SignalFactor(
                    name="Trend",
                    value=trend_signal,
                    weight=technical_weights["trend"],
                    confidence=0.7,
                    description=f"Trend: {trend_value.title()}"
                ))
            
            # Volume Factor
            if indicators.volume_spike is not None:
                volume_signal = 0.3 if indicators.volume_spike else 0.0
                
                factors.append(SignalFactor(
                    name="Volume",
                    value=volume_signal,
                    weight=technical_weights["volume"],
                    confidence=0.6,
                    description=f"Volume: {'Spike detected' if indicators.volume_spike else 'Normal'}"
                ))
            
            # Momentum Factor (based on recent price action)
            if len(market_data) >= 20:
                recent_returns = market_data['close'].pct_change().tail(10)
                momentum = recent_returns.mean()
                
                momentum_signal = np.clip(momentum * 50, -1, 1)  # Scale to -1,1
                
                factors.append(SignalFactor(
                    name="Momentum",
                    value=momentum_signal,
                    weight=technical_weights["momentum"],
                    confidence=0.5,
                    description=f"Momentum: {momentum*100:.2f}% - {'Positive' if momentum > 0 else 'Negative'}"
                ))
            
        except Exception as e:
            logger.error(f"Error analyzing technical factors: {e}")
        
        return factors
    
    async def _analyze_pattern_factors(
        self,
        pattern_analysis,
        market_data: pd.DataFrame
    ) -> List[SignalFactor]:
        """Analyze pattern detection factors"""
        factors = []

        try:
            # Get pattern weights from parameters
            pattern_weights = self.get_parameter("pattern_weights", self.default_parameters["pattern_weights"])

            if not pattern_analysis:
                return factors
            
            current_price = market_data['close'].iloc[-1]
            
            # Support/Resistance Factor
            support_signal = 0.0
            resistance_signal = 0.0
            
            # Check proximity to support levels
            for support in pattern_analysis.support_levels:
                distance_pct = abs(current_price - support.price) / current_price
                if distance_pct < 0.02:  # Within 2%
                    support_signal += support.strength * 0.5  # Bullish near support
            
            # Check proximity to resistance levels
            for resistance in pattern_analysis.resistance_levels:
                distance_pct = abs(current_price - resistance.price) / current_price
                if distance_pct < 0.02:  # Within 2%
                    resistance_signal -= resistance.strength * 0.5  # Bearish near resistance
            
            sr_signal = np.clip(support_signal + resistance_signal, -1, 1)
            
            factors.append(SignalFactor(
                name="Support/Resistance",
                value=sr_signal,
                weight=pattern_weights["support_resistance"],
                confidence=0.8,
                description=f"S/R: {'Near support' if support_signal > 0 else 'Near resistance' if resistance_signal < 0 else 'Neutral'}"
            ))
            
            # Trend Lines Factor
            trendline_signal = 0.0
            
            for trendline in pattern_analysis.trend_lines:
                if not trendline.is_broken and trendline.r_squared > 0.7:
                    if trendline.line_type == "support_trendline" and trendline.slope > 0:
                        trendline_signal += 0.3  # Bullish ascending support
                    elif trendline.line_type == "resistance_trendline" and trendline.slope < 0:
                        trendline_signal -= 0.3  # Bearish descending resistance
            
            trendline_signal = np.clip(trendline_signal, -1, 1)
            
            factors.append(SignalFactor(
                name="Trend Lines",
                value=trendline_signal,
                weight=pattern_weights["trend_lines"],
                confidence=0.7,
                description=f"Trend Lines: {'Bullish' if trendline_signal > 0 else 'Bearish' if trendline_signal < 0 else 'Neutral'}"
            ))
            
            # Chart Patterns Factor
            pattern_signal = 0.0
            strongest_pattern = None
            
            for pattern in pattern_analysis.chart_patterns:
                if pattern.confidence > 0.6:
                    if pattern.breakout_direction == "bullish":
                        pattern_signal += pattern.confidence * 0.5
                    elif pattern.breakout_direction == "bearish":
                        pattern_signal -= pattern.confidence * 0.5
                    
                    if strongest_pattern is None or pattern.confidence > strongest_pattern.confidence:
                        strongest_pattern = pattern
            
            pattern_signal = np.clip(pattern_signal, -1, 1)
            
            pattern_desc = f"Patterns: {strongest_pattern.pattern_name if strongest_pattern else 'None detected'}"
            
            factors.append(SignalFactor(
                name="Chart Patterns",
                value=pattern_signal,
                weight=pattern_weights["chart_patterns"],
                confidence=0.75,
                description=pattern_desc
            ))
            
            # Fibonacci Factor
            fib_signal = 0.0
            
            for fib_level in pattern_analysis.fibonacci_levels:
                if fib_level.is_active and fib_level.touches > 0:
                    distance_pct = abs(current_price - fib_level.price) / current_price
                    if distance_pct < 0.01:  # Within 1%
                        if fib_level.level_type == "retracement":
                            fib_signal += 0.2  # Bullish at retracement
                        else:
                            fib_signal += 0.1  # Neutral at extension
            
            fib_signal = np.clip(fib_signal, -1, 1)
            
            factors.append(SignalFactor(
                name="Fibonacci",
                value=fib_signal,
                weight=pattern_weights["fibonacci"],
                confidence=0.6,
                description=f"Fibonacci: {'Near key level' if fib_signal > 0 else 'No interaction'}"
            ))
            
        except Exception as e:
            logger.error(f"Error analyzing pattern factors: {e}")
        
        return factors
    
    async def _analyze_market_factors(
        self,
        market_data: pd.DataFrame,
        symbol: str
    ) -> List[SignalFactor]:
        """Analyze broader market factors"""
        factors = []

        try:
            # Get market weights from parameters
            market_weights = self.get_parameter("market_weights", self.default_parameters["market_weights"])
            # Market Trend Factor (based on longer-term moving averages)
            if len(market_data) >= 50:
                sma_20 = market_data['close'].rolling(20).mean().iloc[-1]
                sma_50 = market_data['close'].rolling(50).mean().iloc[-1]
                current_price = market_data['close'].iloc[-1]
                
                if current_price > sma_20 > sma_50:
                    trend_signal = 0.6  # Strong bullish trend
                elif current_price > sma_20 and sma_20 < sma_50:
                    trend_signal = 0.3  # Weak bullish trend
                elif current_price < sma_20 < sma_50:
                    trend_signal = -0.6  # Strong bearish trend
                elif current_price < sma_20 and sma_20 > sma_50:
                    trend_signal = -0.3  # Weak bearish trend
                else:
                    trend_signal = 0.0  # Sideways
                
                factors.append(SignalFactor(
                    name="Market Trend",
                    value=trend_signal,
                    weight=market_weights["market_trend"],
                    confidence=0.7,
                    description=f"Market Trend: {'Bullish' if trend_signal > 0 else 'Bearish' if trend_signal < 0 else 'Sideways'}"
                ))
            
            # Volatility Factor
            if len(market_data) >= 20:
                returns = market_data['close'].pct_change().dropna()
                volatility = returns.rolling(20).std().iloc[-1]
                
                # High volatility can be both opportunity and risk
                if volatility > 0.05:  # > 5% daily volatility
                    vol_signal = -0.3  # Slightly bearish (high risk)
                elif volatility < 0.02:  # < 2% daily volatility
                    vol_signal = 0.2  # Slightly bullish (stable)
                else:
                    vol_signal = 0.0  # Normal
                
                factors.append(SignalFactor(
                    name="Volatility",
                    value=vol_signal,
                    weight=market_weights["volatility"],
                    confidence=0.6,
                    description=f"Volatility: {volatility*100:.1f}% - {'High' if volatility > 0.05 else 'Low' if volatility < 0.02 else 'Normal'}"
                ))
            
            # Volume Profile Factor
            if 'volume' in market_data.columns and len(market_data) >= 20:
                avg_volume = market_data['volume'].rolling(20).mean().iloc[-1]
                recent_volume = market_data['volume'].iloc[-1]
                
                volume_ratio = recent_volume / avg_volume if avg_volume > 0 else 1
                
                if volume_ratio > 1.5:
                    volume_signal = 0.4  # High volume confirms moves
                elif volume_ratio < 0.5:
                    volume_signal = -0.2  # Low volume weakens signals
                else:
                    volume_signal = 0.0  # Normal volume
                
                factors.append(SignalFactor(
                    name="Volume Profile",
                    value=volume_signal,
                    weight=market_weights["volume_profile"],
                    confidence=0.5,
                    description=f"Volume: {volume_ratio:.1f}x average - {'High' if volume_ratio > 1.5 else 'Low' if volume_ratio < 0.5 else 'Normal'}"
                ))
            
        except Exception as e:
            logger.error(f"Error analyzing market factors: {e}")
        
        return factors

    async def _analyze_ai_factors(
        self,
        symbol: str,
        timeframe: str = "1h"
    ) -> List[SignalFactor]:
        """Analyze AI-based factors (sentiment, news, insights)"""
        factors = []

        try:
            # Get AI weights from parameters
            ai_weights = self.get_parameter("ai_weights", self.default_parameters["ai_weights"])
            # Import AI analysis service
            from app.services.ai_analysis_service import ai_analysis_service

            # Get AI analysis
            ai_analysis = await ai_analysis_service.generate_comprehensive_analysis(
                symbol, timeframe, include_technical=False
            )

            if not ai_analysis:
                logger.warning(f"No AI analysis available for {symbol}")
                return factors

            # Sentiment Factor
            sentiment_score = ai_analysis.sentiment_analysis.sentiment_score
            sentiment_confidence = ai_analysis.sentiment_analysis.confidence

            # Convert sentiment score to signal value (-1 to 1)
            sentiment_signal = sentiment_score  # Already in -1 to 1 range

            sentiment_desc = f"Sentiment: {ai_analysis.sentiment_analysis.overall_sentiment.value} (score: {sentiment_score:.2f})"

            factors.append(SignalFactor(
                name="Market Sentiment",
                value=sentiment_signal,
                weight=ai_weights["sentiment"],
                confidence=sentiment_confidence,
                description=sentiment_desc
            ))

            # News Impact Factor
            news_count = ai_analysis.sentiment_analysis.news_count
            bullish_signals = len(ai_analysis.sentiment_analysis.bullish_signals)
            bearish_signals = len(ai_analysis.sentiment_analysis.bearish_signals)

            # Calculate news impact based on signal balance
            if news_count > 0:
                news_impact = (bullish_signals - bearish_signals) / max(news_count, 1)
                news_impact = np.clip(news_impact, -1, 1)
            else:
                news_impact = 0.0

            news_desc = f"News Impact: {news_count} articles, {bullish_signals} bullish, {bearish_signals} bearish"

            factors.append(SignalFactor(
                name="News Impact",
                value=news_impact,
                weight=ai_weights["news_impact"],
                confidence=min(sentiment_confidence, 0.8),  # Slightly lower confidence for news
                description=news_desc
            ))

            # Market Insights Factor
            insights_score = 0.0
            insights_count = len(ai_analysis.market_insights)

            if insights_count > 0:
                # Average impact score from insights
                total_impact = sum(insight.impact_score for insight in ai_analysis.market_insights)
                insights_score = total_impact / insights_count
                insights_score = np.clip(insights_score, -1, 1)

            insights_desc = f"Market Insights: {insights_count} insights, avg impact: {insights_score:.2f}"

            factors.append(SignalFactor(
                name="Market Insights",
                value=insights_score,
                weight=ai_weights["market_insights"],
                confidence=ai_analysis.confidence_score,
                description=insights_desc
            ))

            # AI Recommendation Factor
            recommendation = ai_analysis.ai_recommendation.lower()
            rec_signal = 0.0

            if "strong buy" in recommendation:
                rec_signal = 0.8
            elif "buy" in recommendation:
                rec_signal = 0.5
            elif "hold" in recommendation:
                rec_signal = 0.0
            elif "sell" in recommendation:
                rec_signal = -0.5
            elif "strong sell" in recommendation:
                rec_signal = -0.8

            rec_desc = f"AI Recommendation: {recommendation[:50]}..."

            factors.append(SignalFactor(
                name="AI Recommendation",
                value=rec_signal,
                weight=ai_weights["ai_recommendation"],
                confidence=ai_analysis.confidence_score,
                description=rec_desc
            ))

            logger.info(f"AI factors analyzed for {symbol}: {len(factors)} factors")

        except Exception as e:
            logger.warning(f"Error analyzing AI factors for {symbol}: {e}")
            # Return empty factors if AI analysis fails

        return factors

    def _calculate_overall_score(
        self,
        technical_factors: List[SignalFactor],
        pattern_factors: List[SignalFactor],
        market_factors: List[SignalFactor],
        ai_factors: List[SignalFactor]
    ) -> Tuple[float, float]:
        """Calculate overall signal score and confidence"""
        try:
            # Calculate weighted scores for each category
            tech_score = sum(f.value * f.weight for f in technical_factors)
            tech_confidence = sum(f.confidence * f.weight for f in technical_factors) if technical_factors else 0
            
            pattern_score = sum(f.value * f.weight for f in pattern_factors)
            pattern_confidence = sum(f.confidence * f.weight for f in pattern_factors) if pattern_factors else 0
            
            market_score = sum(f.value * f.weight for f in market_factors)
            market_confidence = sum(f.confidence * f.weight for f in market_factors) if market_factors else 0

            ai_score = sum(f.value * f.weight for f in ai_factors)
            ai_confidence = sum(f.confidence * f.weight for f in ai_factors) if ai_factors else 0

            # Get signal weights from parameters
            signal_weights = self.get_parameter("signal_weights", self.default_parameters["signal_weights"])

            # Combine scores with category weights
            overall_score = (
                tech_score * signal_weights["technical"] +
                pattern_score * signal_weights["pattern"] +
                market_score * signal_weights["market"] +
                ai_score * signal_weights["ai"]
            )

            # Calculate overall confidence
            overall_confidence = (
                tech_confidence * signal_weights["technical"] +
                pattern_confidence * signal_weights["pattern"] +
                market_confidence * signal_weights["market"] +
                ai_confidence * signal_weights["ai"]
            )
            
            # Normalize scores
            overall_score = np.clip(overall_score, -1, 1)
            overall_confidence = np.clip(overall_confidence, 0, 1)
            
            return overall_score, overall_confidence
            
        except Exception as e:
            logger.error(f"Error calculating overall score: {e}")
            return 0.0, 0.0
    
    def _determine_signal_type(
        self,
        overall_score: float,
        confidence: float
    ) -> Tuple[SignalType, SignalStrength]:
        """Determine signal type and strength"""
        try:
            # Determine signal type based on score
            if overall_score > 0.6:
                signal_type = SignalType.STRONG_BUY
            elif overall_score > 0.2:
                signal_type = SignalType.BUY
            elif overall_score < -0.6:
                signal_type = SignalType.STRONG_SELL
            elif overall_score < -0.2:
                signal_type = SignalType.SELL
            else:
                signal_type = SignalType.HOLD
            
            # Determine signal strength based on confidence and score magnitude
            score_magnitude = abs(overall_score)
            
            if confidence > 0.8 and score_magnitude > 0.6:
                signal_strength = SignalStrength.VERY_STRONG
            elif confidence > 0.7 and score_magnitude > 0.4:
                signal_strength = SignalStrength.STRONG
            elif confidence > 0.5 and score_magnitude > 0.2:
                signal_strength = SignalStrength.MODERATE
            else:
                signal_strength = SignalStrength.WEAK
            
            return signal_type, signal_strength
            
        except Exception as e:
            logger.error(f"Error determining signal type: {e}")
            return SignalType.HOLD, SignalStrength.WEAK


    async def _optimize_entry_point(
        self,
        signal_type: SignalType,
        market_data: pd.DataFrame,
        technical_analysis,
        pattern_analysis
    ) -> EntryExitPoint:
        """Optimize entry point based on signal type and market conditions"""
        try:
            current_price = market_data['close'].iloc[-1]

            if signal_type in [SignalType.BUY, SignalType.STRONG_BUY]:
                # For buy signals, look for optimal entry below current price
                entry_price = current_price
                confidence = 0.7
                reasoning = "Market entry at current price"

                # Check for better entry near support
                if pattern_analysis and pattern_analysis.support_levels:
                    nearest_support = min(
                        pattern_analysis.support_levels,
                        key=lambda s: abs(s.price - current_price)
                    )

                    if nearest_support.price < current_price and \
                       (current_price - nearest_support.price) / current_price < 0.03:  # Within 3%
                        entry_price = nearest_support.price + (current_price - nearest_support.price) * 0.2
                        confidence = 0.85
                        reasoning = f"Entry near support level at ${nearest_support.price:.2f}"

                # Calculate stop loss and take profit using parameters
                default_stop_loss = self.get_parameter("default_stop_loss", self.default_parameters["default_stop_loss"])
                min_risk_reward = self.get_parameter("min_risk_reward", self.default_parameters["min_risk_reward"])

                stop_loss = entry_price * (1 - default_stop_loss)
                take_profit = entry_price * (1 + default_stop_loss * min_risk_reward)

                return EntryExitPoint(
                    price=entry_price,
                    confidence=confidence,
                    reasoning=reasoning,
                    stop_loss=stop_loss,
                    take_profit=take_profit,
                    risk_reward_ratio=min_risk_reward
                )

            elif signal_type in [SignalType.SELL, SignalType.STRONG_SELL]:
                # For sell signals, look for optimal entry above current price
                entry_price = current_price
                confidence = 0.7
                reasoning = "Market entry at current price"

                # Check for better entry near resistance
                if pattern_analysis and pattern_analysis.resistance_levels:
                    nearest_resistance = min(
                        pattern_analysis.resistance_levels,
                        key=lambda r: abs(r.price - current_price)
                    )

                    if nearest_resistance.price > current_price and \
                       (nearest_resistance.price - current_price) / current_price < 0.03:  # Within 3%
                        entry_price = nearest_resistance.price - (nearest_resistance.price - current_price) * 0.2
                        confidence = 0.85
                        reasoning = f"Entry near resistance level at ${nearest_resistance.price:.2f}"

                # Calculate stop loss and take profit for short using parameters
                stop_loss = entry_price * (1 + default_stop_loss)
                take_profit = entry_price * (1 - default_stop_loss * min_risk_reward)

                return EntryExitPoint(
                    price=entry_price,
                    confidence=confidence,
                    reasoning=reasoning,
                    stop_loss=stop_loss,
                    take_profit=take_profit,
                    risk_reward_ratio=min_risk_reward
                )

            else:  # HOLD
                return EntryExitPoint(
                    price=current_price,
                    confidence=0.5,
                    reasoning="Hold position - no clear entry signal",
                    stop_loss=current_price * 0.95,
                    take_profit=current_price * 1.05,
                    risk_reward_ratio=1.0
                )

        except Exception as e:
            logger.error(f"Error optimizing entry point: {e}")
            current_price = market_data['close'].iloc[-1]
            return EntryExitPoint(
                price=current_price,
                confidence=0.5,
                reasoning="Default entry at current price",
                stop_loss=current_price * 0.98,
                take_profit=current_price * 1.04,
                risk_reward_ratio=2.0
            )

    async def _optimize_exit_points(
        self,
        signal_type: SignalType,
        entry_point: EntryExitPoint,
        market_data: pd.DataFrame,
        technical_analysis,
        regime_adjustments: Optional[Dict[str, float]] = None
    ) -> List[EntryExitPoint]:
        """Optimize multiple exit points for scaling out"""
        exit_points = []

        try:
            entry_price = entry_point.price

            # Apply regime adjustments to take profit and stop loss levels
            take_profit_multiplier = 1.0
            stop_loss_multiplier = 1.0

            if regime_adjustments:
                take_profit_multiplier = regime_adjustments.get("take_profit_multiplier", 1.0)
                stop_loss_multiplier = regime_adjustments.get("stop_loss_multiplier", 1.0)
                logger.info(f"Applying regime adjustments to exit points: "
                           f"TP multiplier={take_profit_multiplier:.2f}, "
                           f"SL multiplier={stop_loss_multiplier:.2f}")

            if signal_type in [SignalType.BUY, SignalType.STRONG_BUY]:
                # Multiple take profit levels for long positions

                # Conservative exit (30% position)
                tp1 = entry_price * (1.0 + 0.02 * take_profit_multiplier)  # Adjusted 2% profit
                exit_points.append(EntryExitPoint(
                    price=tp1,
                    confidence=0.8,
                    reasoning="Conservative take profit - 30% position",
                    risk_reward_ratio=1.0
                ))

                # Moderate exit (40% position)
                tp2 = entry_price * (1.0 + 0.04 * take_profit_multiplier)  # Adjusted 4% profit
                exit_points.append(EntryExitPoint(
                    price=tp2,
                    confidence=0.7,
                    reasoning="Moderate take profit - 40% position",
                    risk_reward_ratio=2.0
                ))

                # Aggressive exit (30% position)
                tp3 = entry_price * (1.0 + 0.08 * take_profit_multiplier)  # Adjusted 8% profit
                exit_points.append(EntryExitPoint(
                    price=tp3,
                    confidence=0.6,
                    reasoning="Aggressive take profit - 30% position",
                    risk_reward_ratio=4.0
                ))

            elif signal_type in [SignalType.SELL, SignalType.STRONG_SELL]:
                # Multiple take profit levels for short positions

                # Conservative exit (30% position)
                tp1 = entry_price * 0.98  # 2% profit
                exit_points.append(EntryExitPoint(
                    price=tp1,
                    confidence=0.8,
                    reasoning="Conservative take profit - 30% position",
                    risk_reward_ratio=1.0
                ))

                # Moderate exit (40% position)
                tp2 = entry_price * 0.96  # 4% profit
                exit_points.append(EntryExitPoint(
                    price=tp2,
                    confidence=0.7,
                    reasoning="Moderate take profit - 40% position",
                    risk_reward_ratio=2.0
                ))

                # Aggressive exit (30% position)
                tp3 = entry_price * 0.92  # 8% profit
                exit_points.append(EntryExitPoint(
                    price=tp3,
                    confidence=0.6,
                    reasoning="Aggressive take profit - 30% position",
                    risk_reward_ratio=4.0
                ))

            # Add resistance/support based exits if available
            indicators = technical_analysis.indicators
            if indicators.resistance_level:
                resistance_exit = EntryExitPoint(
                    price=indicators.resistance_level,
                    confidence=0.75,
                    reasoning=f"Exit at resistance level ${indicators.resistance_level:.2f}",
                    risk_reward_ratio=((indicators.resistance_level - entry_price) / entry_price) / self.get_parameter("default_stop_loss", self.default_parameters["default_stop_loss"])
                )
                exit_points.append(resistance_exit)

        except Exception as e:
            logger.error(f"Error optimizing exit points: {e}")

        return exit_points

    async def _calculate_position_sizing(
        self,
        signal_type: SignalType,
        signal_strength: SignalStrength,
        confidence: float,
        portfolio_size: float,
        entry_point: EntryExitPoint,
        exit_points: List[EntryExitPoint],
        regime_adjustments: Optional[Dict[str, float]] = None
    ) -> PositionSizing:
        """Calculate optimal position sizing based on risk management"""
        try:
            # Base position size based on signal strength
            base_size_map = {
                SignalStrength.WEAK: 0.02,      # 2%
                SignalStrength.MODERATE: 0.04,   # 4%
                SignalStrength.STRONG: 0.06,     # 6%
                SignalStrength.VERY_STRONG: 0.08 # 8%
            }

            base_position_size = base_size_map.get(signal_strength, 0.02)

            # Adjust based on confidence
            confidence_multiplier = 0.5 + (confidence * 0.5)  # 0.5 to 1.0
            position_size = base_position_size * confidence_multiplier

            # Risk level determination
            if signal_strength == SignalStrength.VERY_STRONG and confidence > 0.8:
                risk_level = RiskLevel.MEDIUM
            elif signal_strength in [SignalStrength.STRONG, SignalStrength.VERY_STRONG]:
                risk_level = RiskLevel.MEDIUM
            elif signal_strength == SignalStrength.MODERATE:
                risk_level = RiskLevel.LOW
            else:
                risk_level = RiskLevel.LOW

            # Adjust for signal type
            if signal_type in [SignalType.STRONG_BUY, SignalType.STRONG_SELL]:
                position_size *= 1.2  # Increase for strong signals
            elif signal_type == SignalType.HOLD:
                position_size *= 0.5  # Reduce for hold signals

            # Apply market regime adjustments
            if regime_adjustments:
                # Apply position size multiplier from regime
                position_size_multiplier = regime_adjustments.get("position_size_multiplier", 1.0)
                position_size *= position_size_multiplier

                # Apply long/short bias
                long_bias = regime_adjustments.get("long_bias", 0.0)
                if signal_type in [SignalType.BUY, SignalType.STRONG_BUY]:
                    # Increase position size for long signals in bull market
                    position_size *= (1.0 + max(0, long_bias))
                elif signal_type in [SignalType.SELL, SignalType.STRONG_SELL]:
                    # Increase position size for short signals in bear market
                    position_size *= (1.0 + max(0, -long_bias))

                logger.info(f"Applied regime adjustments: position_size_multiplier={position_size_multiplier:.2f}, "
                           f"long_bias={long_bias:.2f}")

            # Cap at maximum position size
            max_position_size = self.get_parameter("max_position_size", self.default_parameters["max_position_size"])
            position_size = min(position_size, max_position_size)

            # Calculate position size in USD
            position_size_usd = portfolio_size * position_size

            # Calculate max loss percentage
            if entry_point.stop_loss:
                if signal_type in [SignalType.BUY, SignalType.STRONG_BUY]:
                    max_loss_pct = ((entry_point.price - entry_point.stop_loss) / entry_point.price) * position_size
                else:
                    max_loss_pct = ((entry_point.stop_loss - entry_point.price) / entry_point.price) * position_size
            else:
                max_loss_pct = self.get_parameter("default_stop_loss", self.default_parameters["default_stop_loss"]) * position_size

            # Leverage calculation (conservative approach)
            leverage = 1.0
            if signal_strength == SignalStrength.VERY_STRONG and confidence > 0.85:
                leverage = 1.5  # Modest leverage for very strong signals

            reasoning = f"Position size based on {signal_strength.value} signal with {confidence:.1%} confidence"

            return PositionSizing(
                position_size_percentage=position_size * 100,
                position_size_usd=position_size_usd,
                max_loss_percentage=max_loss_pct * 100,
                risk_level=risk_level,
                leverage=leverage,
                reasoning=reasoning
            )

        except Exception as e:
            logger.error(f"Error calculating position sizing: {e}")
            return PositionSizing(
                position_size_percentage=2.0,  # Default 2%
                position_size_usd=portfolio_size * 0.02,
                max_loss_percentage=2.0,
                risk_level=RiskLevel.LOW,
                leverage=1.0,
                reasoning="Default conservative position sizing"
            )

    def _generate_reasoning(
        self,
        technical_factors: List[SignalFactor],
        pattern_factors: List[SignalFactor],
        market_factors: List[SignalFactor],
        ai_factors: List[SignalFactor],
        signal_type: SignalType,
        signal_strength: SignalStrength
    ) -> str:
        """Generate human-readable reasoning for the signal"""
        try:
            reasoning_parts = []

            # Signal summary
            reasoning_parts.append(f"{signal_strength.value.title()} {signal_type.value.upper()} signal generated.")

            # Technical factors
            strong_tech_factors = [f for f in technical_factors if abs(f.value) > 0.3]
            if strong_tech_factors:
                tech_reasons = []
                for factor in strong_tech_factors:
                    direction = "bullish" if factor.value > 0 else "bearish"
                    tech_reasons.append(f"{factor.name} is {direction}")
                reasoning_parts.append(f"Technical: {', '.join(tech_reasons)}.")

            # Pattern factors
            strong_pattern_factors = [f for f in pattern_factors if abs(f.value) > 0.3]
            if strong_pattern_factors:
                pattern_reasons = []
                for factor in strong_pattern_factors:
                    direction = "bullish" if factor.value > 0 else "bearish"
                    pattern_reasons.append(f"{factor.name} shows {direction} signals")
                reasoning_parts.append(f"Patterns: {', '.join(pattern_reasons)}.")

            # Market factors
            strong_market_factors = [f for f in market_factors if abs(f.value) > 0.2]
            if strong_market_factors:
                market_reasons = []
                for factor in strong_market_factors:
                    direction = "supportive" if factor.value > 0 else "concerning"
                    market_reasons.append(f"{factor.name} is {direction}")
                reasoning_parts.append(f"Market: {', '.join(market_reasons)}.")

            # AI factors
            strong_ai_factors = [f for f in ai_factors if abs(f.value) > 0.2]
            if strong_ai_factors:
                ai_reasons = []
                for factor in strong_ai_factors:
                    direction = "positive" if factor.value > 0 else "negative"
                    ai_reasons.append(f"{factor.name} is {direction}")
                reasoning_parts.append(f"AI Analysis: {', '.join(ai_reasons)}.")

            return " ".join(reasoning_parts)

        except Exception as e:
            logger.error(f"Error generating reasoning: {e}")
            return f"{signal_strength.value.title()} {signal_type.value} signal based on technical and pattern analysis."

    async def _calculate_risk_metrics(
        self,
        market_data: pd.DataFrame,
        signal_type: SignalType,
        entry_point: EntryExitPoint
    ) -> Dict[str, float]:
        """Calculate risk metrics for the signal"""
        try:
            # Calculate volatility risk
            returns = market_data['close'].pct_change().dropna()
            volatility = returns.std() * np.sqrt(24)  # Annualized hourly volatility

            # Calculate max drawdown risk
            rolling_max = market_data['close'].expanding().max()
            drawdown = (market_data['close'] - rolling_max) / rolling_max
            max_drawdown = abs(drawdown.min())

            # Market correlation risk (simplified - would need broader market data)
            correlation_risk = 0.3  # Default moderate correlation

            return {
                "volatility": min(volatility, 1.0),  # Cap at 100%
                "max_drawdown": min(max_drawdown, 1.0),  # Cap at 100%
                "correlation": correlation_risk
            }

        except Exception as e:
            logger.error(f"Error calculating risk metrics: {e}")
            return {
                "volatility": 0.3,
                "max_drawdown": 0.2,
                "correlation": 0.3
            }

    async def generate_multi_symbol_signals(
        self,
        symbols: List[str],
        timeframe: str = "1h",
        exchange: str = "binance",
        portfolio_size: float = 10000.0
    ) -> Dict[str, Optional[TradingSignal]]:
        """Generate signals for multiple symbols"""
        signals = {}

        # Process symbols concurrently
        tasks = []
        for symbol in symbols:
            task = self.generate_signal(symbol, timeframe, exchange, portfolio_size)
            tasks.append(task)

        results = await asyncio.gather(*tasks, return_exceptions=True)

        for symbol, result in zip(symbols, results):
            if isinstance(result, Exception):
                logger.error(f"Error generating signal for {symbol}: {result}")
                signals[symbol] = None
            else:
                signals[symbol] = result

        return signals

    async def get_signal_summary(
        self,
        symbols: List[str] = None,
        timeframe: str = "1h",
        exchange: str = "binance"
    ) -> Dict[str, Any]:
        """Get market signal summary"""
        try:
            if symbols is None:
                symbols = settings.DEFAULT_SYMBOLS

            signals = await self.generate_multi_symbol_signals(
                symbols, timeframe, exchange
            )

            # Analyze signal distribution
            signal_counts = {
                "strong_buy": 0,
                "buy": 0,
                "hold": 0,
                "sell": 0,
                "strong_sell": 0
            }

            total_confidence = 0
            valid_signals = 0

            for symbol, signal in signals.items():
                if signal:
                    signal_counts[signal.signal_type.value] += 1
                    total_confidence += signal.confidence
                    valid_signals += 1

            avg_confidence = total_confidence / valid_signals if valid_signals > 0 else 0

            # Market sentiment
            bullish_signals = signal_counts["strong_buy"] + signal_counts["buy"]
            bearish_signals = signal_counts["strong_sell"] + signal_counts["sell"]

            if bullish_signals > bearish_signals:
                market_sentiment = "bullish"
            elif bearish_signals > bullish_signals:
                market_sentiment = "bearish"
            else:
                market_sentiment = "neutral"

            return {
                "market_sentiment": market_sentiment,
                "signal_distribution": signal_counts,
                "average_confidence": round(avg_confidence, 3),
                "total_symbols_analyzed": len(symbols),
                "valid_signals": valid_signals,
                "analysis_timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error getting signal summary: {e}")
            return {"error": str(e)}


# Global instance
signal_generation_service = SignalGenerationService()

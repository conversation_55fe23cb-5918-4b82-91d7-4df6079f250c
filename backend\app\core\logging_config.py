"""
Comprehensive Logging Configuration for AI Crypto Trading System
Author: inkbytefo

Advanced logging system with:
- Structured logging with JSON format
- Performance monitoring
- Log aggregation and rotation
- Context-aware logging
- Error tracking and alerting
"""

import logging
import logging.handlers
import json
import sys
import os
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path
import traceback
from contextlib import contextmanager
import time
from functools import wraps

from app.core.config import settings


class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured JSON logging"""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as structured JSON"""
        
        # Base log structure
        log_entry = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
            "thread": record.thread,
            "process": record.process
        }
        
        # Add exception information if present
        if record.exc_info:
            log_entry["exception"] = {
                "type": record.exc_info[0].__name__ if record.exc_info[0] else None,
                "message": str(record.exc_info[1]) if record.exc_info[1] else None,
                "traceback": traceback.format_exception(*record.exc_info)
            }
        
        # Add custom fields from extra
        if hasattr(record, 'extra_fields'):
            log_entry.update(record.extra_fields)
        
        # Add performance metrics if present
        if hasattr(record, 'performance'):
            log_entry["performance"] = record.performance
        
        # Add trading context if present
        if hasattr(record, 'trading_context'):
            log_entry["trading_context"] = record.trading_context
        
        # Add user context if present
        if hasattr(record, 'user_context'):
            log_entry["user_context"] = record.user_context
        
        return json.dumps(log_entry, ensure_ascii=False)


class PerformanceLogger:
    """Logger for performance monitoring"""
    
    def __init__(self, logger_name: str = "performance"):
        self.logger = logging.getLogger(logger_name)
    
    def log_execution_time(self, operation: str, duration: float, **kwargs):
        """Log execution time for operations"""
        performance_data = {
            "operation": operation,
            "duration_ms": round(duration * 1000, 2),
            "duration_seconds": round(duration, 4),
            **kwargs
        }
        
        # Create log record with performance data
        record = logging.LogRecord(
            name=self.logger.name,
            level=logging.INFO,
            pathname="",
            lineno=0,
            msg=f"Performance: {operation} completed in {duration:.4f}s",
            args=(),
            exc_info=None
        )
        record.performance = performance_data
        
        self.logger.handle(record)
    
    def log_memory_usage(self, operation: str, memory_mb: float, **kwargs):
        """Log memory usage for operations"""
        performance_data = {
            "operation": operation,
            "memory_mb": round(memory_mb, 2),
            "type": "memory_usage",
            **kwargs
        }
        
        record = logging.LogRecord(
            name=self.logger.name,
            level=logging.INFO,
            pathname="",
            lineno=0,
            msg=f"Memory: {operation} used {memory_mb:.2f}MB",
            args=(),
            exc_info=None
        )
        record.performance = performance_data
        
        self.logger.handle(record)


class TradingContextLogger:
    """Logger with trading-specific context"""
    
    def __init__(self, logger_name: str):
        self.logger = logging.getLogger(logger_name)
        self.context = {}
    
    def set_context(self, **kwargs):
        """Set trading context for subsequent logs"""
        self.context.update(kwargs)
    
    def clear_context(self):
        """Clear trading context"""
        self.context.clear()
    
    def log(self, level: int, message: str, **kwargs):
        """Log with trading context"""
        record = logging.LogRecord(
            name=self.logger.name,
            level=level,
            pathname="",
            lineno=0,
            msg=message,
            args=(),
            exc_info=None
        )
        
        # Add trading context
        trading_context = {**self.context, **kwargs}
        if trading_context:
            record.trading_context = trading_context
        
        self.logger.handle(record)
    
    def info(self, message: str, **kwargs):
        """Log info with trading context"""
        self.log(logging.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning with trading context"""
        self.log(logging.WARNING, message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error with trading context"""
        self.log(logging.ERROR, message, **kwargs)
    
    def debug(self, message: str, **kwargs):
        """Log debug with trading context"""
        self.log(logging.DEBUG, message, **kwargs)


def performance_monitor(operation_name: str = None):
    """Decorator for automatic performance monitoring"""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            operation = operation_name or f"{func.__module__}.{func.__name__}"
            
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                
                # Log successful execution
                perf_logger = PerformanceLogger()
                perf_logger.log_execution_time(
                    operation=operation,
                    duration=duration,
                    status="success",
                    args_count=len(args),
                    kwargs_count=len(kwargs)
                )
                
                return result
                
            except Exception as e:
                duration = time.time() - start_time
                
                # Log failed execution
                perf_logger = PerformanceLogger()
                perf_logger.log_execution_time(
                    operation=operation,
                    duration=duration,
                    status="error",
                    error_type=type(e).__name__,
                    error_message=str(e)
                )
                
                raise
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            operation = operation_name or f"{func.__module__}.{func.__name__}"
            
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                # Log successful execution
                perf_logger = PerformanceLogger()
                perf_logger.log_execution_time(
                    operation=operation,
                    duration=duration,
                    status="success"
                )
                
                return result
                
            except Exception as e:
                duration = time.time() - start_time
                
                # Log failed execution
                perf_logger = PerformanceLogger()
                perf_logger.log_execution_time(
                    operation=operation,
                    duration=duration,
                    status="error",
                    error_type=type(e).__name__,
                    error_message=str(e)
                )
                
                raise
        
        # Return appropriate wrapper based on function type
        if hasattr(func, '__code__') and func.__code__.co_flags & 0x80:  # CO_COROUTINE
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


@contextmanager
def trading_context(**context_data):
    """Context manager for trading operations"""
    logger = TradingContextLogger("trading")
    logger.set_context(**context_data)
    
    try:
        yield logger
    finally:
        logger.clear_context()


def setup_logging():
    """Setup comprehensive logging configuration"""
    
    # Create logs directory
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Root logger configuration
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.LOG_LEVEL.upper(), logging.INFO))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Console handler with structured format
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    
    if settings.ENVIRONMENT == "development":
        # Human-readable format for development
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    else:
        # Structured JSON format for production
        console_formatter = StructuredFormatter()
    
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)
    
    # File handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        filename=log_dir / "trading_system.log",
        maxBytes=50 * 1024 * 1024,  # 50MB
        backupCount=10,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(StructuredFormatter())
    root_logger.addHandler(file_handler)
    
    # Error file handler
    error_handler = logging.handlers.RotatingFileHandler(
        filename=log_dir / "errors.log",
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(StructuredFormatter())
    root_logger.addHandler(error_handler)
    
    # Performance log handler
    perf_logger = logging.getLogger("performance")
    perf_handler = logging.handlers.RotatingFileHandler(
        filename=log_dir / "performance.log",
        maxBytes=20 * 1024 * 1024,  # 20MB
        backupCount=5,
        encoding='utf-8'
    )
    perf_handler.setFormatter(StructuredFormatter())
    perf_logger.addHandler(perf_handler)
    perf_logger.setLevel(logging.INFO)
    perf_logger.propagate = False
    
    # Trading log handler
    trading_logger = logging.getLogger("trading")
    trading_handler = logging.handlers.RotatingFileHandler(
        filename=log_dir / "trading.log",
        maxBytes=30 * 1024 * 1024,  # 30MB
        backupCount=10,
        encoding='utf-8'
    )
    trading_handler.setFormatter(StructuredFormatter())
    trading_logger.addHandler(trading_handler)
    trading_logger.setLevel(logging.INFO)
    trading_logger.propagate = False
    
    # Disable some noisy loggers
    logging.getLogger("ccxt").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("requests").setLevel(logging.WARNING)
    
    logging.info("Comprehensive logging system initialized")


# Global instances
performance_logger = PerformanceLogger()
trading_logger = TradingContextLogger("trading")

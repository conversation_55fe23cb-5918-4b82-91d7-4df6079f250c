#!/usr/bin/env python3
"""
Test Runner for AI Crypto Trading System
Author: inkbytefo

This script provides a comprehensive test runner with various options for running
different types of tests and generating reports.
"""

import os
import sys
import subprocess
import argparse
import time
from pathlib import Path
from typing import List, Optional

# Add backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))


class TestRunner:
    """Test runner class with various test execution options"""
    
    def __init__(self):
        self.backend_dir = backend_dir
        self.test_dir = self.backend_dir / "tests"
        
    def run_unit_tests(self, verbose: bool = False, coverage: bool = True) -> int:
        """Run unit tests"""
        print("🧪 Running Unit Tests...")
        print("=" * 50)
        
        cmd = ["python", "-m", "pytest", "tests/unit/"]
        
        if verbose:
            cmd.append("-v")
        
        if coverage:
            cmd.extend(["--cov=app", "--cov-report=term-missing"])
        
        cmd.append("-m")
        cmd.append("unit")
        
        return self._run_command(cmd)
    
    def run_integration_tests(self, verbose: bool = False) -> int:
        """Run integration tests"""
        print("🔗 Running Integration Tests...")
        print("=" * 50)
        
        cmd = ["python", "-m", "pytest", "tests/integration/"]
        
        if verbose:
            cmd.append("-v")
        
        cmd.append("-m")
        cmd.append("integration")
        
        return self._run_command(cmd)
    
    def run_performance_tests(self, verbose: bool = False) -> int:
        """Run performance tests"""
        print("⚡ Running Performance Tests...")
        print("=" * 50)
        
        cmd = ["python", "-m", "pytest", "tests/performance/"]
        
        if verbose:
            cmd.append("-v")
        
        cmd.append("-m")
        cmd.append("performance")
        
        return self._run_command(cmd)
    
    def run_all_tests(self, verbose: bool = False, coverage: bool = True) -> int:
        """Run all tests"""
        print("🚀 Running All Tests...")
        print("=" * 50)
        
        cmd = ["python", "-m", "pytest", "tests/"]
        
        if verbose:
            cmd.append("-v")
        
        if coverage:
            cmd.extend([
                "--cov=app",
                "--cov-report=term-missing",
                "--cov-report=html:htmlcov",
                "--cov-report=xml"
            ])
        
        return self._run_command(cmd)
    
    def run_specific_test(self, test_path: str, verbose: bool = False) -> int:
        """Run a specific test file or test function"""
        print(f"🎯 Running Specific Test: {test_path}")
        print("=" * 50)
        
        cmd = ["python", "-m", "pytest", test_path]
        
        if verbose:
            cmd.append("-v")
        
        return self._run_command(cmd)
    
    def run_tests_by_marker(self, marker: str, verbose: bool = False) -> int:
        """Run tests by marker"""
        print(f"🏷️ Running Tests with Marker: {marker}")
        print("=" * 50)
        
        cmd = ["python", "-m", "pytest", "-m", marker]
        
        if verbose:
            cmd.append("-v")
        
        return self._run_command(cmd)
    
    def run_failed_tests(self, verbose: bool = False) -> int:
        """Re-run only failed tests"""
        print("🔄 Re-running Failed Tests...")
        print("=" * 50)
        
        cmd = ["python", "-m", "pytest", "--lf"]
        
        if verbose:
            cmd.append("-v")
        
        return self._run_command(cmd)
    
    def generate_coverage_report(self) -> int:
        """Generate detailed coverage report"""
        print("📊 Generating Coverage Report...")
        print("=" * 50)
        
        cmd = [
            "python", "-m", "pytest",
            "--cov=app",
            "--cov-report=html:htmlcov",
            "--cov-report=xml",
            "--cov-report=term-missing",
            "tests/"
        ]
        
        result = self._run_command(cmd)
        
        if result == 0:
            print("\n📈 Coverage reports generated:")
            print("  - HTML: htmlcov/index.html")
            print("  - XML: coverage.xml")
        
        return result
    
    def check_test_environment(self) -> bool:
        """Check if test environment is properly set up"""
        print("🔍 Checking Test Environment...")
        print("=" * 50)
        
        checks = []
        
        # Check if pytest is installed
        try:
            import pytest
            checks.append(("✅", "pytest", f"version {pytest.__version__}"))
        except ImportError:
            checks.append(("❌", "pytest", "not installed"))
        
        # Check if pytest-asyncio is installed
        try:
            import pytest_asyncio
            checks.append(("✅", "pytest-asyncio", f"version {pytest_asyncio.__version__}"))
        except ImportError:
            checks.append(("❌", "pytest-asyncio", "not installed"))
        
        # Check if pytest-cov is installed
        try:
            import pytest_cov
            checks.append(("✅", "pytest-cov", "installed"))
        except ImportError:
            checks.append(("❌", "pytest-cov", "not installed"))
        
        # Check test directory structure
        if self.test_dir.exists():
            checks.append(("✅", "test directory", "exists"))
        else:
            checks.append(("❌", "test directory", "missing"))
        
        # Check for conftest.py
        conftest_path = self.test_dir / "conftest.py"
        if conftest_path.exists():
            checks.append(("✅", "conftest.py", "exists"))
        else:
            checks.append(("❌", "conftest.py", "missing"))
        
        # Print results
        for status, component, message in checks:
            print(f"  {status} {component}: {message}")
        
        # Return True if all checks pass
        return all(check[0] == "✅" for check in checks)
    
    def clean_test_artifacts(self) -> None:
        """Clean test artifacts and cache files"""
        print("🧹 Cleaning Test Artifacts...")
        print("=" * 50)
        
        artifacts = [
            ".pytest_cache",
            "__pycache__",
            "htmlcov",
            "coverage.xml",
            ".coverage",
            "test.db"
        ]
        
        for artifact in artifacts:
            artifact_path = self.backend_dir / artifact
            if artifact_path.exists():
                if artifact_path.is_file():
                    artifact_path.unlink()
                    print(f"  🗑️ Removed file: {artifact}")
                elif artifact_path.is_dir():
                    import shutil
                    shutil.rmtree(artifact_path)
                    print(f"  🗑️ Removed directory: {artifact}")
        
        print("✅ Test artifacts cleaned")
    
    def _run_command(self, cmd: List[str]) -> int:
        """Run a command and return exit code"""
        try:
            print(f"Running: {' '.join(cmd)}")
            print("-" * 50)
            
            start_time = time.time()
            result = subprocess.run(cmd, cwd=self.backend_dir)
            end_time = time.time()
            
            duration = end_time - start_time
            print(f"\n⏱️ Test execution time: {duration:.2f} seconds")
            
            if result.returncode == 0:
                print("✅ Tests completed successfully")
            else:
                print("❌ Tests failed")
            
            return result.returncode
            
        except KeyboardInterrupt:
            print("\n⏹️ Tests interrupted by user")
            return 1
        except Exception as e:
            print(f"\n💥 Error running tests: {e}")
            return 1


def main():
    """Main function for CLI usage"""
    parser = argparse.ArgumentParser(description="Test Runner for AI Crypto Trading System")
    
    parser.add_argument(
        "test_type",
        choices=["unit", "integration", "performance", "all", "specific", "marker", "failed", "coverage", "check", "clean"],
        help="Type of tests to run"
    )
    
    parser.add_argument(
        "--path",
        help="Specific test path (for 'specific' test type)"
    )
    
    parser.add_argument(
        "--marker",
        help="Test marker to run (for 'marker' test type)"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Verbose output"
    )
    
    parser.add_argument(
        "--no-coverage",
        action="store_true",
        help="Disable coverage reporting"
    )
    
    args = parser.parse_args()
    
    print("🧪 AI Crypto Trading System - Test Runner")
    print("Author: inkbytefo")
    print("=" * 60)
    
    runner = TestRunner()
    
    if args.test_type == "check":
        if runner.check_test_environment():
            print("\n🎉 Test environment is ready!")
            return 0
        else:
            print("\n💥 Test environment has issues!")
            return 1
    
    elif args.test_type == "clean":
        runner.clean_test_artifacts()
        return 0
    
    elif args.test_type == "unit":
        return runner.run_unit_tests(args.verbose, not args.no_coverage)
    
    elif args.test_type == "integration":
        return runner.run_integration_tests(args.verbose)
    
    elif args.test_type == "performance":
        return runner.run_performance_tests(args.verbose)
    
    elif args.test_type == "all":
        return runner.run_all_tests(args.verbose, not args.no_coverage)
    
    elif args.test_type == "specific":
        if not args.path:
            print("❌ --path required for specific test type")
            return 1
        return runner.run_specific_test(args.path, args.verbose)
    
    elif args.test_type == "marker":
        if not args.marker:
            print("❌ --marker required for marker test type")
            return 1
        return runner.run_tests_by_marker(args.marker, args.verbose)
    
    elif args.test_type == "failed":
        return runner.run_failed_tests(args.verbose)
    
    elif args.test_type == "coverage":
        return runner.generate_coverage_report()
    
    else:
        print(f"❌ Unknown test type: {args.test_type}")
        return 1


if __name__ == "__main__":
    sys.exit(main())

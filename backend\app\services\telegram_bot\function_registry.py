"""
System Function Registry for AI Assistant
Author: inkbytefo

Registry of system functions that can be called by the AI assistant.
"""

import json
import logging
from typing import Dict, Any, List, Callable, Optional
from datetime import datetime

from app.services.market_data_service import MarketDataService
from app.services.technical_analysis_service import TechnicalAnalysisService
from app.services.pattern_detection_service import PatternDetectionService
from app.services.signal_generation_service import SignalGenerationService
from app.services.ai_analysis_service import AIAnalysisService

logger = logging.getLogger(__name__)


class SystemFunctionRegistry:
    """
    Registry of system functions that can be called by the AI assistant.
    Maps natural language intents to system API calls.
    """
    
    def __init__(self):
        self.functions: Dict[str, Callable] = {}
        self.function_definitions: List[Dict[str, Any]] = []
        
        # Initialize services
        self.market_service = MarketDataService()
        self.technical_service = TechnicalAnalysisService()
        self.pattern_service = PatternDetectionService()
        self.signal_service = SignalGenerationService()
        self.ai_service = AIAnalysisService()
        
    async def initialize(self) -> None:
        """Initialize the function registry."""
        try:
            # Register all available functions
            self._register_functions()
            
            logger.info(f"Function registry initialized with {len(self.functions)} functions")
            
        except Exception as e:
            logger.error(f"Failed to initialize function registry: {e}")
            raise
    
    def _register_functions(self) -> None:
        """Register all available system functions."""
        
        # Portfolio and account functions
        self._register_function(
            name="get_portfolio_status",
            function=self._get_portfolio_status,
            description="Get current portfolio status, holdings, and performance",
            parameters={
                "type": "object",
                "properties": {
                    "include_details": {
                        "type": "boolean",
                        "description": "Include detailed breakdown of holdings"
                    }
                }
            }
        )
        
        # Crypto analysis functions
        self._register_function(
            name="get_crypto_analysis",
            function=self._get_crypto_analysis,
            description="Get comprehensive analysis for a cryptocurrency",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {
                        "type": "string",
                        "description": "Cryptocurrency symbol (e.g., BTC, ETH)"
                    },
                    "timeframe": {
                        "type": "string",
                        "description": "Analysis timeframe (1h, 4h, 1d)",
                        "default": "1h"
                    },
                    "include_ai": {
                        "type": "boolean",
                        "description": "Include AI analysis and sentiment",
                        "default": True
                    }
                },
                "required": ["symbol"]
            }
        )
        
        # Trading signals functions
        self._register_function(
            name="get_trading_signals",
            function=self._get_trading_signals,
            description="Get latest trading signals and recommendations",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {
                        "type": "string",
                        "description": "Specific cryptocurrency symbol, or 'all' for all symbols"
                    },
                    "signal_type": {
                        "type": "string",
                        "description": "Type of signals (buy, sell, all)",
                        "default": "all"
                    },
                    "limit": {
                        "type": "integer",
                        "description": "Number of signals to return",
                        "default": 5
                    }
                }
            }
        )
        
        # Market overview functions
        self._register_function(
            name="get_market_overview",
            function=self._get_market_overview,
            description="Get general market overview and top movers",
            parameters={
                "type": "object",
                "properties": {
                    "include_news": {
                        "type": "boolean",
                        "description": "Include latest market news",
                        "default": True
                    }
                }
            }
        )
        
        # Price alert functions
        self._register_function(
            name="set_price_alert",
            function=self._set_price_alert,
            description="Set price alert for a cryptocurrency",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {
                        "type": "string",
                        "description": "Cryptocurrency symbol"
                    },
                    "price": {
                        "type": "number",
                        "description": "Target price for alert"
                    },
                    "condition": {
                        "type": "string",
                        "description": "Alert condition (above, below)",
                        "default": "above"
                    }
                },
                "required": ["symbol", "price"]
            }
        )
        
        # System status functions
        self._register_function(
            name="get_system_status",
            function=self._get_system_status,
            description="Get current system status and health",
            parameters={
                "type": "object",
                "properties": {
                    "detailed": {
                        "type": "boolean",
                        "description": "Include detailed component status",
                        "default": False
                    }
                }
            }
        )
    
    def _register_function(
        self, 
        name: str, 
        function: Callable, 
        description: str, 
        parameters: Dict[str, Any]
    ) -> None:
        """Register a single function."""
        self.functions[name] = function
        
        self.function_definitions.append({
            "name": name,
            "description": description,
            "parameters": parameters
        })
    
    async def get_function_definitions(self) -> List[Dict[str, Any]]:
        """Get OpenAI function definitions for function calling."""
        return self.function_definitions
    
    async def execute_function(
        self, 
        function_name: str, 
        arguments: Dict[str, Any],
        user_id: int,
        context: Dict[str, Any]
    ) -> Any:
        """Execute a registered function."""
        if function_name not in self.functions:
            return {"error": f"Function {function_name} not found"}
        
        try:
            # Add user context to arguments
            arguments["user_id"] = user_id
            arguments["context"] = context
            
            # Execute function
            result = await self.functions[function_name](**arguments)
            return result
            
        except Exception as e:
            logger.error(f"Error executing function {function_name}: {e}")
            return {"error": f"Function execution failed: {str(e)}"}
    
    # Function implementations
    
    async def _get_portfolio_status(self, user_id: int, context: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """Get portfolio status for user."""
        try:
            from app.services.portfolio_management_service import portfolio_management_service

            # Get portfolio summary
            portfolio_summary = await portfolio_management_service.get_portfolio_summary(user_id)

            if not portfolio_summary:
                return {
                    "message": "❌ Portföy bilgisi alınamadı. Lütfen daha sonra tekrar deneyin.",
                    "data": {"error": "Portfolio data not available"}
                }

            metrics = portfolio_summary.metrics
            allocations = portfolio_summary.allocations[:5]  # Top 5 holdings

            # Format message
            message = f"📊 **Portföy Durumu**\n\n"
            message += f"💰 **Toplam Değer**: ${metrics.total_value_usd:,.2f}\n"
            message += f"📈 **Toplam P&L**: {metrics.total_pnl_percentage:+.2f}% (${metrics.total_pnl:+,.2f})\n"
            message += f"📅 **Günlük Değişim**: {metrics.daily_pnl_percentage:+.2f}% (${metrics.daily_pnl:+,.2f})\n"
            message += f"📊 **Haftalık Değişim**: {metrics.weekly_pnl_percentage:+.2f}% (${metrics.weekly_pnl:+,.2f})\n\n"

            if allocations:
                message += "**En Büyük Pozisyonlar:**\n"
                for allocation in allocations:
                    pnl_emoji = "📈" if allocation.unrealized_pnl_percentage >= 0 else "📉"
                    message += f"• {allocation.symbol}: {allocation.quantity:.4f} "
                    message += f"(${allocation.total_value_usd:,.2f}) "
                    message += f"{pnl_emoji} {allocation.unrealized_pnl_percentage:+.2f}%\n"
            else:
                message += "**Aktif pozisyon bulunmuyor**\n"

            # Add performance metrics
            message += f"\n📊 **Performans Metrikleri:**\n"
            message += f"• Max Drawdown: {metrics.max_drawdown:.2f}%\n"
            message += f"• Sharpe Ratio: {metrics.sharpe_ratio:.2f}\n"
            message += f"• Toplam İşlem: {metrics.total_trades}\n"
            message += f"• Kazanma Oranı: {metrics.win_rate:.1f}%\n"

            return {
                "message": message,
                "data": {
                    "total_value": metrics.total_value_usd,
                    "daily_change": metrics.daily_pnl_percentage,
                "holdings": [
                    {"symbol": "BTC", "amount": 0.25, "value": 10500},
                    {"symbol": "ETH", "amount": 2.1, "value": 3150},
                    {"symbol": "ADA", "amount": 1000, "value": 950}
                ]
            }
        }
    
    async def _get_crypto_analysis(self, symbol: str, user_id: int, context: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """Get comprehensive crypto analysis."""
        try:
            timeframe = kwargs.get("timeframe", "1h")
            include_ai = kwargs.get("include_ai", True)
            
            # Get combined analysis from existing services
            if include_ai:
                # Use combined analysis endpoint
                analysis = await self.signal_service.get_combined_analysis(symbol, timeframe)
            else:
                # Get technical analysis only
                analysis = await self.technical_service.get_enhanced_analysis(symbol, timeframe)
            
            # Format response
            return {
                "message": f"📊 **{symbol.upper()} Analizi**\n\nAnaliz sonuçları hazırlandı.",
                "data": analysis,
                "actions": []
            }
            
        except Exception as e:
            return {"error": f"{symbol} analizi alınamadı: {str(e)}"}
    
    async def _get_trading_signals(self, user_id: int, context: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """Get trading signals."""
        try:
            symbol = kwargs.get("symbol", "all")
            limit = kwargs.get("limit", 5)
            
            if symbol == "all":
                # Get market summary
                signals = await self.signal_service.get_market_summary()
            else:
                # Get specific symbol signals
                signal = await self.signal_service.generate_signal(symbol)
                signals = [signal] if signal else []
            
            return {
                "message": f"🔔 **Trading Sinyalleri**\n\n{len(signals)} sinyal bulundu.",
                "data": signals,
                "actions": []
            }
            
        except Exception as e:
            return {"error": f"Trading sinyalleri alınamadı: {str(e)}"}
    
    async def _get_market_overview(self, user_id: int, context: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """Get market overview."""
        try:
            include_news = kwargs.get("include_news", True)
            
            # Get market summary
            market_data = await self.signal_service.get_market_summary()
            
            overview_text = "📈 **Piyasa Genel Durumu**\n\nPiyasa verileri güncellendi."
            
            result = {
                "message": overview_text,
                "data": market_data,
                "actions": []
            }
            
            if include_news:
                # Add AI market overview
                try:
                    ai_overview = await self.ai_service.get_market_overview()
                    result["ai_insights"] = ai_overview
                except Exception:
                    pass
            
            return result
            
        except Exception as e:
            return {"error": f"Piyasa durumu alınamadı: {str(e)}"}
    
    async def _set_price_alert(self, symbol: str, price: float, user_id: int, context: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """Set price alert."""
        try:
            condition = kwargs.get("condition", "above")
            recurring = kwargs.get("recurring", False)

            # Import notification engine
            from app.services.telegram_bot.notification_engine import notification_engine, PriceAlert
            from datetime import datetime

            # Create price alert
            alert = PriceAlert(
                user_id=user_id,
                symbol=symbol.upper(),
                target_price=price,
                condition=condition,
                created_at=datetime.now(),
                is_active=True,
                recurring=recurring
            )

            # Add alert to notification engine
            if user_id not in notification_engine.price_alerts:
                notification_engine.price_alerts[user_id] = []

            notification_engine.price_alerts[user_id].append(alert)

            # Format condition text
            condition_text = {
                "above": "üstüne çıktığında",
                "below": "altına düştüğünde",
                "equals": "seviyesine ulaştığında"
            }.get(condition, condition)

            return {
                "message": f"🚨 **Fiyat Alarmı Kuruldu**\n\n{symbol.upper()} ${price:.4f} {condition_text} bildirim alacaksınız.\n\n{'🔄 Tekrarlanan alarm' if recurring else '⚡ Tek seferlik alarm'}",
                "actions": [{
                    "type": "set_alert",
                    "config": {
                        "symbol": symbol,
                        "price": price,
                        "condition": condition,
                        "user_id": user_id,
                        "recurring": recurring
                    }
                }]
            }

        except Exception as e:
            return {"error": f"Fiyat alarmı kurulamadı: {str(e)}"}
    
    async def _get_system_status(self, user_id: int, context: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """Get system status."""
        try:
            from app.services.telegram_bot.bot_manager import TelegramBotManager

            # Get system status from bot manager
            bot_manager = TelegramBotManager()
            status = await bot_manager._get_system_status()

            # Format message
            message = "⚙️ **Sistem Durumu**\n\n"

            # Check overall health
            all_green = all("🟢" in status_value for status_value in status.values())

            if all_green:
                message += "🟢 Tüm sistemler normal çalışıyor.\n\n"
            else:
                message += "⚠️ Bazı sistemlerde sorun tespit edildi.\n\n"

            # Add individual status
            status_labels = {
                "api_status": "API Bağlantısı",
                "db_status": "Veritabanı",
                "ai_status": "AI Servisi",
                "market_status": "Market Verisi",
                "trading_status": "Trading Engine"
            }

            for key, value in status.items():
                label = status_labels.get(key, key)
                message += f"• {label}: {value}\n"

            # Convert status to simple format for data
            data_status = {}
            for key, value in status.items():
                if "🟢" in value:
                    data_status[key] = "active"
                elif "🟡" in value:
                    data_status[key] = "limited"
                else:
                    data_status[key] = "error"

            return {
                "message": message,
                "data": data_status
            }

        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {
                "message": "❌ Sistem durumu alınamadı. Lütfen daha sonra tekrar deneyin.",
                "data": {"error": "System status unavailable"}
            }

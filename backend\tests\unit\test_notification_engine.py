"""
Unit tests for Notification Engine
Author: inkbytefo
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch

from app.services.telegram_bot.notification_engine import (
    NotificationEngine,
    PriceAlert,
    Notification,
    NotificationType
)


@pytest.fixture
def notification_engine():
    """Create notification engine instance."""
    engine = NotificationEngine()
    return engine


@pytest.fixture
def sample_price_alert():
    """Sample price alert for testing."""
    return PriceAlert(
        user_id=1,
        symbol="BTC",
        target_price=50000.0,
        condition="above",
        created_at=datetime.now(),
        is_active=True,
        recurring=False
    )


@pytest.fixture
def sample_notification():
    """Sample notification for testing."""
    return Notification(
        user_id=1,
        type=NotificationType.PRICE_ALERT,
        title="Test Alert",
        message="Test message",
        data={"symbol": "BTC", "price": 50000.0}
    )


class TestNotificationEngine:
    """Test cases for Notification Engine."""

    def test_notification_creation(self, sample_notification):
        """Test notification object creation."""
        assert sample_notification.user_id == 1
        assert sample_notification.type == NotificationType.PRICE_ALERT
        assert sample_notification.title == "Test Alert"
        assert sample_notification.message == "Test message"
        assert isinstance(sample_notification.data, dict)
        assert isinstance(sample_notification.created_at, datetime)

    def test_price_alert_creation(self, sample_price_alert):
        """Test price alert object creation."""
        assert sample_price_alert.user_id == 1
        assert sample_price_alert.symbol == "BTC"
        assert sample_price_alert.target_price == 50000.0
        assert sample_price_alert.condition == "above"
        assert sample_price_alert.is_active is True
        assert sample_price_alert.recurring is False

    @pytest.mark.asyncio
    async def test_add_price_alert(self, notification_engine, sample_price_alert):
        """Test adding price alert."""
        user_id = sample_price_alert.user_id
        
        # Add alert
        notification_engine.price_alerts[user_id] = [sample_price_alert]
        
        assert user_id in notification_engine.price_alerts
        assert len(notification_engine.price_alerts[user_id]) == 1
        assert notification_engine.price_alerts[user_id][0] == sample_price_alert

    @pytest.mark.asyncio
    async def test_send_notification(self, notification_engine, sample_notification):
        """Test sending notification."""
        with patch.object(notification_engine, '_send_to_telegram') as mock_send:
            mock_send.return_value = True
            
            result = await notification_engine.send_notification(sample_notification)
            
            assert result is True
            mock_send.assert_called_once()

    @pytest.mark.asyncio
    async def test_check_price_alerts_trigger_above(self, notification_engine):
        """Test price alert triggering for 'above' condition."""
        # Create alert for BTC above $45000
        alert = PriceAlert(
            user_id=1,
            symbol="BTC",
            target_price=45000.0,
            condition="above",
            created_at=datetime.now(),
            is_active=True,
            recurring=False
        )
        
        notification_engine.price_alerts[1] = [alert]
        
        # Mock market data service to return price above threshold
        with patch('app.services.telegram_bot.notification_engine.market_data_service') as mock_market:
            mock_market.get_ticker_data.return_value = {"price": "46000.0"}
            
            with patch.object(notification_engine, '_send_price_alert_notification') as mock_send:
                await notification_engine._check_price_alerts()
                
                mock_send.assert_called_once()
                # Alert should be deactivated (non-recurring)
                assert not alert.is_active

    @pytest.mark.asyncio
    async def test_check_price_alerts_trigger_below(self, notification_engine):
        """Test price alert triggering for 'below' condition."""
        # Create alert for BTC below $45000
        alert = PriceAlert(
            user_id=1,
            symbol="BTC",
            target_price=45000.0,
            condition="below",
            created_at=datetime.now(),
            is_active=True,
            recurring=False
        )
        
        notification_engine.price_alerts[1] = [alert]
        
        # Mock market data service to return price below threshold
        with patch('app.services.telegram_bot.notification_engine.market_data_service') as mock_market:
            mock_market.get_ticker_data.return_value = {"price": "44000.0"}
            
            with patch.object(notification_engine, '_send_price_alert_notification') as mock_send:
                await notification_engine._check_price_alerts()
                
                mock_send.assert_called_once()
                assert not alert.is_active

    @pytest.mark.asyncio
    async def test_check_price_alerts_no_trigger(self, notification_engine):
        """Test price alert not triggering when condition not met."""
        # Create alert for BTC above $50000
        alert = PriceAlert(
            user_id=1,
            symbol="BTC",
            target_price=50000.0,
            condition="above",
            created_at=datetime.now(),
            is_active=True,
            recurring=False
        )
        
        notification_engine.price_alerts[1] = [alert]
        
        # Mock market data service to return price below threshold
        with patch('app.services.telegram_bot.notification_engine.market_data_service') as mock_market:
            mock_market.get_ticker_data.return_value = {"price": "49000.0"}
            
            with patch.object(notification_engine, '_send_price_alert_notification') as mock_send:
                await notification_engine._check_price_alerts()
                
                mock_send.assert_not_called()
                # Alert should remain active
                assert alert.is_active

    @pytest.mark.asyncio
    async def test_recurring_alert_stays_active(self, notification_engine):
        """Test that recurring alerts stay active after triggering."""
        # Create recurring alert
        alert = PriceAlert(
            user_id=1,
            symbol="BTC",
            target_price=45000.0,
            condition="above",
            created_at=datetime.now(),
            is_active=True,
            recurring=True
        )
        
        notification_engine.price_alerts[1] = [alert]
        
        # Mock market data service to return price above threshold
        with patch('app.services.telegram_bot.notification_engine.market_data_service') as mock_market:
            mock_market.get_ticker_data.return_value = {"price": "46000.0"}
            
            with patch.object(notification_engine, '_send_price_alert_notification') as mock_send:
                await notification_engine._check_price_alerts()
                
                mock_send.assert_called_once()
                # Recurring alert should stay active
                assert alert.is_active

    @pytest.mark.asyncio
    async def test_portfolio_alerts(self, notification_engine):
        """Test portfolio alert checking."""
        # Add user to price alerts to trigger portfolio check
        notification_engine.price_alerts[1] = []
        
        with patch('app.services.telegram_bot.notification_engine.portfolio_management_service') as mock_portfolio:
            mock_portfolio.get_portfolio_summary.return_value = {
                "performance": {
                    "day_1": {"percentage": -6.0}  # Large loss
                },
                "total_value_usd": 50.0  # Low value
            }
            
            with patch.object(notification_engine, '_send_portfolio_alert') as mock_send:
                await notification_engine._check_portfolio_alerts()
                
                # Should send alerts for both large loss and low value
                assert mock_send.call_count >= 1

    @pytest.mark.asyncio
    async def test_system_alerts(self, notification_engine):
        """Test system alert checking."""
        with patch('app.services.telegram_bot.notification_engine.TelegramBotManager') as mock_bot_manager:
            mock_manager_instance = mock_bot_manager.return_value
            mock_manager_instance._get_system_status.return_value = {
                "database": "🔴 Error",
                "api": "🟢 OK"
            }
            
            with patch.object(notification_engine, '_send_system_alert') as mock_send:
                await notification_engine._check_system_alerts()
                
                mock_send.assert_called_once()

    @pytest.mark.asyncio
    async def test_start_stop_monitoring(self, notification_engine):
        """Test starting and stopping monitoring."""
        # Start monitoring
        await notification_engine.start()
        assert notification_engine._monitoring_task is not None
        
        # Stop monitoring
        await notification_engine.stop()
        assert notification_engine._monitoring_task is None or notification_engine._monitoring_task.cancelled()

    def test_get_statistics(self, notification_engine):
        """Test getting notification statistics."""
        # Add some test data
        notification_engine.price_alerts[1] = [
            PriceAlert(1, "BTC", 50000.0, "above", datetime.now(), True, False),
            PriceAlert(1, "ETH", 3000.0, "below", datetime.now(), False, True)
        ]
        notification_engine.price_alerts[2] = [
            PriceAlert(2, "BNB", 400.0, "above", datetime.now(), True, False)
        ]
        
        stats = notification_engine.get_statistics()
        
        assert isinstance(stats, dict)
        assert stats["total_users_with_alerts"] == 2
        assert stats["total_alerts"] == 3
        assert stats["active_alerts"] == 2


if __name__ == "__main__":
    pytest.main([__file__])

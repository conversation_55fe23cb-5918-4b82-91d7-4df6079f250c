# AI Crypto Trading System - Environment Configuration
# Author: inkbytefo
#
# Copy this file to .env and fill in your actual values
# DO NOT commit .env file to version control

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

# Environment (development, staging, production)
ENVIRONMENT=development

# Debug mode (true/false)
DEBUG=true

# Application name
APP_NAME="AI Crypto Trading System"

# API settings
API_HOST=0.0.0.0
API_PORT=8000
API_RELOAD=true

# Secret key for JWT tokens (generate a secure random string)
SECRET_KEY=your_super_secret_key_here_change_this_in_production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# =============================================================================
# DATABASE SETTINGS
# =============================================================================

# PostgreSQL Database URL
DATABASE_URL=postgresql://username:password@localhost:5432/crypto_trading

# Redis Cache URL
REDIS_URL=redis://localhost:6379/0

# Database pool settings
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30

# Cache TTL settings (in seconds)
CACHE_TTL_DEFAULT=300
CACHE_TTL_MARKET_DATA=60
CACHE_TTL_USER_DATA=1800

# =============================================================================
# TRADING SETTINGS
# =============================================================================

# Enable/disable actual trading (true/false)
ENABLE_TRADING=false

# Trading mode (testnet/live)
TRADING_MODE=testnet

# Use testnet for trading (true/false)
TRADING_TESTNET=true

# Risk settings
RISK_LEVEL=low
DEFAULT_RISK_PERCENTAGE=2.0
MAX_POSITION_SIZE=0.1
MAX_DAILY_TRADES=10
MAX_DRAWDOWN=0.20
DEFAULT_STOP_LOSS=0.05
DEFAULT_TAKE_PROFIT=0.15

# =============================================================================
# EXCHANGE API SETTINGS
# =============================================================================

# Binance API credentials
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_SECRET_KEY=your_binance_secret_key_here

# CoinGecko API
COINGECKO_API_KEY=your_coingecko_api_key_here

# =============================================================================
# AI SERVICE SETTINGS
# =============================================================================

# OpenAI API settings
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=2000

# Mistral AI settings (alternative to OpenAI)
MISTRAL_API_KEY=your-mistral-api-key
MISTRAL_MODEL=mistral-large-latest

# AI service provider (openai, mistral)
AI_PROVIDER=openai

# AI cache settings
AI_ANALYSIS_CACHE_TTL=3600
NEWS_CACHE_TTL=1800
SENTIMENT_CACHE_TTL=1800

# =============================================================================
# TELEGRAM BOT SETTINGS
# =============================================================================

# Telegram Bot Token (get from @BotFather)
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here

# Telegram settings
TELEGRAM_CHAT_ID=your_telegram_chat_id_here
TELEGRAM_AUTHORIZED_USERS=123456789,987654321
TELEGRAM_ADMIN_USERS=123456789
TELEGRAM_WEBHOOK_URL=https://your-domain.com/api/v1/telegram/webhook
TELEGRAM_WEBHOOK_SECRET=your_webhook_secret_token

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================

# News API
NEWSAPI_KEY=your_newsapi_key_here

# =============================================================================
# LOGGING SETTINGS
# =============================================================================

# Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO
LOG_FILE=logs/trading.log

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable/disable specific features
ENABLE_PATTERN_DETECTION=true
ENABLE_AI_ANALYSIS=true
ENABLE_RISK_MANAGEMENT=true
ENABLE_PORTFOLIO_TRACKING=true
ENABLE_BACKTESTING=false
ENABLE_PAPER_TRADING=true

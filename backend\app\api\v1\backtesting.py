"""
Backtesting API Endpoints
Author: inkbytefo

API endpoints for managing backtesting strategies and running backtests.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime

from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.user import User
from app.models.backtesting import (
    BacktestStrategy, BacktestRun, BacktestTrade,
    BacktestStrategyCreate, BacktestRunCreate, BacktestRunResponse
)
from app.services.backtesting_engine import backtesting_engine

router = APIRouter()


@router.post("/strategies", response_model=dict)
async def create_strategy(
    strategy_data: BacktestStrategyCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new backtesting strategy"""
    try:
        strategy = BacktestStrategy(
            user_id=current_user.id,
            name=strategy_data.name,
            description=strategy_data.description,
            strategy_type=strategy_data.strategy_type,
            parameters=strategy_data.parameters,
            risk_parameters=strategy_data.risk_parameters,
            entry_conditions=strategy_data.entry_conditions,
            exit_conditions=strategy_data.exit_conditions,
            min_confidence=strategy_data.min_confidence,
            max_position_size=strategy_data.max_position_size,
            stop_loss_percentage=strategy_data.stop_loss_percentage,
            take_profit_percentage=strategy_data.take_profit_percentage,
            created_by=current_user.username or current_user.email
        )
        
        db.add(strategy)
        db.commit()
        db.refresh(strategy)
        
        return {
            "status": "success",
            "message": "Strategy created successfully",
            "strategy_id": strategy.id,
            "strategy_uuid": strategy.strategy_uuid
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating strategy: {str(e)}"
        )


@router.get("/strategies", response_model=List[dict])
async def list_strategies(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000)
):
    """List user's backtesting strategies"""
    strategies = db.query(BacktestStrategy).filter(
        BacktestStrategy.user_id == current_user.id,
        BacktestStrategy.is_active == True
    ).offset(skip).limit(limit).all()
    
    return [
        {
            "id": strategy.id,
            "strategy_uuid": strategy.strategy_uuid,
            "name": strategy.name,
            "description": strategy.description,
            "strategy_type": strategy.strategy_type,
            "version": strategy.version,
            "min_confidence": strategy.min_confidence,
            "max_position_size": strategy.max_position_size,
            "stop_loss_percentage": strategy.stop_loss_percentage,
            "take_profit_percentage": strategy.take_profit_percentage,
            "created_at": strategy.created_at,
            "updated_at": strategy.updated_at
        }
        for strategy in strategies
    ]


@router.get("/strategies/{strategy_id}", response_model=dict)
async def get_strategy(
    strategy_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get detailed strategy information"""
    strategy = db.query(BacktestStrategy).filter(
        BacktestStrategy.id == strategy_id,
        BacktestStrategy.user_id == current_user.id
    ).first()
    
    if not strategy:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Strategy not found"
        )
    
    return {
        "id": strategy.id,
        "strategy_uuid": strategy.strategy_uuid,
        "name": strategy.name,
        "description": strategy.description,
        "strategy_type": strategy.strategy_type,
        "version": strategy.version,
        "parameters": strategy.parameters,
        "risk_parameters": strategy.risk_parameters,
        "entry_conditions": strategy.entry_conditions,
        "exit_conditions": strategy.exit_conditions,
        "min_confidence": strategy.min_confidence,
        "max_position_size": strategy.max_position_size,
        "stop_loss_percentage": strategy.stop_loss_percentage,
        "take_profit_percentage": strategy.take_profit_percentage,
        "is_active": strategy.is_active,
        "created_at": strategy.created_at,
        "updated_at": strategy.updated_at,
        "created_by": strategy.created_by
    }


@router.post("/runs", response_model=dict)
async def create_backtest_run(
    run_data: BacktestRunCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Start a new backtest run"""
    try:
        # Validate strategy exists and belongs to user
        strategy = db.query(BacktestStrategy).filter(
            BacktestStrategy.id == run_data.strategy_id,
            BacktestStrategy.user_id == current_user.id
        ).first()
        
        if not strategy:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Strategy not found"
            )
        
        # Validate date range
        if run_data.start_date >= run_data.end_date:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Start date must be before end date"
            )
        
        # Start backtest
        backtest_run_id = await backtesting_engine.run_backtest(
            strategy_id=run_data.strategy_id,
            symbols=run_data.symbols,
            timeframes=run_data.timeframes,
            start_date=run_data.start_date,
            end_date=run_data.end_date,
            initial_capital=run_data.initial_capital,
            user_id=current_user.id,
            name=run_data.name,
            description=run_data.description
        )
        
        return {
            "status": "success",
            "message": "Backtest started successfully",
            "backtest_run_id": backtest_run_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting backtest: {str(e)}"
        )


@router.get("/runs", response_model=List[BacktestRunResponse])
async def list_backtest_runs(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    strategy_id: Optional[int] = Query(None),
    status_filter: Optional[str] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000)
):
    """List user's backtest runs"""
    query = db.query(BacktestRun).filter(
        BacktestRun.user_id == current_user.id
    )
    
    if strategy_id:
        query = query.filter(BacktestRun.strategy_id == strategy_id)
    
    if status_filter:
        query = query.filter(BacktestRun.status == status_filter)
    
    runs = query.order_by(BacktestRun.created_at.desc()).offset(skip).limit(limit).all()
    
    return [BacktestRunResponse.from_orm(run) for run in runs]


@router.get("/runs/{run_id}", response_model=BacktestRunResponse)
async def get_backtest_run(
    run_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get detailed backtest run information"""
    run = db.query(BacktestRun).filter(
        BacktestRun.id == run_id,
        BacktestRun.user_id == current_user.id
    ).first()
    
    if not run:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Backtest run not found"
        )
    
    return BacktestRunResponse.from_orm(run)


@router.get("/runs/{run_id}/trades", response_model=List[dict])
async def get_backtest_trades(
    run_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(1000, ge=1, le=10000)
):
    """Get trades from a backtest run"""
    # Verify run belongs to user
    run = db.query(BacktestRun).filter(
        BacktestRun.id == run_id,
        BacktestRun.user_id == current_user.id
    ).first()
    
    if not run:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Backtest run not found"
        )
    
    trades = db.query(BacktestTrade).filter(
        BacktestTrade.backtest_run_id == run_id
    ).offset(skip).limit(limit).all()
    
    return [
        {
            "id": trade.id,
            "trade_uuid": trade.trade_uuid,
            "symbol": trade.symbol,
            "timeframe": trade.timeframe,
            "trade_type": trade.trade_type,
            "entry_date": trade.entry_date,
            "entry_price": trade.entry_price,
            "exit_date": trade.exit_date,
            "exit_price": trade.exit_price,
            "exit_reason": trade.exit_reason,
            "quantity": trade.quantity,
            "pnl": trade.pnl,
            "pnl_percentage": trade.pnl_percentage,
            "fees": trade.fees,
            "net_pnl": trade.net_pnl,
            "duration_hours": trade.duration_hours,
            "is_winning_trade": trade.is_winning_trade,
            "entry_signal_strength": trade.entry_signal_strength,
            "entry_confidence": trade.entry_confidence,
            "max_favorable_excursion": trade.max_favorable_excursion,
            "max_adverse_excursion": trade.max_adverse_excursion
        }
        for trade in trades
    ]


@router.delete("/runs/{run_id}")
async def cancel_backtest_run(
    run_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Cancel a running backtest"""
    run = db.query(BacktestRun).filter(
        BacktestRun.id == run_id,
        BacktestRun.user_id == current_user.id
    ).first()
    
    if not run:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Backtest run not found"
        )
    
    if run.status not in ["pending", "running"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Can only cancel pending or running backtests"
        )
    
    run.status = "cancelled"
    run.completed_at = datetime.now()
    db.commit()
    
    return {"status": "success", "message": "Backtest cancelled successfully"}


@router.get("/runs/{run_id}/performance", response_model=dict)
async def get_backtest_performance(
    run_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get detailed performance analysis for a backtest run"""
    run = db.query(BacktestRun).filter(
        BacktestRun.id == run_id,
        BacktestRun.user_id == current_user.id
    ).first()
    
    if not run:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Backtest run not found"
        )
    
    if run.status != "completed":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Backtest is not completed yet"
        )
    
    return {
        "basic_metrics": {
            "total_return": run.total_return,
            "total_return_percentage": run.total_return_percentage,
            "final_capital": run.final_capital,
            "initial_capital": run.initial_capital,
            "total_trades": run.total_trades,
            "winning_trades": run.winning_trades,
            "losing_trades": run.losing_trades,
            "win_rate": run.win_rate
        },
        "risk_metrics": {
            "max_drawdown": run.max_drawdown,
            "max_drawdown_percentage": run.max_drawdown_percentage,
            "sharpe_ratio": run.sharpe_ratio,
            "sortino_ratio": run.sortino_ratio,
            "calmar_ratio": run.calmar_ratio,
            "volatility": run.volatility
        },
        "trade_metrics": {
            "avg_win": run.avg_win,
            "avg_loss": run.avg_loss,
            "profit_factor": run.profit_factor,
            "avg_trade_duration": run.avg_trade_duration
        },
        "detailed_results": run.detailed_results
    }

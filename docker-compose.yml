version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: crypto_trading_db
    environment:
      POSTGRES_DB: crypto_trading
      POSTGRES_USER: trader
      POSTGRES_PASSWORD: secure_password_123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - crypto_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: crypto_trading_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - crypto_network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: crypto_trading_backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=*****************************************************/crypto_trading
      - REDIS_URL=redis://redis:6379/0
      - ENVIRONMENT=development
    volumes:
      - ./backend:/app
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - crypto_network
    restart: unless-stopped

  # Telegram <PERSON> (AI Assistant)
  telegram_bot:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: crypto_trading_telegram_bot
    environment:
      - DATABASE_URL=*****************************************************/crypto_trading
      - REDIS_URL=redis://redis:6379/0
      - ENVIRONMENT=production
    volumes:
      - ./backend:/app
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - crypto_network
    restart: unless-stopped
    command: ["python", "simple_working_bot.py"]

volumes:
  postgres_data:
  redis_data:

networks:
  crypto_network:
    driver: bridge

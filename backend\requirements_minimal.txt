# Minimal Requirements for AI Crypto Trading System
# Author: inkbytefo

# Core Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Database (Choose one)
sqlalchemy==2.0.23
# PostgreSQL support
psycopg2-binary==2.9.9
# Redis cache (optional)
redis==5.0.1

# Crypto Trading
ccxt==4.1.64

# Data Analysis
pandas==2.1.4
numpy==1.26.4
scipy==1.11.4

# AI & ML
openai==1.54.4
requests==2.31.0
aiohttp==3.9.1
feedparser==6.0.10

# Telegram Bot
python-telegram-bot==22.2

# Utilities
python-dotenv==1.0.0
httpx>=0.27.0,<0.29.0
email-validator==2.1.0

# Security
cryptography>=41.0.0

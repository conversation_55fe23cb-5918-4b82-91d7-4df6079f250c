# AI Crypto Trading System - Backend Documentation

**Author:** inkbytefo  
**Version:** 1.0.0  
**Last Updated:** 2025-01-26

## 📋 Overview

Bu backend sistemi, Telegram Bot arayüzü ile kontrol edilen AI destekli kripto para trading sistemidir. Sistem, doğal dil işleme ile kullanıcı komutlarını anlayıp, teknik analiz ve AI önerileri sunar.

## 🏗️ Sistem Mimarisi

### Core Components

1. **Telegram Bot Interface** (`simple_working_bot.py`)
   - Ana kullanıcı arayüzü
   - AI destekli doğal dil işleme
   - Mistral AI entegrasyonu

2. **FastAPI Backend** (`app/main.py`)
   - RESTful API endpoints
   - Async/await architecture
   - CORS middleware

3. **Database Layer** (`app/core/database.py`)
   - SQLAlchemy ORM
   - PostgreSQL/SQLite support
   - Redis cache (optional)

4. **Services Layer** (`app/services/`)
   - Market data service
   - Technical analysis service
   - Pattern detection service
   - Signal generation service
   - AI analysis service

## 📁 Directory Structure

```
backend/
├── app/
│   ├── api/                    # API endpoints
│   │   ├── routes.py          # Main API router
│   │   └── v1/                # API v1 endpoints
│   ├── core/                  # Core functionality
│   │   ├── config.py          # Configuration management
│   │   ├── database.py        # Database setup
│   │   └── logging.py         # Logging configuration
│   ├── models/                # Database models
│   │   ├── market_data.py     # Market data models
│   │   ├── user.py            # User models
│   │   ├── trading.py         # Trading models
│   │   ├── system.py          # System models
│   │   └── technical_analysis.py # TA models
│   └── services/              # Business logic
│       ├── market_data_service.py
│       ├── technical_analysis_service.py
│       ├── pattern_detection_service.py
│       ├── signal_generation_service.py
│       ├── ai_analysis_service.py
│       └── telegram_bot/      # Telegram bot services
├── simple_working_bot.py      # Main Telegram bot
├── requirements.txt           # Python dependencies
├── Dockerfile                 # Docker configuration
└── README_BACKEND.md          # This file
```

## 🔧 Configuration

### Environment Variables (.env)

```bash
# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/crypto_trading
REDIS_URL=redis://localhost:6379/0

# Telegram Bot
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_CHAT_ID=your_chat_id
TELEGRAM_AUTHORIZED_USERS=user_id1,user_id2
TELEGRAM_ADMIN_USERS=admin_user_id

# AI Configuration
OPENAI_API_KEY=your_mistral_api_key
OPENAI_BASE_URL=https://api.mistral.ai/v1
OPENAI_MODEL=mistral-medium-latest

# Trading Configuration
ENABLE_TRADING=false
TRADING_MODE=simulation
RISK_LEVEL=medium
MAX_DAILY_TRADES=10
MAX_POSITION_SIZE=0.1

# Exchange APIs
BINANCE_API_KEY=your_binance_api_key
BINANCE_SECRET_KEY=your_binance_secret_key
```

## 🚀 Installation & Setup

### 1. Python Environment

```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Database Setup

```bash
# PostgreSQL (recommended)
createdb crypto_trading

# Or use SQLite (for development)
# DATABASE_URL=sqlite:///./crypto_trading.db
```

### 3. Configuration

```bash
# Copy environment file
cp .env.example .env

# Edit .env with your API keys
nano .env
```

### 4. Run the System

```bash
# Start Telegram Bot
python simple_working_bot.py

# Or start FastAPI server
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

## 📊 Dependencies

### Core Dependencies

- **FastAPI** - Modern web framework
- **SQLAlchemy** - ORM and database toolkit
- **Pydantic** - Data validation
- **python-telegram-bot** - Telegram Bot API
- **openai** - AI API client (Mistral compatible)

### Data & Analysis

- **pandas** - Data manipulation
- **numpy** - Numerical computing
- **scipy** - Scientific computing
- **ccxt** - Cryptocurrency exchange API

### Database & Cache

- **psycopg2-binary** - PostgreSQL adapter
- **redis** - Redis client (optional)

### Utilities

- **aiohttp** - Async HTTP client
- **feedparser** - RSS feed parsing
- **python-dotenv** - Environment variables
- **cryptography** - Cryptographic functions

## 🔌 API Endpoints

### System Status
- `GET /` - Root endpoint
- `GET /health` - Health check
- `GET /api/v1/status` - System status

### Market Data
- `GET /api/v1/market/data/{symbol}` - OHLCV data
- `GET /api/v1/market/ticker/{symbol}` - Ticker data
- `GET /api/v1/market/price/{symbol}` - Current price

### Technical Analysis
- `GET /api/v1/technical/analyze/{symbol}` - Technical analysis
- `GET /api/v1/technical/indicators/{symbol}` - Technical indicators
- `GET /api/v1/technical/signals/{symbol}` - Trading signals

### Pattern Detection
- `GET /api/v1/patterns/detect/{symbol}` - Pattern detection
- `GET /api/v1/patterns/support-resistance/{symbol}` - S/R levels

### AI Analysis
- `GET /api/v1/ai/analyze/{symbol}` - AI market analysis
- `GET /api/v1/ai/sentiment/{symbol}` - Sentiment analysis

## 🤖 Telegram Bot Commands

### Basic Commands
- `/start` - Start the bot
- `/help` - Show help message
- `/status` - System status

### Natural Language Interface
Bot accepts natural language commands like:
- "Bitcoin fiyatı nedir?"
- "ETH için teknik analiz yap"
- "DOGE'de pattern var mı?"
- "Portföyümü göster"

## 🔒 Security

### Authentication
- Telegram user ID based authentication
- Authorized users list in environment variables
- Admin users for system management

### API Security
- CORS middleware configured
- Input validation with Pydantic
- SQL injection protection with SQLAlchemy

## 📝 Logging

### Log Levels
- **INFO** - General information
- **WARNING** - Warning messages
- **ERROR** - Error messages
- **DEBUG** - Debug information (development only)

### Log Files
- `logs/telegram_bot.log` - Telegram bot logs
- Console output for development

## 🐳 Docker Deployment

### Build and Run

```bash
# Build image
docker build -t crypto-trading-backend .

# Run with docker-compose
docker-compose up -d
```

### Docker Services
- **postgres** - PostgreSQL database
- **redis** - Redis cache
- **backend** - FastAPI application
- **telegram_bot** - Telegram bot service

## 🧪 Testing

### Manual Testing

```bash
# Test Telegram bot
python simple_working_bot.py

# Test API endpoints
curl http://localhost:8000/health
curl http://localhost:8000/api/v1/market/price/BTC
```

## 🔧 Troubleshooting

### Common Issues

1. **Module Import Errors**
   - Check virtual environment activation
   - Verify requirements.txt installation

2. **Database Connection**
   - Check DATABASE_URL format
   - Verify PostgreSQL/SQLite setup

3. **Telegram Bot Not Responding**
   - Verify TELEGRAM_BOT_TOKEN
   - Check authorized users configuration

4. **AI Responses Not Working**
   - Verify OPENAI_API_KEY (Mistral)
   - Check API base URL configuration

## 📈 Performance

### Optimization Features
- Redis caching for market data
- Async/await for concurrent operations
- Database connection pooling
- Efficient data structures

### Monitoring
- Health check endpoints
- Structured logging
- Error tracking

## 🔄 Updates & Maintenance

### Regular Tasks
- Monitor log files
- Update dependencies
- Backup database
- Check API rate limits

### Version Updates
- Update requirements.txt
- Test compatibility
- Update documentation

---

**Note:** This system is for educational purposes only. Use with funds you can afford to lose.

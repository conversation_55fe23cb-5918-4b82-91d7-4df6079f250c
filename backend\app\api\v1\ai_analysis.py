"""
AI Analysis API Endpoints
Author: inkby<PERSON><PERSON>

Provides endpoints for:
- News aggregation
- Sentiment analysis
- AI-powered market insights
- Comprehensive trading analysis
"""

from fastapi import APIRouter, HTTPException, Query, Depends
from typing import List, Optional
import logging
from datetime import datetime

from app.services.ai_analysis_service import ai_analysis_service
from app.models.ai_analysis import (
    NewsArticleResponse,
    SentimentAnalysisResponse,
    AIMarketInsightResponse,
    AIAnalysisResponse,
    MarketNewsResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/news/{symbol}", response_model=MarketNewsResponse)
async def get_crypto_news(
    symbol: str,
    limit: int = Query(20, ge=1, le=50, description="Number of news articles to fetch")
):
    """
    Get cryptocurrency news for a specific symbol
    
    - **symbol**: Cryptocurrency symbol (e.g., BTC, ETH, BTC/USDT)
    - **limit**: Number of articles to return (1-50)
    """
    try:
        logger.info(f"📰 News request for {symbol}")
        
        # Normalize symbol
        if '/' not in symbol:
            symbol = f"{symbol}/USDT"
        
        # Get news articles
        news_articles = await ai_analysis_service.get_crypto_news(symbol, limit)
        
        # Convert to response format
        articles_response = []
        for article in news_articles:
            articles_response.append(NewsArticleResponse(
                title=article.title,
                content=article.content,
                url=article.url,
                source=article.source.value,
                published_at=article.published_at,
                symbol=article.symbol,
                sentiment_score=article.sentiment_score,
                sentiment_type=article.sentiment_type.value if article.sentiment_type else None,
                relevance_score=article.relevance_score,
                keywords=article.keywords or []
            ))
        
        return MarketNewsResponse(
            symbol=symbol,
            articles=articles_response,
            total_articles=len(articles_response),
            sources_used=list(set(article.source for article in articles_response)),
            fetched_at=datetime.now(),
            status="success"
        )
        
    except Exception as e:
        logger.error(f"❌ Error fetching news for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=f"Error fetching news: {str(e)}")


@router.get("/sentiment/{symbol}", response_model=SentimentAnalysisResponse)
async def analyze_sentiment(
    symbol: str,
    news_limit: int = Query(20, ge=5, le=50, description="Number of news articles to analyze")
):
    """
    Analyze market sentiment for a cryptocurrency
    
    - **symbol**: Cryptocurrency symbol (e.g., BTC, ETH, BTC/USDT)
    - **news_limit**: Number of recent news articles to analyze
    """
    try:
        logger.info(f"📊 Sentiment analysis request for {symbol}")
        
        # Normalize symbol
        if '/' not in symbol:
            symbol = f"{symbol}/USDT"
        
        # Get news articles first
        news_articles = await ai_analysis_service.get_crypto_news(symbol, news_limit)
        
        # Analyze sentiment
        sentiment_result = await ai_analysis_service.analyze_sentiment(symbol, news_articles)
        
        return SentimentAnalysisResponse(
            symbol=sentiment_result.symbol,
            overall_sentiment=sentiment_result.overall_sentiment.value,
            sentiment_score=sentiment_result.sentiment_score,
            confidence=sentiment_result.confidence,
            news_count=sentiment_result.news_count,
            bullish_signals=sentiment_result.bullish_signals,
            bearish_signals=sentiment_result.bearish_signals,
            key_themes=sentiment_result.key_themes,
            analysis_timestamp=sentiment_result.analysis_timestamp,
            status="success"
        )
        
    except Exception as e:
        logger.error(f"❌ Error analyzing sentiment for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=f"Error analyzing sentiment: {str(e)}")


@router.get("/analysis/{symbol}", response_model=AIAnalysisResponse)
async def get_comprehensive_analysis(
    symbol: str,
    timeframe: str = Query("1h", description="Timeframe for analysis"),
    include_technical: bool = Query(True, description="Include technical analysis")
):
    """
    Get comprehensive AI analysis combining news, sentiment, and technical data
    
    - **symbol**: Cryptocurrency symbol (e.g., BTC, ETH, BTC/USDT)
    - **timeframe**: Analysis timeframe (1h, 4h, 1d)
    - **include_technical**: Whether to include technical analysis
    """
    try:
        logger.info(f"🤖 Comprehensive AI analysis request for {symbol}")
        
        # Normalize symbol
        if '/' not in symbol:
            symbol = f"{symbol}/USDT"
        
        # Generate comprehensive analysis
        analysis_result = await ai_analysis_service.generate_comprehensive_analysis(
            symbol, timeframe, include_technical
        )
        
        # Convert market insights to response format
        insights_response = []
        for insight in analysis_result.market_insights:
            insights_response.append(AIMarketInsightResponse(
                symbol=insight.symbol,
                insight_type=insight.insight_type,
                title=insight.title,
                description=insight.description,
                confidence=insight.confidence,
                impact_score=insight.impact_score,
                time_horizon=insight.time_horizon,
                supporting_evidence=insight.supporting_evidence,
                generated_at=insight.generated_at
            ))
        
        return AIAnalysisResponse(
            symbol=analysis_result.symbol,
            timeframe=analysis_result.timeframe,
            sentiment_analysis=SentimentAnalysisResponse(
                symbol=analysis_result.sentiment_analysis.symbol,
                overall_sentiment=analysis_result.sentiment_analysis.overall_sentiment.value,
                sentiment_score=analysis_result.sentiment_analysis.sentiment_score,
                confidence=analysis_result.sentiment_analysis.confidence,
                news_count=analysis_result.sentiment_analysis.news_count,
                bullish_signals=analysis_result.sentiment_analysis.bullish_signals,
                bearish_signals=analysis_result.sentiment_analysis.bearish_signals,
                key_themes=analysis_result.sentiment_analysis.key_themes,
                analysis_timestamp=analysis_result.sentiment_analysis.analysis_timestamp,
                status="success"
            ),
            market_insights=insights_response,
            ai_recommendation=analysis_result.ai_recommendation,
            confidence_score=analysis_result.confidence_score,
            risk_assessment=analysis_result.risk_assessment,
            key_factors=analysis_result.key_factors,
            generated_at=analysis_result.generated_at,
            valid_until=analysis_result.valid_until,
            status="success"
        )
        
    except Exception as e:
        logger.error(f"❌ Error generating comprehensive analysis for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=f"Error generating analysis: {str(e)}")


@router.get("/market-overview")
async def get_market_overview(
    symbols: str = Query("BTC,ETH,BNB,ADA,SOL", description="Comma-separated list of symbols"),
    timeframe: str = Query("1h", description="Analysis timeframe")
):
    """
    Get AI analysis overview for multiple cryptocurrencies
    
    - **symbols**: Comma-separated list of symbols (e.g., "BTC,ETH,BNB")
    - **timeframe**: Analysis timeframe
    """
    try:
        logger.info(f"🌍 Market overview request for: {symbols}")
        
        symbol_list = [s.strip().upper() for s in symbols.split(",")]
        
        # Limit to 10 symbols to prevent overload
        if len(symbol_list) > 10:
            symbol_list = symbol_list[:10]
        
        market_data = []
        
        for symbol in symbol_list:
            try:
                # Normalize symbol
                if '/' not in symbol:
                    symbol = f"{symbol}/USDT"
                
                # Get quick sentiment analysis
                sentiment = await ai_analysis_service.analyze_sentiment(symbol)
                
                market_data.append({
                    "symbol": symbol,
                    "sentiment": sentiment.overall_sentiment.value,
                    "sentiment_score": sentiment.sentiment_score,
                    "confidence": sentiment.confidence,
                    "news_count": sentiment.news_count,
                    "key_themes": sentiment.key_themes[:3]  # Top 3 themes
                })
                
            except Exception as e:
                logger.warning(f"Error analyzing {symbol}: {e}")
                market_data.append({
                    "symbol": symbol,
                    "sentiment": "neutral",
                    "sentiment_score": 0.0,
                    "confidence": 0.0,
                    "news_count": 0,
                    "key_themes": [],
                    "error": str(e)
                })
        
        # Calculate market summary
        valid_analyses = [data for data in market_data if "error" not in data]
        
        if valid_analyses:
            avg_sentiment = sum(data["sentiment_score"] for data in valid_analyses) / len(valid_analyses)
            avg_confidence = sum(data["confidence"] for data in valid_analyses) / len(valid_analyses)
            
            # Determine overall market sentiment
            if avg_sentiment > 0.3:
                market_sentiment = "bullish"
            elif avg_sentiment > -0.3:
                market_sentiment = "neutral"
            else:
                market_sentiment = "bearish"
        else:
            avg_sentiment = 0.0
            avg_confidence = 0.0
            market_sentiment = "neutral"
        
        return {
            "market_sentiment": market_sentiment,
            "average_sentiment_score": avg_sentiment,
            "average_confidence": avg_confidence,
            "symbols_analyzed": len(valid_analyses),
            "total_symbols": len(symbol_list),
            "symbol_data": market_data,
            "timeframe": timeframe,
            "analysis_timestamp": datetime.now(),
            "status": "success"
        }
        
    except Exception as e:
        logger.error(f"❌ Error generating market overview: {e}")
        raise HTTPException(status_code=500, detail=f"Error generating market overview: {str(e)}")


@router.get("/health")
async def health_check():
    """Health check for AI Analysis service"""
    try:
        # Test basic functionality
        test_symbol = "BTC/USDT"
        
        # Quick test of news fetching
        news_test = await ai_analysis_service.get_crypto_news(test_symbol, limit=1)
        
        return {
            "status": "healthy",
            "service": "AI Analysis Service",
            "news_sources_available": len(news_test) > 0,
            "ai_client_available": ai_analysis_service.openai_client is not None,
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"❌ Health check failed: {e}")
        return {
            "status": "unhealthy",
            "service": "AI Analysis Service",
            "error": str(e),
            "timestamp": datetime.now()
        }

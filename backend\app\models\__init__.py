"""
Database models and schemas for AI Crypto Trading System
Author: inkbytefo
"""

# Import the shared Base from database.py
from app.core.database import Base

# Import all models to ensure they are registered with SQLAlchemy
from .market_data import MarketData, TickerData, MarketStats
from .user import User, UserAPIKey, UserSession, UserNotification
from .trading import Trade, TradeSignal, Portfolio, PortfolioHolding
from .system import SystemConfig, Exchange, TradingPair, AIModel, SystemLog, SystemMetrics
from .technical_analysis import TechnicalIndicator, TechnicalSignalDB, PatternDetectionDB, AnalysisSession
from .backtesting import BacktestStrategy, BacktestRun, BacktestTrade

# Import all Pydantic models for API
from .market_data import (
    MarketDataResponse, TickerResponse, MarketStatsResponse,
    MarketDataRequest, TickerRequest
)
from .user import (
    UserCreate, UserUpdate, UserResponse,
    APIKeyCreate, APIKeyResponse,
    NotificationCreate, NotificationResponse
)
from .trading import (
    TradeCreate, TradeResponse,
    SignalCreate, SignalResponse,
    PortfolioResponse, HoldingResponse
)
from .system import (
    SystemConfigCreate, SystemConfigUpdate, SystemConfigResponse,
    ExchangeCreate, ExchangeResponse,
    TradingPairCreate, TradingPairResponse,
    AIModelResponse, SystemLogResponse, SystemMetricsResponse
)
from .technical_analysis import (
    TechnicalIndicatorResponse, TechnicalSignalResponse, PatternDetectionResponse,
    MultiTimeframeAnalysisResponse, MarketOverviewResponse,
    PositionSizeRequest, PositionSizeResponse,
    SignalType, TrendDirection, PatternType
)

# Export all models
__all__ = [
    # SQLAlchemy Models
    "MarketData", "TickerData", "MarketStats",
    "User", "UserAPIKey", "UserSession", "UserNotification",
    "Trade", "TradeSignal", "Portfolio", "PortfolioHolding",
    "SystemConfig", "Exchange", "TradingPair", "AIModel", "SystemLog", "SystemMetrics",

    # Pydantic Models
    "MarketDataResponse", "TickerResponse", "MarketStatsResponse",
    "MarketDataRequest", "TickerRequest",
    "UserCreate", "UserUpdate", "UserResponse",
    "APIKeyCreate", "APIKeyResponse",
    "NotificationCreate", "NotificationResponse",
    "TradeCreate", "TradeResponse",
    "SignalCreate", "SignalResponse",
    "PortfolioResponse", "HoldingResponse",
    "SystemConfigCreate", "SystemConfigUpdate", "SystemConfigResponse",
    "ExchangeCreate", "ExchangeResponse",
    "TradingPairCreate", "TradingPairResponse",
    "AIModelResponse", "SystemLogResponse", "SystemMetricsResponse"
]

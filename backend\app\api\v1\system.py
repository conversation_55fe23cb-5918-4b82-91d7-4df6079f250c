"""
System Management API Endpoints
Author: inkbytefo

Endpoints for:
- System health monitoring
- Graceful shutdown management
- Log monitoring status
- Performance metrics
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from typing import Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel

from app.core.shutdown_manager import shutdown_manager, ShutdownReason
from app.core.log_monitoring import log_monitor
from app.core.auth import get_current_user
from app.models.user import User


router = APIRouter()


class ShutdownRequest(BaseModel):
    """Shutdown request model"""
    reason: str = "user_request"
    details: str = ""
    emergency: bool = False


class SystemHealthResponse(BaseModel):
    """System health response model"""
    health_score: int
    status: str
    recent_alerts: Dict[str, int]
    performance_metrics: Dict[str, Any]
    monitoring_active: bool
    last_check: str
    shutdown_status: Dict[str, Any]


@router.get("/health", response_model=SystemHealthResponse)
async def get_system_health(current_user: User = Depends(get_current_user)):
    """Get comprehensive system health status"""
    try:
        # Get log monitoring health
        health_data = log_monitor.get_system_health()
        
        # Get shutdown status
        shutdown_status = shutdown_manager.get_shutdown_status()
        
        # Combine health data
        return SystemHealthResponse(
            health_score=health_data["health_score"],
            status=health_data["status"],
            recent_alerts=health_data["recent_alerts"],
            performance_metrics=health_data["performance_metrics"],
            monitoring_active=health_data["monitoring_active"],
            last_check=health_data["last_check"],
            shutdown_status=shutdown_status
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting system health: {str(e)}")


@router.get("/alerts")
async def get_recent_alerts(
    limit: int = 50,
    level: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """Get recent system alerts"""
    try:
        alerts = log_monitor.alerts
        
        # Filter by level if specified
        if level:
            alerts = [a for a in alerts if a.level.value == level.lower()]
        
        # Limit results
        alerts = alerts[-limit:] if limit else alerts
        
        # Convert to dict format
        alert_data = []
        for alert in alerts:
            alert_data.append({
                "alert_id": alert.alert_id,
                "type": alert.alert_type.value,
                "level": alert.level.value,
                "title": alert.title,
                "message": alert.message,
                "source": alert.source,
                "timestamp": alert.timestamp.isoformat(),
                "metadata": alert.metadata,
                "resolved": alert.resolved,
                "resolved_at": alert.resolved_at.isoformat() if alert.resolved_at else None
            })
        
        return {
            "alerts": alert_data,
            "total_count": len(alert_data),
            "filtered_by_level": level
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting alerts: {str(e)}")


@router.post("/alerts/{alert_id}/resolve")
async def resolve_alert(
    alert_id: str,
    current_user: User = Depends(get_current_user)
):
    """Mark an alert as resolved"""
    try:
        # Find alert
        alert = None
        for a in log_monitor.alerts:
            if a.alert_id == alert_id:
                alert = a
                break
        
        if not alert:
            raise HTTPException(status_code=404, detail="Alert not found")
        
        if alert.resolved:
            return {"message": "Alert already resolved"}
        
        # Mark as resolved
        alert.resolved = True
        alert.resolved_at = datetime.now()
        
        return {
            "message": "Alert resolved successfully",
            "alert_id": alert_id,
            "resolved_at": alert.resolved_at.isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error resolving alert: {str(e)}")


@router.get("/performance")
async def get_performance_metrics(
    operation: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """Get detailed performance metrics"""
    try:
        performance_data = {}
        
        for op_name, data_queue in log_monitor.performance_data.items():
            if operation and op_name != operation:
                continue
            
            if not data_queue:
                continue
            
            # Calculate metrics
            recent_data = list(data_queue)[-100:]  # Last 100 entries
            
            if recent_data:
                durations = [d["duration_ms"] for d in recent_data]
                memory_usage = [d.get("memory_mb", 0) for d in recent_data]
                error_count = sum(1 for d in recent_data if d["status"] == "error")
                
                performance_data[op_name] = {
                    "avg_duration_ms": sum(durations) / len(durations),
                    "min_duration_ms": min(durations),
                    "max_duration_ms": max(durations),
                    "avg_memory_mb": sum(memory_usage) / len(memory_usage) if any(memory_usage) else 0,
                    "max_memory_mb": max(memory_usage) if memory_usage else 0,
                    "error_rate": error_count / len(recent_data),
                    "total_executions": len(recent_data),
                    "error_count": error_count
                }
        
        return {
            "performance_metrics": performance_data,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting performance metrics: {str(e)}")


@router.post("/shutdown")
async def initiate_shutdown(
    request: ShutdownRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user)
):
    """Initiate graceful system shutdown"""
    try:
        # Validate shutdown reason
        try:
            reason = ShutdownReason(request.reason)
        except ValueError:
            reason = ShutdownReason.USER_REQUEST
        
        # Check if already shutting down
        if shutdown_manager.is_shutting_down:
            return {
                "message": "Shutdown already in progress",
                "status": shutdown_manager.get_shutdown_status()
            }
        
        # Start shutdown in background
        background_tasks.add_task(
            shutdown_manager.initiate_shutdown,
            reason=reason,
            details=request.details,
            emergency=request.emergency
        )
        
        return {
            "message": f"{'Emergency' if request.emergency else 'Graceful'} shutdown initiated",
            "reason": reason.value,
            "details": request.details,
            "initiated_by": current_user.username,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error initiating shutdown: {str(e)}")


@router.get("/shutdown/status")
async def get_shutdown_status(current_user: User = Depends(get_current_user)):
    """Get current shutdown status"""
    try:
        status = shutdown_manager.get_shutdown_status()
        return status
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting shutdown status: {str(e)}")


@router.post("/monitoring/start")
async def start_monitoring(current_user: User = Depends(get_current_user)):
    """Start log monitoring"""
    try:
        if log_monitor.is_monitoring:
            return {"message": "Monitoring already active"}
        
        await log_monitor.start_monitoring()
        
        return {
            "message": "Log monitoring started",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error starting monitoring: {str(e)}")


@router.post("/monitoring/stop")
async def stop_monitoring(current_user: User = Depends(get_current_user)):
    """Stop log monitoring"""
    try:
        if not log_monitor.is_monitoring:
            return {"message": "Monitoring not active"}
        
        await log_monitor.stop_monitoring()
        
        return {
            "message": "Log monitoring stopped",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error stopping monitoring: {str(e)}")


@router.get("/logs/summary")
async def get_log_summary(
    hours: int = 24,
    current_user: User = Depends(get_current_user)
):
    """Get log summary for specified time period"""
    try:
        from datetime import timedelta
        import json
        from pathlib import Path
        
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        # Read recent alerts
        alerts_file = Path("logs/alerts.log")
        recent_alerts = []
        
        if alerts_file.exists():
            with open(alerts_file, "r", encoding="utf-8") as f:
                for line in f:
                    try:
                        alert_data = json.loads(line.strip())
                        alert_time = datetime.fromisoformat(alert_data["timestamp"])
                        if alert_time > cutoff_time:
                            recent_alerts.append(alert_data)
                    except (json.JSONDecodeError, KeyError, ValueError):
                        continue
        
        # Count alerts by level
        alert_counts = {"low": 0, "medium": 0, "high": 0, "critical": 0}
        for alert in recent_alerts:
            level = alert.get("level", "low")
            if level in alert_counts:
                alert_counts[level] += 1
        
        # Get error summary
        error_summary = {}
        for source, errors in log_monitor.error_counts.items():
            recent_errors = [e for e in errors if e["timestamp"] > cutoff_time]
            if recent_errors:
                error_types = {}
                for error in recent_errors:
                    error_type = error.get("error_type", "unknown")
                    error_types[error_type] = error_types.get(error_type, 0) + 1
                
                error_summary[source] = {
                    "total_errors": len(recent_errors),
                    "error_types": error_types
                }
        
        return {
            "time_period_hours": hours,
            "alert_summary": {
                "total_alerts": len(recent_alerts),
                "by_level": alert_counts
            },
            "error_summary": error_summary,
            "monitoring_active": log_monitor.is_monitoring,
            "generated_at": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting log summary: {str(e)}")

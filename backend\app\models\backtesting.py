"""
Backtesting Models for AI Crypto Trading System
Author: inkbytefo

Models for storing and managing backtesting results, strategies, and performance metrics.
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Text, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum as PyEnum
import uuid

from app.core.database import Base


class BacktestStatus(PyEnum):
    """Backtest execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class StrategyType(PyEnum):
    """Trading strategy types"""
    TECHNICAL_ONLY = "technical_only"
    AI_ENHANCED = "ai_enhanced"
    PATTERN_BASED = "pattern_based"
    SENTIMENT_BASED = "sentiment_based"
    HYBRID = "hybrid"
    CUSTOM = "custom"


class BacktestStrategy(Base):
    """Backtesting strategy configuration"""
    __tablename__ = "backtest_strategies"
    
    id = Column(Integer, primary_key=True, index=True)
    strategy_uuid = Column(String(36), unique=True, index=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Strategy Information
    name = Column(String(100), nullable=False)
    description = Column(Text)
    strategy_type = Column(String(20), nullable=False)  # StrategyType enum
    version = Column(String(20), default="1.0")
    
    # Strategy Configuration
    parameters = Column(JSON)  # Strategy-specific parameters
    risk_parameters = Column(JSON)  # Risk management settings
    entry_conditions = Column(JSON)  # Entry signal conditions
    exit_conditions = Column(JSON)  # Exit signal conditions
    
    # Performance Thresholds
    min_confidence = Column(Float, default=0.6)
    max_position_size = Column(Float, default=0.1)  # 10% of portfolio
    stop_loss_percentage = Column(Float, default=0.05)  # 5%
    take_profit_percentage = Column(Float, default=0.15)  # 15%
    
    # Metadata
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    created_by = Column(String(100))
    
    # Relationships
    user = relationship("User")
    backtests = relationship("BacktestRun", back_populates="strategy")


class BacktestRun(Base):
    """Individual backtest execution"""
    __tablename__ = "backtest_runs"
    
    id = Column(Integer, primary_key=True, index=True)
    run_uuid = Column(String(36), unique=True, index=True, default=lambda: str(uuid.uuid4()))
    strategy_id = Column(Integer, ForeignKey("backtest_strategies.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Test Configuration
    name = Column(String(100), nullable=False)
    description = Column(Text)
    symbols = Column(JSON)  # List of symbols tested
    timeframes = Column(JSON)  # List of timeframes
    start_date = Column(DateTime, nullable=False)
    end_date = Column(DateTime, nullable=False)
    initial_capital = Column(Float, nullable=False, default=10000.0)
    
    # Execution Status
    status = Column(String(20), default=BacktestStatus.PENDING.value)
    progress_percentage = Column(Float, default=0.0)
    current_symbol = Column(String(20))
    current_date = Column(DateTime)
    
    # Performance Metrics
    total_trades = Column(Integer, default=0)
    winning_trades = Column(Integer, default=0)
    losing_trades = Column(Integer, default=0)
    final_capital = Column(Float)
    total_return = Column(Float)  # Absolute return
    total_return_percentage = Column(Float)  # Percentage return
    
    # Risk Metrics
    max_drawdown = Column(Float)
    max_drawdown_percentage = Column(Float)
    sharpe_ratio = Column(Float)
    sortino_ratio = Column(Float)
    calmar_ratio = Column(Float)
    volatility = Column(Float)
    
    # Trade Statistics
    win_rate = Column(Float)  # Percentage of winning trades
    avg_win = Column(Float)  # Average winning trade
    avg_loss = Column(Float)  # Average losing trade
    profit_factor = Column(Float)  # Gross profit / Gross loss
    avg_trade_duration = Column(Float)  # Average trade duration in hours
    
    # Execution Details
    execution_time = Column(Float)  # Execution time in seconds
    error_message = Column(Text)
    detailed_results = Column(JSON)  # Detailed trade-by-trade results
    
    # Metadata
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    created_at = Column(DateTime, default=func.now())
    
    # Relationships
    strategy = relationship("BacktestStrategy", back_populates="backtests")
    user = relationship("User")
    trades = relationship("BacktestTrade", back_populates="backtest_run")


class BacktestTrade(Base):
    """Individual trade within a backtest"""
    __tablename__ = "backtest_trades"
    
    id = Column(Integer, primary_key=True, index=True)
    trade_uuid = Column(String(36), unique=True, index=True, default=lambda: str(uuid.uuid4()))
    backtest_run_id = Column(Integer, ForeignKey("backtest_runs.id"), nullable=False)
    
    # Trade Information
    symbol = Column(String(20), nullable=False, index=True)
    timeframe = Column(String(10), nullable=False)
    trade_type = Column(String(10), nullable=False)  # buy, sell
    
    # Entry Details
    entry_date = Column(DateTime, nullable=False)
    entry_price = Column(Float, nullable=False)
    entry_signal_strength = Column(Float)
    entry_confidence = Column(Float)
    entry_reasoning = Column(Text)
    
    # Exit Details
    exit_date = Column(DateTime)
    exit_price = Column(Float)
    exit_reason = Column(String(50))  # stop_loss, take_profit, signal_exit, timeout
    exit_signal_strength = Column(Float)
    
    # Position Details
    quantity = Column(Float, nullable=False)
    position_size_percentage = Column(Float)  # Percentage of portfolio
    
    # Performance
    pnl = Column(Float)  # Profit/Loss in absolute terms
    pnl_percentage = Column(Float)  # Profit/Loss percentage
    fees = Column(Float, default=0.0)
    net_pnl = Column(Float)  # PnL after fees
    
    # Risk Management
    stop_loss_price = Column(Float)
    take_profit_price = Column(Float)
    max_favorable_excursion = Column(Float)  # Best price during trade
    max_adverse_excursion = Column(Float)  # Worst price during trade
    
    # Trade Duration
    duration_hours = Column(Float)
    duration_bars = Column(Integer)  # Number of timeframe bars
    
    # Signal Analysis
    technical_score = Column(Float)
    pattern_score = Column(Float)
    ai_score = Column(Float)
    market_score = Column(Float)
    overall_score = Column(Float)
    
    # Metadata
    is_winning_trade = Column(Boolean)
    created_at = Column(DateTime, default=func.now())
    
    # Relationships
    backtest_run = relationship("BacktestRun", back_populates="trades")


# Pydantic Models for API

class BacktestStrategyCreate(BaseModel):
    """Create backtest strategy request"""
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    strategy_type: str = Field(..., pattern="^(technical_only|ai_enhanced|pattern_based|sentiment_based|hybrid|custom)$")
    parameters: Dict[str, Any] = Field(default_factory=dict)
    risk_parameters: Dict[str, Any] = Field(default_factory=dict)
    entry_conditions: Dict[str, Any] = Field(default_factory=dict)
    exit_conditions: Dict[str, Any] = Field(default_factory=dict)
    min_confidence: float = Field(default=0.6, ge=0.0, le=1.0)
    max_position_size: float = Field(default=0.1, gt=0.0, le=1.0)
    stop_loss_percentage: float = Field(default=0.05, ge=0.0, le=1.0)
    take_profit_percentage: float = Field(default=0.15, ge=0.0, le=5.0)


class BacktestRunCreate(BaseModel):
    """Create backtest run request"""
    strategy_id: int
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    symbols: List[str] = Field(..., min_items=1)
    timeframes: List[str] = Field(default=["1h"])
    start_date: datetime
    end_date: datetime
    initial_capital: float = Field(default=10000.0, gt=0)


class BacktestRunResponse(BaseModel):
    """Backtest run response"""
    id: int
    run_uuid: str
    strategy_id: int
    name: str
    status: str
    progress_percentage: float
    symbols: List[str]
    timeframes: List[str]
    start_date: datetime
    end_date: datetime
    initial_capital: float
    
    # Performance metrics (optional, only when completed)
    total_trades: Optional[int] = None
    winning_trades: Optional[int] = None
    losing_trades: Optional[int] = None
    final_capital: Optional[float] = None
    total_return: Optional[float] = None
    total_return_percentage: Optional[float] = None
    max_drawdown_percentage: Optional[float] = None
    sharpe_ratio: Optional[float] = None
    win_rate: Optional[float] = None
    
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    created_at: datetime
    
    class Config:
        from_attributes = True


class BacktestPerformanceMetrics(BaseModel):
    """Comprehensive backtest performance metrics"""
    # Basic metrics
    total_return: float
    total_return_percentage: float
    final_capital: float
    initial_capital: float
    
    # Trade statistics
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    
    # Risk metrics
    max_drawdown: float
    max_drawdown_percentage: float
    sharpe_ratio: float
    sortino_ratio: float
    calmar_ratio: float
    volatility: float
    
    # Trade performance
    avg_win: float
    avg_loss: float
    profit_factor: float
    avg_trade_duration: float
    
    # Additional metrics
    best_trade: float
    worst_trade: float
    consecutive_wins: int
    consecutive_losses: int
    recovery_factor: float
    
    # Time-based analysis
    monthly_returns: List[float]
    drawdown_periods: List[Dict[str, Any]]
    equity_curve: List[Dict[str, Any]]


# Walk-Forward Analysis Models
class WalkForwardAnalysis(Base):
    """Walk-forward analysis results"""
    __tablename__ = "walk_forward_analyses"

    id = Column(Integer, primary_key=True, index=True)
    analysis_uuid = Column(String(36), unique=True, index=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    strategy_id = Column(Integer, ForeignKey("backtest_strategies.id"), nullable=False)

    # Analysis parameters
    symbol = Column(String(20), nullable=False)
    timeframe = Column(String(10), nullable=False)
    start_date = Column(DateTime, nullable=False)
    end_date = Column(DateTime, nullable=False)
    in_sample_ratio = Column(Float, nullable=False, default=0.7)
    step_size_months = Column(Integer, nullable=False, default=3)
    min_out_sample_months = Column(Integer, nullable=False, default=1)
    optimization_metric = Column(String(50), nullable=False, default="sharpe_ratio")

    # Results
    total_periods = Column(Integer, default=0)
    avg_in_sample_return = Column(Float, default=0.0)
    avg_out_sample_return = Column(Float, default=0.0)
    in_sample_sharpe = Column(Float, default=0.0)
    out_sample_sharpe = Column(Float, default=0.0)

    # Robustness metrics
    consistency_score = Column(Float, default=0.0)
    overfitting_score = Column(Float, default=0.0)
    stability_score = Column(Float, default=0.0)
    is_robust = Column(Boolean, default=False)
    robustness_rating = Column(String(20), default="Unknown")

    # Status and metadata
    status = Column(String(20), default="pending")  # pending, running, completed, failed
    progress_percentage = Column(Float, default=0.0)
    error_message = Column(Text, nullable=True)
    recommendations = Column(JSON, nullable=True)

    # Timestamps
    created_at = Column(DateTime, default=func.now())
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)

    # Relationships
    user = relationship("User", back_populates="walk_forward_analyses")
    strategy = relationship("BacktestStrategy")
    periods = relationship("WalkForwardPeriod", back_populates="analysis", cascade="all, delete-orphan")


class WalkForwardPeriod(Base):
    """Individual period in walk-forward analysis"""
    __tablename__ = "walk_forward_periods"

    id = Column(Integer, primary_key=True, index=True)
    analysis_id = Column(Integer, ForeignKey("walk_forward_analyses.id"), nullable=False)
    period_id = Column(Integer, nullable=False)

    # Period dates
    in_sample_start = Column(DateTime, nullable=False)
    in_sample_end = Column(DateTime, nullable=False)
    out_sample_start = Column(DateTime, nullable=False)
    out_sample_end = Column(DateTime, nullable=False)

    # Optimized parameters for this period
    optimized_parameters = Column(JSON, nullable=True)

    # In-sample results
    in_sample_return = Column(Float, nullable=True)
    in_sample_return_pct = Column(Float, nullable=True)
    in_sample_sharpe = Column(Float, nullable=True)
    in_sample_max_drawdown = Column(Float, nullable=True)
    in_sample_trades = Column(Integer, nullable=True)
    in_sample_win_rate = Column(Float, nullable=True)

    # Out-of-sample results
    out_sample_return = Column(Float, nullable=True)
    out_sample_return_pct = Column(Float, nullable=True)
    out_sample_sharpe = Column(Float, nullable=True)
    out_sample_max_drawdown = Column(Float, nullable=True)
    out_sample_trades = Column(Integer, nullable=True)
    out_sample_win_rate = Column(Float, nullable=True)

    # Performance comparison
    performance_degradation = Column(Float, nullable=True)  # (in_sample - out_sample) / in_sample

    # Status
    status = Column(String(20), default="pending")  # pending, optimizing, testing, completed, failed
    error_message = Column(Text, nullable=True)

    # Timestamps
    created_at = Column(DateTime, default=func.now())
    completed_at = Column(DateTime, nullable=True)

    # Relationships
    analysis = relationship("WalkForwardAnalysis", back_populates="periods")


# Pydantic models for API responses
class WalkForwardAnalysisResponse(BaseModel):
    """Walk-forward analysis response model"""
    id: int
    analysis_uuid: str
    user_id: int
    strategy_id: int
    symbol: str
    timeframe: str
    start_date: datetime
    end_date: datetime
    in_sample_ratio: float
    step_size_months: int
    min_out_sample_months: int
    optimization_metric: str

    # Results
    total_periods: int
    avg_in_sample_return: float
    avg_out_sample_return: float
    in_sample_sharpe: float
    out_sample_sharpe: float

    # Robustness metrics
    consistency_score: float
    overfitting_score: float
    stability_score: float
    is_robust: bool
    robustness_rating: str

    # Status
    status: str
    progress_percentage: float
    error_message: Optional[str] = None
    recommendations: Optional[List[str]] = None

    # Timestamps
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class WalkForwardPeriodResponse(BaseModel):
    """Walk-forward period response model"""
    id: int
    analysis_id: int
    period_id: int

    # Period dates
    in_sample_start: datetime
    in_sample_end: datetime
    out_sample_start: datetime
    out_sample_end: datetime

    # Parameters and results
    optimized_parameters: Optional[Dict[str, Any]] = None

    # In-sample metrics
    in_sample_return: Optional[float] = None
    in_sample_return_pct: Optional[float] = None
    in_sample_sharpe: Optional[float] = None
    in_sample_max_drawdown: Optional[float] = None
    in_sample_trades: Optional[int] = None
    in_sample_win_rate: Optional[float] = None

    # Out-of-sample metrics
    out_sample_return: Optional[float] = None
    out_sample_return_pct: Optional[float] = None
    out_sample_sharpe: Optional[float] = None
    out_sample_max_drawdown: Optional[float] = None
    out_sample_trades: Optional[int] = None
    out_sample_win_rate: Optional[float] = None

    # Performance comparison
    performance_degradation: Optional[float] = None

    # Status
    status: str
    error_message: Optional[str] = None

    # Timestamps
    created_at: datetime
    completed_at: Optional[datetime] = None

    class Config:
        from_attributes = True

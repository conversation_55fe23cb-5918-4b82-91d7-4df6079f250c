"""
Encryption Service for AI Crypto Trading System
Author: inkbytefo

This service handles encryption and decryption of sensitive data like API keys.
Uses Fernet symmetric encryption from the cryptography library.
"""

import os
import base64
import logging
from typing import Optional, Union
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

from app.core.config import settings

logger = logging.getLogger(__name__)


class EncryptionService:
    """Service for encrypting and decrypting sensitive data"""
    
    def __init__(self):
        self._cipher = None
        self._initialize_cipher()
    
    def _initialize_cipher(self):
        """Initialize the Fernet cipher with a key derived from settings"""
        try:
            # Get encryption key from environment or generate one
            encryption_key = getattr(settings, 'ENCRYPTION_KEY', None)
            
            if not encryption_key:
                # Generate a key from SECRET_KEY if ENCRYPTION_KEY is not set
                password = settings.SECRET_KEY.encode()
                salt = b'crypto_trading_salt'  # In production, use a random salt stored securely
                
                kdf = PBKDF2HMAC(
                    algorithm=hashes.SHA256(),
                    length=32,
                    salt=salt,
                    iterations=100000,
                )
                key = base64.urlsafe_b64encode(kdf.derive(password))
                encryption_key = key.decode()
            
            # Create Fernet cipher
            self._cipher = Fernet(encryption_key.encode())
            logger.info("Encryption service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize encryption service: {e}")
            raise RuntimeError(f"Encryption initialization failed: {e}")
    
    def encrypt(self, data: str) -> str:
        """
        Encrypt a string and return base64 encoded encrypted data
        
        Args:
            data: Plain text string to encrypt
            
        Returns:
            Base64 encoded encrypted string
            
        Raises:
            ValueError: If data is None or empty
            RuntimeError: If encryption fails
        """
        if not data:
            raise ValueError("Data to encrypt cannot be None or empty")
        
        if not self._cipher:
            raise RuntimeError("Encryption service not initialized")
        
        try:
            # Convert string to bytes and encrypt
            data_bytes = data.encode('utf-8')
            encrypted_bytes = self._cipher.encrypt(data_bytes)
            
            # Return base64 encoded string for database storage
            return base64.b64encode(encrypted_bytes).decode('utf-8')
            
        except Exception as e:
            logger.error(f"Encryption failed: {e}")
            raise RuntimeError(f"Failed to encrypt data: {e}")
    
    def decrypt(self, encrypted_data: str) -> str:
        """
        Decrypt base64 encoded encrypted data and return plain text
        
        Args:
            encrypted_data: Base64 encoded encrypted string
            
        Returns:
            Decrypted plain text string
            
        Raises:
            ValueError: If encrypted_data is None or empty
            RuntimeError: If decryption fails
        """
        if not encrypted_data:
            raise ValueError("Encrypted data cannot be None or empty")
        
        if not self._cipher:
            raise RuntimeError("Encryption service not initialized")
        
        try:
            # Decode base64 and decrypt
            encrypted_bytes = base64.b64decode(encrypted_data.encode('utf-8'))
            decrypted_bytes = self._cipher.decrypt(encrypted_bytes)
            
            # Return decoded string
            return decrypted_bytes.decode('utf-8')
            
        except Exception as e:
            logger.error(f"Decryption failed: {e}")
            raise RuntimeError(f"Failed to decrypt data: {e}")
    
    def is_encrypted(self, data: str) -> bool:
        """
        Check if a string appears to be encrypted (base64 encoded)
        
        Args:
            data: String to check
            
        Returns:
            True if data appears to be encrypted, False otherwise
        """
        if not data:
            return False
        
        try:
            # Try to decode as base64
            decoded = base64.b64decode(data.encode('utf-8'))
            # If successful and looks like Fernet token, it's probably encrypted
            return len(decoded) > 32  # Fernet tokens are at least 32 bytes
        except Exception:
            return False
    
    def encrypt_if_needed(self, data: str) -> str:
        """
        Encrypt data only if it's not already encrypted
        
        Args:
            data: String to encrypt if needed
            
        Returns:
            Encrypted string (or original if already encrypted)
        """
        if not data:
            return data
        
        if self.is_encrypted(data):
            return data
        
        return self.encrypt(data)
    
    def decrypt_if_needed(self, data: str) -> str:
        """
        Decrypt data only if it appears to be encrypted
        
        Args:
            data: String to decrypt if needed
            
        Returns:
            Decrypted string (or original if not encrypted)
        """
        if not data:
            return data
        
        if not self.is_encrypted(data):
            return data
        
        return self.decrypt(data)


# Global encryption service instance
encryption_service = None

try:
    encryption_service = EncryptionService()
except Exception as e:
    logger.error(f"Failed to initialize global encryption service: {e}")
    encryption_service = None


def get_encryption_service() -> Optional[EncryptionService]:
    """Get the global encryption service instance"""
    return encryption_service


def generate_encryption_key() -> str:
    """Generate a new Fernet encryption key"""
    return Fernet.generate_key().decode()


def validate_encryption_key(key: str) -> bool:
    """Validate if a string is a valid Fernet encryption key"""
    try:
        Fernet(key.encode())
        return True
    except Exception:
        return False

"""
Market Data Models for AI Crypto Trading System
Author: inkbytefo
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Index, Text
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field

from app.core.database import Base


class MarketData(Base):
    """Market data table for storing OHLCV data"""
    __tablename__ = "market_data"
    
    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String(20), nullable=False, index=True)
    exchange = Column(String(20), nullable=False, default="binance")
    timeframe = Column(String(10), nullable=False, index=True)  # 1m, 5m, 1h, 1d
    
    # OHLCV Data
    timestamp = Column(DateTime, nullable=False, index=True)
    open_price = Column(Float, nullable=False)
    high_price = Column(Float, nullable=False)
    low_price = Column(Float, nullable=False)
    close_price = Column(Float, nullable=False)
    volume = Column(Float, nullable=False)
    
    # Additional metrics
    quote_volume = Column(Float)
    trades_count = Column(Integer)
    
    # Metadata
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Composite indexes for efficient queries
    __table_args__ = (
        Index('idx_symbol_timeframe_timestamp', 'symbol', 'timeframe', 'timestamp'),
        Index('idx_exchange_symbol_timestamp', 'exchange', 'symbol', 'timestamp'),
    )


class TickerData(Base):
    """Real-time ticker data"""
    __tablename__ = "ticker_data"
    
    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String(20), nullable=False, index=True)
    exchange = Column(String(20), nullable=False, default="binance")
    
    # Price data
    last_price = Column(Float, nullable=False)
    bid_price = Column(Float)
    ask_price = Column(Float)
    
    # 24h statistics
    price_change_24h = Column(Float)
    price_change_percent_24h = Column(Float)
    high_24h = Column(Float)
    low_24h = Column(Float)
    volume_24h = Column(Float)
    quote_volume_24h = Column(Float)
    
    # Metadata
    timestamp = Column(DateTime, nullable=False, index=True)
    created_at = Column(DateTime, default=func.now())
    
    __table_args__ = (
        Index('idx_symbol_exchange_timestamp', 'symbol', 'exchange', 'timestamp'),
    )


class MarketStats(Base):
    """Market statistics and metrics"""
    __tablename__ = "market_stats"
    
    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String(20), nullable=False, index=True)
    exchange = Column(String(20), nullable=False, default="binance")
    
    # Market cap and supply data
    market_cap = Column(Float)
    circulating_supply = Column(Float)
    total_supply = Column(Float)
    max_supply = Column(Float)
    
    # Rankings and metrics
    market_cap_rank = Column(Integer)
    volume_rank = Column(Integer)
    
    # Additional data
    ath_price = Column(Float)  # All-time high
    ath_date = Column(DateTime)
    atl_price = Column(Float)  # All-time low
    atl_date = Column(DateTime)
    
    # Metadata
    timestamp = Column(DateTime, nullable=False, index=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())


# Pydantic models for API responses
class MarketDataResponse(BaseModel):
    """Market data API response model"""
    symbol: str
    exchange: str
    timeframe: str
    timestamp: datetime
    open_price: float
    high_price: float
    low_price: float
    close_price: float
    volume: float
    quote_volume: Optional[float] = None
    trades_count: Optional[int] = None
    
    class Config:
        from_attributes = True


class TickerResponse(BaseModel):
    """Ticker data API response model"""
    symbol: str
    exchange: str
    last_price: float
    bid_price: Optional[float] = None
    ask_price: Optional[float] = None
    price_change_24h: Optional[float] = None
    price_change_percent_24h: Optional[float] = None
    high_24h: Optional[float] = None
    low_24h: Optional[float] = None
    volume_24h: Optional[float] = None
    quote_volume_24h: Optional[float] = None
    timestamp: datetime
    
    class Config:
        from_attributes = True


class MarketStatsResponse(BaseModel):
    """Market statistics API response model"""
    symbol: str
    exchange: str
    market_cap: Optional[float] = None
    circulating_supply: Optional[float] = None
    total_supply: Optional[float] = None
    max_supply: Optional[float] = None
    market_cap_rank: Optional[int] = None
    volume_rank: Optional[int] = None
    ath_price: Optional[float] = None
    ath_date: Optional[datetime] = None
    atl_price: Optional[float] = None
    atl_date: Optional[datetime] = None
    timestamp: datetime
    
    class Config:
        from_attributes = True


class MarketDataRequest(BaseModel):
    """Market data request model"""
    symbol: str = Field(..., description="Trading symbol (e.g., BTC/USDT)")
    timeframe: str = Field(default="1h", description="Timeframe (1m, 5m, 15m, 1h, 4h, 1d)")
    limit: int = Field(default=100, ge=1, le=1000, description="Number of data points")
    start_time: Optional[datetime] = Field(None, description="Start time for historical data")
    end_time: Optional[datetime] = Field(None, description="End time for historical data")


class TickerRequest(BaseModel):
    """Ticker data request model"""
    symbol: Optional[str] = Field(None, description="Specific trading symbol (e.g., BTC/USDT)")
    symbols: Optional[List[str]] = Field(None, description="List of trading symbols")
    exchange: str = Field(default="binance", description="Exchange name")

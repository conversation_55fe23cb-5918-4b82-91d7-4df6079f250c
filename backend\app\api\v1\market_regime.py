"""
Market Regime Detection API Endpoints
Author: inkbytefo
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from typing import Dict, Any, Optional, List
from datetime import datetime
import logging

from app.services.market_regime_detector import market_regime_detector, MarketRegime, RegimeSignal
from app.core.auth import get_current_user
from app.models.user import User
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)

router = APIRouter()

# Pydantic Models
class RegimeDetectionRequest(BaseModel):
    """Request model for regime detection"""
    symbol: str = Field(..., description="Trading symbol (e.g., 'BTC/USDT')")
    timeframe: str = Field(default="1d", description="Data timeframe for regime detection")
    exchange: str = Field(default="binance", description="Exchange name")

class RegimeSignalResponse(BaseModel):
    """Response model for regime detection"""
    symbol: str
    regime: str
    confidence: float
    strength: float
    duration_days: int
    trend_direction: str
    volatility_level: str
    momentum_strength: float
    
    # Technical indicators
    ma_slope: float
    price_vs_ma: float
    adx_value: float
    volatility_percentile: float
    
    # Recommendations
    recommended_adjustments: Dict[str, float]
    reasoning: str
    
    # Metadata
    detected_at: datetime
    timeframe: str
    exchange: str

class MultiSymbolRegimeRequest(BaseModel):
    """Request model for multi-symbol regime detection"""
    symbols: List[str] = Field(..., description="List of trading symbols")
    timeframe: str = Field(default="1d", description="Data timeframe for regime detection")
    exchange: str = Field(default="binance", description="Exchange name")

class MultiSymbolRegimeResponse(BaseModel):
    """Response model for multi-symbol regime detection"""
    results: Dict[str, RegimeSignalResponse]
    summary: Dict[str, Any]
    detected_at: datetime

@router.post("/detect", response_model=RegimeSignalResponse)
async def detect_market_regime(
    request: RegimeDetectionRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Detect market regime for a specific symbol
    
    This endpoint analyzes market conditions and returns:
    - Current market regime (bull/bear/sideways/volatile)
    - Confidence level and strength metrics
    - Technical indicators supporting the analysis
    - Recommended trading adjustments
    """
    try:
        logger.info(f"User {current_user.username} requesting regime detection for {request.symbol}")
        
        # Detect market regime
        regime_signal = await market_regime_detector.detect_regime(
            symbol=request.symbol,
            timeframe=request.timeframe,
            exchange=request.exchange
        )
        
        # Convert to response model
        response = RegimeSignalResponse(
            symbol=request.symbol,
            regime=regime_signal.regime.value,
            confidence=regime_signal.confidence,
            strength=regime_signal.strength,
            duration_days=regime_signal.duration_days,
            trend_direction=regime_signal.trend_direction,
            volatility_level=regime_signal.volatility_level,
            momentum_strength=regime_signal.momentum_strength,
            ma_slope=regime_signal.ma_slope,
            price_vs_ma=regime_signal.price_vs_ma,
            adx_value=regime_signal.adx_value,
            volatility_percentile=regime_signal.volatility_percentile,
            recommended_adjustments=regime_signal.recommended_adjustments,
            reasoning=regime_signal.reasoning,
            detected_at=datetime.utcnow(),
            timeframe=request.timeframe,
            exchange=request.exchange
        )
        
        logger.info(f"Regime detection completed for {request.symbol}: {regime_signal.regime.value} "
                   f"(confidence: {regime_signal.confidence:.2f})")
        
        return response
        
    except Exception as e:
        logger.error(f"Error detecting market regime for {request.symbol}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to detect market regime: {str(e)}"
        )

@router.post("/detect-multiple", response_model=MultiSymbolRegimeResponse)
async def detect_multiple_regimes(
    request: MultiSymbolRegimeRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Detect market regimes for multiple symbols simultaneously
    
    This endpoint analyzes multiple symbols and provides:
    - Individual regime analysis for each symbol
    - Market summary across all symbols
    - Overall market sentiment indicators
    """
    try:
        logger.info(f"User {current_user.username} requesting regime detection for {len(request.symbols)} symbols")
        
        results = {}
        regime_counts = {"bull": 0, "bear": 0, "sideways": 0, "volatile": 0, "unknown": 0}
        total_confidence = 0.0
        valid_detections = 0
        
        # Detect regime for each symbol
        for symbol in request.symbols:
            try:
                regime_signal = await market_regime_detector.detect_regime(
                    symbol=symbol,
                    timeframe=request.timeframe,
                    exchange=request.exchange
                )
                
                # Convert to response model
                regime_response = RegimeSignalResponse(
                    symbol=symbol,
                    regime=regime_signal.regime.value,
                    confidence=regime_signal.confidence,
                    strength=regime_signal.strength,
                    duration_days=regime_signal.duration_days,
                    trend_direction=regime_signal.trend_direction,
                    volatility_level=regime_signal.volatility_level,
                    momentum_strength=regime_signal.momentum_strength,
                    ma_slope=regime_signal.ma_slope,
                    price_vs_ma=regime_signal.price_vs_ma,
                    adx_value=regime_signal.adx_value,
                    volatility_percentile=regime_signal.volatility_percentile,
                    recommended_adjustments=regime_signal.recommended_adjustments,
                    reasoning=regime_signal.reasoning,
                    detected_at=datetime.utcnow(),
                    timeframe=request.timeframe,
                    exchange=request.exchange
                )
                
                results[symbol] = regime_response
                
                # Update statistics
                regime_counts[regime_signal.regime.value] += 1
                if regime_signal.confidence > 0:
                    total_confidence += regime_signal.confidence
                    valid_detections += 1
                    
            except Exception as e:
                logger.warning(f"Failed to detect regime for {symbol}: {e}")
                # Continue with other symbols
                continue
        
        # Calculate summary statistics
        avg_confidence = total_confidence / valid_detections if valid_detections > 0 else 0.0
        dominant_regime = max(regime_counts, key=regime_counts.get)
        
        # Determine overall market sentiment
        bull_bear_ratio = regime_counts["bull"] / max(regime_counts["bear"], 1)
        if bull_bear_ratio > 2:
            market_sentiment = "bullish"
        elif bull_bear_ratio < 0.5:
            market_sentiment = "bearish"
        else:
            market_sentiment = "mixed"
        
        summary = {
            "total_symbols": len(request.symbols),
            "successful_detections": valid_detections,
            "regime_distribution": regime_counts,
            "dominant_regime": dominant_regime,
            "average_confidence": round(avg_confidence, 3),
            "market_sentiment": market_sentiment,
            "bull_bear_ratio": round(bull_bear_ratio, 2)
        }
        
        response = MultiSymbolRegimeResponse(
            results=results,
            summary=summary,
            detected_at=datetime.utcnow()
        )
        
        logger.info(f"Multi-symbol regime detection completed: {valid_detections}/{len(request.symbols)} successful, "
                   f"dominant regime: {dominant_regime}, sentiment: {market_sentiment}")
        
        return response
        
    except Exception as e:
        logger.error(f"Error in multi-symbol regime detection: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to detect regimes for multiple symbols: {str(e)}"
        )

@router.get("/regimes", response_model=Dict[str, str])
async def get_available_regimes():
    """
    Get list of available market regime types
    
    Returns all possible market regime classifications
    """
    return {
        "regimes": {
            "bull": "Strong uptrend - favorable for long positions",
            "bear": "Strong downtrend - favorable for short positions", 
            "sideways": "Range-bound market - favor mean reversion strategies",
            "volatile": "High volatility - reduce position sizes and use tight stops",
            "unknown": "Insufficient data or unclear conditions"
        }
    }

@router.get("/adjustments/explanation")
async def get_adjustment_explanations():
    """
    Get explanation of regime-based trading adjustments
    
    Returns detailed explanation of how each adjustment affects trading strategy
    """
    return {
        "adjustments": {
            "signal_weight_multiplier": {
                "description": "Multiplier for overall signal strength",
                "range": "0.1 to 1.5",
                "effect": "Higher values increase signal sensitivity"
            },
            "long_bias": {
                "description": "Bias towards long or short positions",
                "range": "-1.0 to 1.0",
                "effect": "Positive values favor longs, negative favor shorts"
            },
            "position_size_multiplier": {
                "description": "Multiplier for position sizing",
                "range": "0.3 to 1.5",
                "effect": "Higher values increase position sizes"
            },
            "stop_loss_multiplier": {
                "description": "Multiplier for stop loss distances",
                "range": "0.5 to 1.5",
                "effect": "Lower values create tighter stops"
            },
            "take_profit_multiplier": {
                "description": "Multiplier for take profit targets",
                "range": "0.7 to 1.5",
                "effect": "Higher values set more aggressive profit targets"
            }
        },
        "regime_strategies": {
            "bull": "Favor long positions, reduce stops, higher targets",
            "bear": "Favor short positions, tight stops, lower targets",
            "sideways": "Reduce position sizes, favor mean reversion",
            "volatile": "Minimize exposure, very tight risk management"
        }
    }

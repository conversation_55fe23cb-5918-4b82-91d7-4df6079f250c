"""
Test script for Market Regime Detection
Author: inkbytefo
"""

import asyncio
import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.market_regime_detector import market_regime_detector, MarketRegime
from app.services.signal_generation_service import signal_generation_service

async def test_market_regime_detection():
    """Test market regime detection functionality"""
    
    print("🔍 Testing Market Regime Detection System")
    print("=" * 50)
    
    # Test symbols
    test_symbols = ["BTC/USDT", "ETH/USDT", "ADA/USDT"]
    
    for symbol in test_symbols:
        print(f"\n📊 Analyzing {symbol}...")
        
        try:
            # Detect market regime
            regime_signal = await market_regime_detector.detect_regime(
                symbol=symbol,
                timeframe="1d",
                exchange="binance"
            )
            
            print(f"✅ Regime Detection Results for {symbol}:")
            print(f"   🎯 Regime: {regime_signal.regime.value.upper()}")
            print(f"   📈 Confidence: {regime_signal.confidence:.2f}")
            print(f"   💪 Strength: {regime_signal.strength:.2f}")
            print(f"   📅 Duration: {regime_signal.duration_days} days")
            print(f"   📊 Trend: {regime_signal.trend_direction}")
            print(f"   🌊 Volatility: {regime_signal.volatility_level}")
            print(f"   ⚡ Momentum: {regime_signal.momentum_strength:.2f}")
            
            print(f"\n   📋 Technical Indicators:")
            print(f"   • MA Slope: {regime_signal.ma_slope:.3f}%")
            print(f"   • Price vs MA: {regime_signal.price_vs_ma:.1f}%")
            print(f"   • ADX: {regime_signal.adx_value:.1f}")
            print(f"   • Volatility Percentile: {regime_signal.volatility_percentile:.1f}%")
            
            print(f"\n   🎛️ Recommended Adjustments:")
            for key, value in regime_signal.recommended_adjustments.items():
                print(f"   • {key}: {value:.2f}")
            
            print(f"\n   💭 Reasoning: {regime_signal.reasoning}")
            
        except Exception as e:
            print(f"❌ Error analyzing {symbol}: {e}")
    
    print("\n" + "=" * 50)
    print("🧪 Testing Signal Generation with Regime Integration")
    print("=" * 50)
    
    # Test signal generation with regime detection
    test_symbol = "BTC/USDT"
    print(f"\n🎯 Generating signal for {test_symbol} with regime detection...")
    
    try:
        # Generate signal with regime detection enabled
        signal = await signal_generation_service.generate_signal(
            symbol=test_symbol,
            timeframe="1h",
            exchange="binance",
            portfolio_size=10000.0,
            parameters={
                "enable_regime_detection": True,
                "regime_confidence_threshold": 0.5,
                "regime_adjustment_strength": 1.0
            }
        )
        
        if signal:
            print(f"✅ Signal Generated Successfully:")
            print(f"   🎯 Signal: {signal.signal_type.value.upper()}")
            print(f"   💪 Strength: {signal.signal_strength.value}")
            print(f"   📈 Confidence: {signal.confidence:.2f}")
            print(f"   💰 Entry Price: ${signal.entry_point.price:.2f}")
            print(f"   📊 Position Size: {signal.position_sizing.position_size_percentage:.1f}%")
            print(f"   💵 Position Value: ${signal.position_sizing.position_size_usd:.2f}")
            print(f"   🛡️ Risk Level: {signal.position_sizing.risk_level.value}")
            
            print(f"\n   🎯 Exit Points:")
            for i, exit_point in enumerate(signal.exit_points, 1):
                print(f"   • TP{i}: ${exit_point.price:.2f} (R/R: {exit_point.risk_reward_ratio:.1f})")
            
            print(f"\n   💭 Reasoning: {signal.reasoning}")
            
        else:
            print("❌ No signal generated")
            
    except Exception as e:
        print(f"❌ Error generating signal: {e}")
    
    print("\n" + "=" * 50)
    print("✅ Market Regime Detection Test Completed!")

if __name__ == "__main__":
    asyncio.run(test_market_regime_detection())

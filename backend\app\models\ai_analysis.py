"""
AI Analysis Database Models and Response Schemas
Author: inkbytefo

Database models for:
- News articles
- Sentiment analysis results
- AI market insights
- Analysis results

Response schemas for API endpoints
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, Text, Boolean, JSON
from sqlalchemy.sql import func
from pydantic import BaseModel, <PERSON>
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

from app.core.database import Base


# Database Models
class NewsArticleDB(Base):
    """News article database model"""
    __tablename__ = "news_articles"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(500), nullable=False)
    content = Column(Text, nullable=False)
    url = Column(String(1000), nullable=False)
    source = Column(String(50), nullable=False)
    published_at = Column(DateTime, nullable=False)
    symbol = Column(String(20), nullable=True, index=True)
    sentiment_score = Column(Float, nullable=True)
    sentiment_type = Column(String(20), nullable=True)
    relevance_score = Column(Float, nullable=True)
    keywords = Column(JSON, nullable=True)
    
    # Metadata
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())


class SentimentAnalysisDB(Base):
    """Sentiment analysis database model"""
    __tablename__ = "sentiment_analyses"
    
    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String(20), nullable=False, index=True)
    overall_sentiment = Column(String(20), nullable=False)
    sentiment_score = Column(Float, nullable=False)
    confidence = Column(Float, nullable=False)
    news_count = Column(Integer, nullable=False)
    bullish_signals = Column(JSON, nullable=True)
    bearish_signals = Column(JSON, nullable=True)
    key_themes = Column(JSON, nullable=True)
    analysis_timestamp = Column(DateTime, nullable=False)
    
    # Metadata
    created_at = Column(DateTime, default=func.now())


class AIMarketInsightDB(Base):
    """AI market insight database model"""
    __tablename__ = "ai_market_insights"
    
    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String(20), nullable=False, index=True)
    insight_type = Column(String(50), nullable=False)
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=False)
    confidence = Column(Float, nullable=False)
    impact_score = Column(Float, nullable=False)
    time_horizon = Column(String(20), nullable=False)
    supporting_evidence = Column(JSON, nullable=True)
    generated_at = Column(DateTime, nullable=False)
    
    # Metadata
    created_at = Column(DateTime, default=func.now())


class AIAnalysisResultDB(Base):
    """AI analysis result database model"""
    __tablename__ = "ai_analysis_results"
    
    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String(20), nullable=False, index=True)
    timeframe = Column(String(10), nullable=False)
    sentiment_analysis_id = Column(Integer, nullable=True)
    ai_recommendation = Column(Text, nullable=False)
    confidence_score = Column(Float, nullable=False)
    risk_assessment = Column(Text, nullable=False)
    key_factors = Column(JSON, nullable=True)
    generated_at = Column(DateTime, nullable=False)
    valid_until = Column(DateTime, nullable=False)
    
    # Metadata
    created_at = Column(DateTime, default=func.now())


# Response Models
class NewsArticleResponse(BaseModel):
    """News article response model"""
    title: str
    content: str
    url: str
    source: str
    published_at: datetime
    symbol: Optional[str] = None
    sentiment_score: Optional[float] = None
    sentiment_type: Optional[str] = None
    relevance_score: Optional[float] = None
    keywords: List[str] = []

    class Config:
        from_attributes = True


class MarketNewsResponse(BaseModel):
    """Market news response model"""
    symbol: str
    articles: List[NewsArticleResponse]
    total_articles: int
    sources_used: List[str]
    fetched_at: datetime
    status: str = "success"

    class Config:
        from_attributes = True


class SentimentAnalysisResponse(BaseModel):
    """Sentiment analysis response model"""
    symbol: str
    overall_sentiment: str
    sentiment_score: float = Field(..., ge=-1.0, le=1.0)
    confidence: float = Field(..., ge=0.0, le=1.0)
    news_count: int
    bullish_signals: List[str]
    bearish_signals: List[str]
    key_themes: List[str]
    analysis_timestamp: datetime
    status: str = "success"

    class Config:
        from_attributes = True


class AIMarketInsightResponse(BaseModel):
    """AI market insight response model"""
    symbol: str
    insight_type: str
    title: str
    description: str
    confidence: float = Field(..., ge=0.0, le=1.0)
    impact_score: float = Field(..., ge=-1.0, le=1.0)
    time_horizon: str
    supporting_evidence: List[str]
    generated_at: datetime

    class Config:
        from_attributes = True


class AIAnalysisResponse(BaseModel):
    """Comprehensive AI analysis response model"""
    symbol: str
    timeframe: str
    sentiment_analysis: SentimentAnalysisResponse
    market_insights: List[AIMarketInsightResponse]
    ai_recommendation: str
    confidence_score: float = Field(..., ge=0.0, le=1.0)
    risk_assessment: str
    key_factors: List[str]
    generated_at: datetime
    valid_until: datetime
    status: str = "success"

    class Config:
        from_attributes = True


class MarketOverviewResponse(BaseModel):
    """Market overview response model"""
    market_sentiment: str
    average_sentiment_score: float
    average_confidence: float
    symbols_analyzed: int
    total_symbols: int
    symbol_data: List[Dict[str, Any]]
    timeframe: str
    analysis_timestamp: datetime
    status: str = "success"

    class Config:
        from_attributes = True


class AIServiceHealthResponse(BaseModel):
    """AI service health response model"""
    status: str
    service: str
    news_sources_available: bool
    ai_client_available: bool
    timestamp: datetime
    error: Optional[str] = None

    class Config:
        from_attributes = True


# Enums for validation
class SentimentTypeEnum(str, Enum):
    """Sentiment type enumeration"""
    VERY_BEARISH = "very_bearish"
    BEARISH = "bearish"
    NEUTRAL = "neutral"
    BULLISH = "bullish"
    VERY_BULLISH = "very_bullish"


class NewsSourceEnum(str, Enum):
    """News source enumeration"""
    COINGECKO = "coingecko"
    NEWSAPI = "newsapi"
    RSS_FEED = "rss_feed"
    REDDIT = "reddit"
    MANUAL = "manual"


class InsightTypeEnum(str, Enum):
    """Market insight type enumeration"""
    TECHNICAL = "technical"
    FUNDAMENTAL = "fundamental"
    SENTIMENT = "sentiment"
    NEWS = "news"
    SOCIAL = "social"
    REGULATORY = "regulatory"


class TimeHorizonEnum(str, Enum):
    """Time horizon enumeration"""
    SHORT = "short"      # Minutes to hours
    MEDIUM = "medium"    # Hours to days
    LONG = "long"        # Days to weeks


# Request Models
class NewsRequest(BaseModel):
    """News request model"""
    symbol: str
    limit: int = Field(20, ge=1, le=50)

    class Config:
        from_attributes = True


class SentimentRequest(BaseModel):
    """Sentiment analysis request model"""
    symbol: str
    news_limit: int = Field(20, ge=5, le=50)

    class Config:
        from_attributes = True


class AIAnalysisRequest(BaseModel):
    """AI analysis request model"""
    symbol: str
    timeframe: str = "1h"
    include_technical: bool = True

    class Config:
        from_attributes = True


class MarketOverviewRequest(BaseModel):
    """Market overview request model"""
    symbols: str = "BTC,ETH,BNB,ADA,SOL"
    timeframe: str = "1h"

    class Config:
        from_attributes = True

"""
Logging configuration for AI Crypto Trading System
Author: inkbytefo
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from app.core.config import settings, ensure_log_directory


def setup_logging():
    """Setup application logging"""
    
    # Ensure log directory exists
    log_dir = ensure_log_directory()
    log_file = log_dir / "trading.log"
    
    # Create formatter
    formatter = logging.Formatter(
        fmt="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    
    # Root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.LOG_LEVEL.upper()))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # File handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        log_file,
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5
    )
    file_handler.setFormatter(formatter)
    root_logger.addHandler(file_handler)
    
    # Specific loggers
    trading_logger = logging.getLogger("trading")
    trading_logger.setLevel(logging.INFO)
    
    ai_logger = logging.getLogger("ai_analysis")
    ai_logger.setLevel(logging.INFO)
    
    telegram_logger = logging.getLogger("telegram")
    telegram_logger.setLevel(logging.INFO)
    
    # Suppress noisy third-party loggers
    logging.getLogger("ccxt").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    
    logging.info("Logging system initialized")


def get_logger(name: str) -> logging.Logger:
    """Get a logger instance"""
    return logging.getLogger(name)

"""
Database configuration and connection management
Author: inkbytefo
"""

from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
import redis
from typing import Generator, List, Dict, Optional, Any
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)

# SQLAlchemy setup
engine = create_engine(
    settings.DATABASE_URL,
    pool_pre_ping=True,
    pool_recycle=300,
    echo=settings.DEBUG
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# Redis setup
redis_client = None
try:
    redis_client = redis.from_url(settings.REDIS_URL, decode_responses=True)
    redis_client.ping()
    logger.info("Redis connection established")
except Exception as e:
    logger.error(f"Redis connection failed: {e}")
    redis_client = None


def get_db() -> Generator[Session, None, None]:
    """Get database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_redis():
    """Get Redis client"""
    return redis_client


async def init_database():
    """Initialize database tables"""
    try:
        # Import all models to ensure they are registered
        from app.models.market_data import Base as MarketDataBase
        from app.models.user import Base as UserBase
        from app.models.trading import Base as TradingBase
        from app.models.system import Base as SystemBase

        # Create all tables
        MarketDataBase.metadata.create_all(bind=engine)
        UserBase.metadata.create_all(bind=engine)
        TradingBase.metadata.create_all(bind=engine)
        SystemBase.metadata.create_all(bind=engine)

        logger.info("Database tables created successfully")

        # Initialize default data
        await _initialize_default_data()

        return True
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        return False


async def _initialize_default_data():
    """Initialize default system data"""
    try:
        db = SessionLocal()

        # Import models
        from app.models.system import SystemConfig, Exchange, TradingPair

        # Default system configurations
        default_configs = [
            {
                "config_key": "TRADING_ENABLED",
                "config_value": "false",
                "config_type": "bool",
                "category": "trading",
                "description": "Enable/disable trading functionality"
            },
            {
                "config_key": "MAX_DAILY_TRADES",
                "config_value": "10",
                "config_type": "int",
                "category": "trading",
                "description": "Maximum number of trades per day"
            },
            {
                "config_key": "DEFAULT_RISK_LEVEL",
                "config_value": "low",
                "config_type": "string",
                "category": "risk",
                "description": "Default risk level for new users"
            },
            {
                "config_key": "AI_CONFIDENCE_THRESHOLD",
                "config_value": "0.7",
                "config_type": "float",
                "category": "ai",
                "description": "Minimum AI confidence for trade execution"
            }
        ]

        for config_data in default_configs:
            existing = db.query(SystemConfig).filter(
                SystemConfig.config_key == config_data["config_key"]
            ).first()

            if not existing:
                config = SystemConfig(**config_data)
                db.add(config)

        # Default exchanges
        default_exchanges = [
            {
                "name": "binance",
                "display_name": "Binance",
                "api_url": "https://api.binance.com",
                "testnet_url": "https://testnet.binance.vision",
                "is_active": True,
                "supports_websocket": True,
                "trading_fee": 0.001
            },
            {
                "name": "coingecko",
                "display_name": "CoinGecko",
                "api_url": "https://api.coingecko.com/api/v3",
                "is_active": True,
                "is_trading_enabled": False,
                "supports_websocket": False,
                "trading_fee": 0.0
            }
        ]

        for exchange_data in default_exchanges:
            existing = db.query(Exchange).filter(
                Exchange.name == exchange_data["name"]
            ).first()

            if not existing:
                exchange = Exchange(**exchange_data)
                db.add(exchange)

        # Default trading pairs
        default_pairs = [
            {"symbol": "BTC/USDT", "base_asset": "BTC", "quote_asset": "USDT", "is_active": True, "is_trading_enabled": True},
            {"symbol": "ETH/USDT", "base_asset": "ETH", "quote_asset": "USDT", "is_active": True, "is_trading_enabled": True},
            {"symbol": "BNB/USDT", "base_asset": "BNB", "quote_asset": "USDT", "is_active": True, "is_trading_enabled": True},
            {"symbol": "ADA/USDT", "base_asset": "ADA", "quote_asset": "USDT", "is_active": True, "is_trading_enabled": True},
            {"symbol": "SOL/USDT", "base_asset": "SOL", "quote_asset": "USDT", "is_active": True, "is_trading_enabled": True}
        ]

        for pair_data in default_pairs:
            existing = db.query(TradingPair).filter(
                TradingPair.symbol == pair_data["symbol"]
            ).first()

            if not existing:
                pair = TradingPair(**pair_data)
                db.add(pair)

        db.commit()
        db.close()

        logger.info("Default system data initialized")

    except Exception as e:
        logger.error(f"Default data initialization failed: {e}")
        if 'db' in locals():
            db.rollback()
            db.close()


async def check_database_connection():
    """Check database connectivity"""
    try:
        db = SessionLocal()
        db.execute("SELECT 1")
        db.close()
        logger.info("Database connection healthy")
        return True
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        return False


async def check_redis_connection():
    """Check Redis connectivity"""
    try:
        if redis_client:
            redis_client.ping()
            logger.info("Redis connection healthy")
            return True
        else:
            logger.warning("Redis client not initialized")
            return False
    except Exception as e:
        logger.error(f"Redis connection failed: {e}")
        return False


class CacheManager:
    """Advanced Redis cache management with intelligent strategies"""

    def __init__(self):
        self.redis = redis_client
        self.default_ttl = 300  # 5 minutes
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0
        }

        # Cache tiers with different TTLs
        self.cache_tiers = {
            'hot': 60,      # 1 minute - frequently accessed data
            'warm': 300,    # 5 minutes - moderately accessed data
            'cold': 1800,   # 30 minutes - rarely accessed data
            'frozen': 7200  # 2 hours - historical data
        }

    async def get(self, key: str, track_stats: bool = True):
        """Get value from cache with statistics tracking"""
        if not self.redis:
            return None
        try:
            value = self.redis.get(key)
            if track_stats:
                if value:
                    self.cache_stats['hits'] += 1
                else:
                    self.cache_stats['misses'] += 1
            return value
        except Exception as e:
            logger.error(f"Cache get error: {e}")
            if track_stats:
                self.cache_stats['misses'] += 1
            return None

    async def set(self, key: str, value: str, ttl: int = None, tier: str = None):
        """Set value in cache with tier-based TTL"""
        if not self.redis:
            return False
        try:
            if tier and tier in self.cache_tiers:
                ttl = self.cache_tiers[tier]
            else:
                ttl = ttl or self.default_ttl

            result = self.redis.setex(key, ttl, value)
            self.cache_stats['sets'] += 1
            return result
        except Exception as e:
            logger.error(f"Cache set error: {e}")
            return False

    async def delete(self, key: str):
        """Delete key from cache"""
        if not self.redis:
            return False
        try:
            result = self.redis.delete(key)
            self.cache_stats['deletes'] += 1
            return result
        except Exception as e:
            logger.error(f"Cache delete error: {e}")
            return False

    async def exists(self, key: str):
        """Check if key exists in cache"""
        if not self.redis:
            return False
        try:
            return self.redis.exists(key)
        except Exception as e:
            logger.error(f"Cache exists error: {e}")
            return False

    async def get_multi(self, keys: List[str]) -> Dict[str, str]:
        """Get multiple values from cache"""
        if not self.redis or not keys:
            return {}
        try:
            values = self.redis.mget(keys)
            result = {}
            for i, key in enumerate(keys):
                if values[i] is not None:
                    result[key] = values[i]
                    self.cache_stats['hits'] += 1
                else:
                    self.cache_stats['misses'] += 1
            return result
        except Exception as e:
            logger.error(f"Cache mget error: {e}")
            return {}

    async def set_multi(self, data: Dict[str, str], ttl: int = None, tier: str = None):
        """Set multiple values in cache"""
        if not self.redis or not data:
            return False
        try:
            if tier and tier in self.cache_tiers:
                ttl = self.cache_tiers[tier]
            else:
                ttl = ttl or self.default_ttl

            pipe = self.redis.pipeline()
            for key, value in data.items():
                pipe.setex(key, ttl, value)

            results = pipe.execute()
            self.cache_stats['sets'] += len(data)
            return all(results)
        except Exception as e:
            logger.error(f"Cache mset error: {e}")
            return False

    async def invalidate_pattern(self, pattern: str):
        """Invalidate cache keys matching a pattern"""
        if not self.redis:
            return False
        try:
            keys = self.redis.keys(pattern)
            if keys:
                deleted = self.redis.delete(*keys)
                self.cache_stats['deletes'] += deleted
                logger.info(f"Invalidated {deleted} cache keys matching pattern: {pattern}")
                return deleted
            return 0
        except Exception as e:
            logger.error(f"Cache pattern invalidation error: {e}")
            return False

    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        try:
            total_requests = self.cache_stats['hits'] + self.cache_stats['misses']
            hit_rate = (self.cache_stats['hits'] / total_requests * 100) if total_requests > 0 else 0

            redis_info = {}
            if self.redis:
                redis_info = self.redis.info('memory')

            return {
                'hit_rate': round(hit_rate, 2),
                'total_hits': self.cache_stats['hits'],
                'total_misses': self.cache_stats['misses'],
                'total_sets': self.cache_stats['sets'],
                'total_deletes': self.cache_stats['deletes'],
                'redis_memory_used': redis_info.get('used_memory_human', 'N/A'),
                'redis_connected_clients': redis_info.get('connected_clients', 'N/A')
            }
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return self.cache_stats

    async def warm_cache(self, keys_data: Dict[str, str], tier: str = 'warm'):
        """Warm up cache with predefined data"""
        try:
            await self.set_multi(keys_data, tier=tier)
            logger.info(f"Warmed cache with {len(keys_data)} keys in {tier} tier")
            return True
        except Exception as e:
            logger.error(f"Cache warming error: {e}")
            return False


# Global cache manager instance
try:
    cache_manager = CacheManager()
except Exception as e:
    logger.warning(f"Cache manager initialization failed: {e}")
    cache_manager = None

-- AI Crypto Trading System Database Initialization
-- Author: inkbytefo

-- Create database if not exists (handled by <PERSON><PERSON>)
-- CREATE DATABASE IF NOT EXISTS crypto_trading;

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- <PERSON>reate custom types
DO $$ BEGIN
    CREATE TYPE trade_status AS ENUM ('pending', 'open', 'closed', 'cancelled', 'failed');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE trade_type AS ENUM ('buy', 'sell');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE order_type AS ENUM ('market', 'limit', 'stop_loss', 'take_profit');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Performance indexes (will be created by SQLAlchemy, but we can add additional ones)
-- These are commented out as they will be created automatically

-- Market data performance indexes
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_market_data_symbol_time
--     ON market_data (symbol, timestamp DESC);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_market_data_timeframe_time
--     ON market_data (timeframe, timestamp DESC);

-- User performance indexes
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email_active
--     ON users (email, is_active);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_telegram_id
--     ON users (telegram_user_id);

-- Trading performance indexes
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trades_user_symbol_date
--     ON trades (user_id, symbol, created_at DESC);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trades_status_date
--     ON trades (status, created_at DESC);

-- Portfolio performance indexes
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_portfolio_holdings_symbol
--     ON portfolio_holdings (portfolio_id, symbol);

-- System performance indexes
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_system_logs_level_timestamp
--     ON system_logs (log_level, timestamp DESC);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_system_metrics_component_timestamp
--     ON system_metrics (component, timestamp DESC);

-- Utility functions
CREATE OR REPLACE FUNCTION health_check()
RETURNS TEXT AS $$
BEGIN
    RETURN 'Database is healthy at ' || NOW();
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_portfolio_value(p_portfolio_id INTEGER)
RETURNS DECIMAL AS $$
DECLARE
    total_value DECIMAL := 0;
BEGIN
    SELECT COALESCE(SUM(total_value_usd), 0)
    INTO total_value
    FROM portfolio_holdings
    WHERE portfolio_id = p_portfolio_id;

    RETURN total_value;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION calculate_profit_loss(p_portfolio_id INTEGER)
RETURNS TABLE(total_pnl DECIMAL, pnl_percentage DECIMAL) AS $$
DECLARE
    total_invested DECIMAL := 0;
    current_value DECIMAL := 0;
    pnl DECIMAL := 0;
    pnl_pct DECIMAL := 0;
BEGIN
    -- Get total invested amount
    SELECT COALESCE(SUM(quantity * average_buy_price), 0)
    INTO total_invested
    FROM portfolio_holdings
    WHERE portfolio_id = p_portfolio_id;

    -- Get current portfolio value
    SELECT get_portfolio_value(p_portfolio_id) INTO current_value;

    -- Calculate P&L
    pnl := current_value - total_invested;

    -- Calculate P&L percentage
    IF total_invested > 0 THEN
        pnl_pct := (pnl / total_invested) * 100;
    ELSE
        pnl_pct := 0;
    END IF;

    RETURN QUERY SELECT pnl, pnl_pct;
END;
$$ LANGUAGE plpgsql;

-- Triggers for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Log initialization
DO $$
BEGIN
    RAISE NOTICE 'AI Crypto Trading System database initialized successfully';
    RAISE NOTICE 'Custom types, functions, and triggers created';
    RAISE NOTICE 'Ready for application startup';
END $$;

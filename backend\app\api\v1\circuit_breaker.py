"""
Circuit Breaker & Emergency Stop API Endpoints
Author: inkbytefo
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from typing import Dict, Any, Optional, List
from datetime import datetime
import logging

from app.services.circuit_breaker import (
    circuit_breaker_service, 
    CircuitBreakerState, 
    EmergencyLevel,
    RiskThresholds,
    CircuitBreakerEvent
)
from app.core.auth import get_current_user
from app.models.user import User
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)

router = APIRouter()

# Pydantic Models
class RiskThresholdsRequest(BaseModel):
    """Request model for updating risk thresholds"""
    max_daily_loss_pct: Optional[float] = Field(None, ge=0, le=100, description="Maximum daily loss percentage")
    max_weekly_loss_pct: Optional[float] = Field(None, ge=0, le=100, description="Maximum weekly loss percentage")
    max_monthly_loss_pct: Optional[float] = Field(None, ge=0, le=100, description="Maximum monthly loss percentage")
    max_drawdown_pct: Optional[float] = Field(None, ge=0, le=100, description="Maximum drawdown percentage")
    max_position_loss_pct: Optional[float] = Field(None, ge=0, le=100, description="Maximum loss per position")
    max_correlation_exposure: Optional[float] = Field(None, ge=0, le=1, description="Maximum correlated exposure")
    max_volatility_percentile: Optional[float] = Field(None, ge=0, le=100, description="Maximum volatility percentile")
    min_liquidity_ratio: Optional[float] = Field(None, ge=0, le=1, description="Minimum liquidity ratio")
    max_trades_per_hour: Optional[int] = Field(None, ge=1, description="Maximum trades per hour")
    max_trades_per_day: Optional[int] = Field(None, ge=1, description="Maximum trades per day")
    recovery_profit_threshold: Optional[float] = Field(None, ge=0, description="Recovery profit threshold")
    recovery_time_hours: Optional[int] = Field(None, ge=1, description="Recovery time in hours")

class EmergencyStopRequest(BaseModel):
    """Request model for emergency stop"""
    reason: str = Field(..., description="Reason for emergency stop")

class CircuitBreakerStatusResponse(BaseModel):
    """Response model for circuit breaker status"""
    state: str
    monitoring_active: bool
    last_check: Optional[datetime]
    current_thresholds: Dict[str, Any]
    recent_events: List[Dict[str, Any]]
    portfolio_metrics: Dict[str, float]

class CircuitBreakerEventResponse(BaseModel):
    """Response model for circuit breaker events"""
    timestamp: datetime
    event_type: str
    severity: str
    trigger_value: float
    threshold_value: float
    description: str
    affected_symbols: List[str]
    recommended_actions: List[str]

@router.get("/status", response_model=CircuitBreakerStatusResponse)
async def get_circuit_breaker_status(
    current_user: User = Depends(get_current_user)
):
    """
    Get current circuit breaker status and configuration
    
    Returns:
    - Current circuit breaker state
    - Risk thresholds configuration
    - Recent events and portfolio metrics
    """
    try:
        # Check current portfolio risk
        risk_event = await circuit_breaker_service.check_portfolio_risk(current_user.id)
        
        # Get recent events (last 10)
        recent_events = []
        for event in circuit_breaker_service.events[-10:]:
            recent_events.append({
                "timestamp": event.timestamp,
                "event_type": event.event_type,
                "severity": event.severity.value,
                "trigger_value": event.trigger_value,
                "threshold_value": event.threshold_value,
                "description": event.description,
                "affected_symbols": event.affected_symbols,
                "recommended_actions": event.recommended_actions
            })
        
        # Get current thresholds
        thresholds = circuit_breaker_service.thresholds
        current_thresholds = {
            "max_daily_loss_pct": thresholds.max_daily_loss_pct,
            "max_weekly_loss_pct": thresholds.max_weekly_loss_pct,
            "max_monthly_loss_pct": thresholds.max_monthly_loss_pct,
            "max_drawdown_pct": thresholds.max_drawdown_pct,
            "max_position_loss_pct": thresholds.max_position_loss_pct,
            "max_correlation_exposure": thresholds.max_correlation_exposure,
            "max_volatility_percentile": thresholds.max_volatility_percentile,
            "min_liquidity_ratio": thresholds.min_liquidity_ratio,
            "max_trades_per_hour": thresholds.max_trades_per_hour,
            "max_trades_per_day": thresholds.max_trades_per_day,
            "recovery_profit_threshold": thresholds.recovery_profit_threshold,
            "recovery_time_hours": thresholds.recovery_time_hours
        }
        
        # Portfolio metrics
        portfolio_metrics = {
            "last_portfolio_value": circuit_breaker_service.last_portfolio_value,
            "daily_start_value": circuit_breaker_service.daily_start_value,
            "weekly_start_value": circuit_breaker_service.weekly_start_value,
            "monthly_start_value": circuit_breaker_service.monthly_start_value,
            "peak_portfolio_value": circuit_breaker_service.peak_portfolio_value,
            "hourly_trade_count": circuit_breaker_service.hourly_trade_count,
            "daily_trade_count": circuit_breaker_service.daily_trade_count
        }
        
        response = CircuitBreakerStatusResponse(
            state=circuit_breaker_service.state.value,
            monitoring_active=circuit_breaker_service.monitoring_active,
            last_check=datetime.utcnow(),
            current_thresholds=current_thresholds,
            recent_events=recent_events,
            portfolio_metrics=portfolio_metrics
        )
        
        return response
        
    except Exception as e:
        logger.error(f"Error getting circuit breaker status: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get circuit breaker status: {str(e)}"
        )

@router.post("/configure")
async def configure_risk_thresholds(
    request: RiskThresholdsRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Configure risk thresholds for circuit breaker
    
    Updates the risk thresholds used by the circuit breaker system.
    Only provided values will be updated, others remain unchanged.
    """
    try:
        logger.info(f"User {current_user.username} updating circuit breaker thresholds")
        
        # Get current thresholds
        current_thresholds = circuit_breaker_service.thresholds
        
        # Update only provided values
        update_data = request.dict(exclude_unset=True)
        for key, value in update_data.items():
            if hasattr(current_thresholds, key):
                setattr(current_thresholds, key, value)
        
        # Apply updated thresholds
        circuit_breaker_service.configure_thresholds(current_thresholds)
        
        logger.info(f"Circuit breaker thresholds updated successfully")
        
        return {
            "message": "Risk thresholds updated successfully",
            "updated_fields": list(update_data.keys()),
            "current_thresholds": {
                "max_daily_loss_pct": current_thresholds.max_daily_loss_pct,
                "max_weekly_loss_pct": current_thresholds.max_weekly_loss_pct,
                "max_monthly_loss_pct": current_thresholds.max_monthly_loss_pct,
                "max_drawdown_pct": current_thresholds.max_drawdown_pct,
                "max_position_loss_pct": current_thresholds.max_position_loss_pct,
                "max_correlation_exposure": current_thresholds.max_correlation_exposure,
                "max_volatility_percentile": current_thresholds.max_volatility_percentile,
                "min_liquidity_ratio": current_thresholds.min_liquidity_ratio,
                "max_trades_per_hour": current_thresholds.max_trades_per_hour,
                "max_trades_per_day": current_thresholds.max_trades_per_day,
                "recovery_profit_threshold": current_thresholds.recovery_profit_threshold,
                "recovery_time_hours": current_thresholds.recovery_time_hours
            }
        }
        
    except Exception as e:
        logger.error(f"Error configuring risk thresholds: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to configure risk thresholds: {str(e)}"
        )

@router.post("/emergency-stop")
async def trigger_emergency_stop(
    request: EmergencyStopRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user)
):
    """
    Trigger manual emergency stop
    
    Immediately halts all trading and closes positions.
    This is a critical action that should only be used in emergencies.
    """
    try:
        logger.critical(f"User {current_user.username} triggered emergency stop: {request.reason}")
        
        # Execute emergency stop in background
        background_tasks.add_task(
            circuit_breaker_service.emergency_stop,
            current_user.id,
            request.reason
        )
        
        return {
            "message": "Emergency stop triggered successfully",
            "reason": request.reason,
            "timestamp": datetime.utcnow(),
            "user_id": current_user.id,
            "actions": [
                "All trading halted immediately",
                "Positions will be closed",
                "Notifications sent",
                "Circuit breaker activated"
            ]
        }
        
    except Exception as e:
        logger.error(f"Error triggering emergency stop: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to trigger emergency stop: {str(e)}"
        )

@router.post("/check-risk")
async def check_portfolio_risk(
    current_user: User = Depends(get_current_user)
):
    """
    Manually check portfolio for risk threshold violations
    
    Performs immediate risk assessment and returns any violations found.
    """
    try:
        logger.info(f"User {current_user.username} requesting manual risk check")
        
        # Check portfolio risk
        risk_event = await circuit_breaker_service.check_portfolio_risk(current_user.id)
        
        if risk_event:
            return {
                "risk_detected": True,
                "event": {
                    "timestamp": risk_event.timestamp,
                    "event_type": risk_event.event_type,
                    "severity": risk_event.severity.value,
                    "trigger_value": risk_event.trigger_value,
                    "threshold_value": risk_event.threshold_value,
                    "description": risk_event.description,
                    "recommended_actions": risk_event.recommended_actions
                },
                "message": "Risk threshold violation detected"
            }
        else:
            return {
                "risk_detected": False,
                "message": "No risk threshold violations detected",
                "portfolio_status": "healthy"
            }
        
    except Exception as e:
        logger.error(f"Error checking portfolio risk: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to check portfolio risk: {str(e)}"
        )

@router.post("/reset")
async def reset_circuit_breaker(
    current_user: User = Depends(get_current_user)
):
    """
    Reset circuit breaker to normal operation
    
    Resets the circuit breaker state and resumes normal trading.
    Only available when recovery conditions are met.
    """
    try:
        logger.info(f"User {current_user.username} requesting circuit breaker reset")
        
        # Check if recovery conditions are met
        can_recover = await circuit_breaker_service.check_recovery_conditions(current_user.id)
        
        if not can_recover:
            return {
                "success": False,
                "message": "Recovery conditions not met",
                "requirements": [
                    f"Wait at least {circuit_breaker_service.thresholds.recovery_time_hours} hours since last event",
                    f"Portfolio must show {circuit_breaker_service.thresholds.recovery_profit_threshold}% recovery",
                    "No active risk threshold violations"
                ]
            }
        
        # Reset circuit breaker
        await circuit_breaker_service.reset_circuit_breaker(current_user.id)
        
        return {
            "success": True,
            "message": "Circuit breaker reset successfully",
            "new_state": "closed",
            "timestamp": datetime.utcnow(),
            "actions": [
                "Circuit breaker state reset to CLOSED",
                "Normal trading operations resumed",
                "Tracking values reinitialized",
                "Notification sent"
            ]
        }
        
    except Exception as e:
        logger.error(f"Error resetting circuit breaker: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to reset circuit breaker: {str(e)}"
        )

@router.get("/events", response_model=List[CircuitBreakerEventResponse])
async def get_circuit_breaker_events(
    limit: int = 50,
    current_user: User = Depends(get_current_user)
):
    """
    Get circuit breaker event history
    
    Returns recent circuit breaker events and their details.
    """
    try:
        # Get recent events
        events = circuit_breaker_service.events[-limit:] if circuit_breaker_service.events else []
        
        response = []
        for event in reversed(events):  # Most recent first
            response.append(CircuitBreakerEventResponse(
                timestamp=event.timestamp,
                event_type=event.event_type,
                severity=event.severity.value,
                trigger_value=event.trigger_value,
                threshold_value=event.threshold_value,
                description=event.description,
                affected_symbols=event.affected_symbols,
                recommended_actions=event.recommended_actions
            ))
        
        return response
        
    except Exception as e:
        logger.error(f"Error getting circuit breaker events: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get circuit breaker events: {str(e)}"
        )

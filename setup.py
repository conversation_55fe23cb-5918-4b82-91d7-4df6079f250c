#!/usr/bin/env python3
"""
AI Crypto Trading System Setup Script
Author: inkbytefo
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def run_command(command, cwd=None):
    """Run a shell command"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            check=True, 
            cwd=cwd,
            capture_output=True,
            text=True
        )
        print(f"✅ {command}")
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running: {command}")
        print(f"Error: {e.stderr}")
        return None


def check_requirements():
    """Check if required tools are installed"""
    print("🔍 Checking requirements...")
    
    requirements = {
        "python": "python --version",
        "docker": "docker --version",
        "docker-compose": "docker-compose --version"
    }
    
    missing = []
    for tool, command in requirements.items():
        if run_command(command) is None:
            missing.append(tool)
    
    if missing:
        print(f"❌ Missing requirements: {', '.join(missing)}")
        print("Please install the missing tools and try again.")
        return False
    
    print("✅ All requirements satisfied!")
    return True


def setup_backend():
    """Setup Python backend"""
    print("\n🐍 Setting up Python backend...")
    
    backend_dir = Path("backend")
    
    # Create virtual environment
    if not (backend_dir / "venv").exists():
        run_command("python -m venv venv", cwd=backend_dir)
    
    # Activate virtual environment and install dependencies
    if sys.platform == "win32":
        pip_cmd = "venv\\Scripts\\pip"
    else:
        pip_cmd = "venv/bin/pip"
    
    run_command(f"{pip_cmd} install --upgrade pip", cwd=backend_dir)
    run_command(f"{pip_cmd} install -r requirements.txt", cwd=backend_dir)
    
    # Copy environment file
    env_file = backend_dir / ".env"
    env_example = backend_dir / ".env.example"
    
    if not env_file.exists() and env_example.exists():
        shutil.copy(env_example, env_file)
        print("📝 Created .env file from .env.example")
        print("⚠️  Please edit backend/.env with your API keys!")


def setup_frontend():
    """Frontend removed - using Telegram Bot interface"""
    print("\n🤖 Frontend removed - using Telegram Bot interface")
    print("✅ No frontend setup needed")


def setup_directories():
    """Create necessary directories"""
    print("\n📁 Creating directories...")
    
    directories = [
        "logs",
        "data",
        "backups",
        "backend/logs",
        "frontend/public",
        "frontend/src",
        "frontend/src/components",
        "frontend/src/pages",
        "frontend/src/utils"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ Created {directory}")


def main():
    """Main setup function"""
    print("🚀 AI Crypto Trading System Setup")
    print("=" * 40)
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Setup directories
    setup_directories()
    
    # Setup backend
    setup_backend()
    
    # Setup frontend
    setup_frontend()
    
    print("\n🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Edit backend/.env with your API keys")
    print("2. Run: python backend/simple_working_bot.py")
    print("3. Or use Docker: docker-compose up -d")
    print("4. API docs: http://localhost:8000/docs")
    print("5. Telegram Bot: Send /start to your bot")
    print("\n⚠️  Remember: This is for educational purposes only!")
    print("   Only use with funds you can afford to lose.")


if __name__ == "__main__":
    main()

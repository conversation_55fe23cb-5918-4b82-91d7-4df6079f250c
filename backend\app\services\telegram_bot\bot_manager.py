"""
Telegram Bot Manager
Author: inkbytefo

Main Telegram bot manager with AI assistant integration.
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from datetime import datetime

from telegram import Update, Bot
from telegram.ext import (
    Application, 
    CommandHandler, 
    MessageHandler, 
    filters,
    ContextTypes
)

from app.core.config import settings
from .ai_assistant import AIAssistant
from .user_manager import UserManager
from .notification_engine import NotificationEngine

logger = logging.getLogger(__name__)


class TelegramBotManager:
    """
    Main Telegram bot manager with AI assistant capabilities.
    Handles natural language commands and system integration.
    """
    
    def __init__(self):
        self.bot_token = settings.TELEGRAM_BOT_TOKEN
        self.application: Optional[Application] = None
        self.ai_assistant = AIAssistant()
        self.user_manager = UserManager()
        self.notification_engine = NotificationEngine()
        self._is_running = False
        
    async def initialize(self) -> None:
        """Initialize the Telegram bot application."""
        try:
            # Create application
            self.application = (
                Application.builder()
                .token(self.bot_token)
                .build()
            )
            
            # Register handlers
            await self._register_handlers()
            
            # Initialize components
            await self.ai_assistant.initialize()
            await self.user_manager.initialize()
            await self.notification_engine.initialize()
            
            logger.info("Telegram bot manager initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Telegram bot: {e}")
            raise
    
    async def _register_handlers(self) -> None:
        """Register all bot command and message handlers."""
        if not self.application:
            raise RuntimeError("Application not initialized")
            
        # Command handlers
        self.application.add_handler(
            CommandHandler("start", self._handle_start)
        )
        self.application.add_handler(
            CommandHandler("help", self._handle_help)
        )
        self.application.add_handler(
            CommandHandler("status", self._handle_status)
        )
        
        # Natural language message handler (AI Assistant)
        self.application.add_handler(
            MessageHandler(
                filters.TEXT & ~filters.COMMAND,
                self._handle_ai_message
            )
        )
        
        logger.info("Bot handlers registered successfully")
    
    async def _handle_start(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /start command."""
        user_id = update.effective_user.id
        username = update.effective_user.username or "Unknown"
        
        # Register or authenticate user
        is_authorized = await self.user_manager.authenticate_user(user_id, username)
        
        if is_authorized:
            welcome_message = (
                "🤖 **AI Crypto Trading Assistant'a Hoş Geldiniz!**\n\n"
                "Ben sizin AI destekli kripto trading asistanınızım. "
                "Doğal dil ile benimle konuşabilir, sistem durumunu sorgulayabilir, "
                "portföyünüzü takip edebilir ve trading sinyalleri alabilirsiniz.\n\n"
                "**Örnek Komutlar:**\n"
                "• 'BTC analizi yap'\n"
                "• 'Portföyümü göster'\n"
                "• 'Son trading sinyallerini getir'\n"
                "• 'Sistem durumu nasıl?'\n"
                "• 'ETH için fiyat alarmı kur'\n\n"
                "Herhangi bir sorunuz varsa, sadece doğal dil ile yazın!"
            )
        else:
            welcome_message = (
                "❌ **Erişim Reddedildi**\n\n"
                "Bu bot sadece yetkili kullanıcılar için erişilebilir. "
                "Erişim için sistem yöneticisi ile iletişime geçin."
            )
        
        await update.message.reply_text(welcome_message, parse_mode='Markdown')
    
    async def _handle_help(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /help command."""
        help_message = (
            "🆘 **AI Trading Assistant Yardım**\n\n"
            "**Temel Komutlar:**\n"
            "• `/start` - Bot'u başlat\n"
            "• `/help` - Bu yardım mesajı\n"
            "• `/status` - Sistem durumu\n\n"
            "**AI Asistan Özellikleri:**\n"
            "• 📊 **Analiz**: 'BTC analizi yap', 'ETH teknik analiz'\n"
            "• 💼 **Portföy**: 'Portföyümü göster', 'Kazanç/kayıp durumu'\n"
            "• 🔔 **Sinyaller**: 'Trading sinyalleri', 'Alım/satım önerileri'\n"
            "• ⚙️ **Sistem**: 'Sistem durumu', 'API bağlantıları'\n"
            "• 🚨 **Alarmlar**: 'BTC 50000$ alarm kur', 'Fiyat bildirimleri'\n\n"
            "**Doğal Dil Örnekleri:**\n"
            "• 'Bitcoin nasıl gidiyor?'\n"
            "• 'Hangi coinler yükselişte?'\n"
            "• 'Risk durumum nasıl?'\n"
            "• 'Son 1 saatte ne oldu?'\n\n"
            "Sadece doğal dil ile yazın, ben anlayacağım! 🤖"
        )
        
        await update.message.reply_text(help_message, parse_mode='Markdown')
    
    async def _handle_status(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /status command."""
        user_id = update.effective_user.id
        
        # Check user authorization
        if not await self.user_manager.is_authorized(user_id):
            await update.message.reply_text("❌ Bu komutu kullanma yetkiniz yok.")
            return
        
        # Get system status
        status_info = await self._get_system_status()
        
        status_message = (
            "📊 **Sistem Durumu**\n\n"
            f"🤖 **Bot Durumu**: {'🟢 Aktif' if self._is_running else '🔴 Pasif'}\n"
            f"🔗 **API Bağlantıları**: {status_info.get('api_status', '❓ Bilinmiyor')}\n"
            f"💾 **Veritabanı**: {status_info.get('db_status', '❓ Bilinmiyor')}\n"
            f"🧠 **AI Servisi**: {status_info.get('ai_status', '❓ Bilinmiyor')}\n"
            f"📈 **Market Data**: {status_info.get('market_status', '❓ Bilinmiyor')}\n\n"
            f"⏰ **Son Güncelleme**: {datetime.now().strftime('%H:%M:%S')}"
        )
        
        await update.message.reply_text(status_message, parse_mode='Markdown')
    
    async def _handle_ai_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle natural language messages with AI assistant."""
        user_id = update.effective_user.id
        message_text = update.message.text
        
        # Check user authorization
        if not await self.user_manager.is_authorized(user_id):
            await update.message.reply_text(
                "❌ Bu bot'u kullanma yetkiniz yok. /start komutu ile başlayın."
            )
            return
        
        try:
            # Show typing indicator
            await context.bot.send_chat_action(
                chat_id=update.effective_chat.id,
                action="typing"
            )
            
            # Process message with AI assistant
            response = await self.ai_assistant.process_message(
                user_id=user_id,
                message=message_text,
                context={"chat_id": update.effective_chat.id}
            )
            
            # Send response
            await update.message.reply_text(
                response.get("text", "Üzgünüm, bir hata oluştu."),
                parse_mode='Markdown'
            )
            
            # Handle any additional actions (notifications, etc.)
            if response.get("actions"):
                await self._handle_response_actions(
                    update, context, response["actions"]
                )
                
        except Exception as e:
            logger.error(f"Error processing AI message: {e}")
            await update.message.reply_text(
                "🚫 Mesajınızı işlerken bir hata oluştu. Lütfen tekrar deneyin."
            )
    
    async def _handle_response_actions(
        self, 
        update: Update, 
        context: ContextTypes.DEFAULT_TYPE,
        actions: list
    ) -> None:
        """Handle additional actions from AI response."""
        for action in actions:
            action_type = action.get("type")
            
            if action_type == "set_alert":
                # Set price alert
                await self.notification_engine.set_alert(
                    user_id=update.effective_user.id,
                    alert_config=action.get("config", {})
                )
            elif action_type == "send_chart":
                # Send chart image
                chart_data = action.get("chart_data")
                if chart_data:
                    # TODO: Generate and send chart
                    pass
    
    async def _get_system_status(self) -> Dict[str, str]:
        """Get current system status."""
        try:
            status = {}

            # Check API status
            try:
                from app.services.market_data_service import market_data_service
                ticker = await market_data_service.get_ticker_data("BTC/USDT")
                status["api_status"] = "🟢 Bağlı" if ticker else "🔴 Bağlantı Hatası"
            except Exception:
                status["api_status"] = "🔴 Bağlantı Hatası"

            # Check database status
            try:
                from app.core.database import get_db
                # Simple database check
                status["db_status"] = "🟢 Aktif"
            except Exception:
                status["db_status"] = "🔴 Bağlantı Hatası"

            # Check AI status
            try:
                from app.services.ai_analysis_service import AIAnalysisService
                # Test AI service availability
                ai_service = AIAnalysisService()
                status["ai_status"] = "🟢 Çalışıyor" if ai_service else "🔴 Servis Hatası"
            except Exception:
                status["ai_status"] = "🔴 Servis Hatası"

            # Check market data status
            try:
                from app.services.market_data_service import market_data_service
                # Check if we can get recent market data
                data = await market_data_service.get_ohlcv_data("BTC/USDT", "1h", 1)
                status["market_status"] = "🟢 Güncel" if data is not None else "🔴 Veri Hatası"
            except Exception:
                status["market_status"] = "🔴 Veri Hatası"

            # Check trading engine status
            try:
                from app.services.trading_engine_service import trading_engine_service
                market_status = await trading_engine_service.get_market_status()
                # Simple availability check
                status["trading_status"] = "🟢 Aktif" if trading_engine else "� Servis Hatası"
            except Exception:
                status["trading_status"] = "🔴 Bağlantı Hatası"

            return status

        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {
                "api_status": "🔴 Bilinmiyor",
                "db_status": "🔴 Bilinmiyor",
                "ai_status": "🔴 Bilinmiyor",
                "market_status": "🔴 Bilinmiyor",
                "trading_status": "🔴 Bilinmiyor"
            }
    
    async def start(self) -> None:
        """Start the Telegram bot."""
        if not self.application:
            await self.initialize()
        
        try:
            self._is_running = True
            logger.info("Starting Telegram bot...")
            
            # Start the bot
            await self.application.run_polling(
                drop_pending_updates=True,
                close_loop=False
            )
            
        except Exception as e:
            logger.error(f"Error starting Telegram bot: {e}")
            self._is_running = False
            raise
    
    async def stop(self) -> None:
        """Stop the Telegram bot."""
        if self.application:
            self._is_running = False
            await self.application.stop()
            logger.info("Telegram bot stopped")
    
    async def send_notification(
        self, 
        user_id: int, 
        message: str, 
        parse_mode: str = 'Markdown'
    ) -> bool:
        """Send notification to specific user."""
        try:
            if self.application and self.application.bot:
                await self.application.bot.send_message(
                    chat_id=user_id,
                    text=message,
                    parse_mode=parse_mode
                )
                return True
        except Exception as e:
            logger.error(f"Failed to send notification to {user_id}: {e}")
        
        return False

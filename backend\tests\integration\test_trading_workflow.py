"""
Integration Tests for Trading Workflow
Author: inkbytefo

Tests the complete trading workflow from signal generation to execution.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from fastapi.testclient import TestClient

from app.main import app
from app.services.market_data_service import market_data_service
from app.services.ai_analysis_service import ai_analysis_service
from app.services.risk_management_service import risk_management_service
from app.services.trading_engine_service import trading_engine_service
from app.services.portfolio_management_service import portfolio_management_service


class TestTradingWorkflow:
    """Integration tests for complete trading workflow"""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)
    
    @pytest.mark.asyncio
    async def test_complete_trading_workflow(self, sample_user, sample_portfolio):
        """Test complete trading workflow from analysis to execution."""
        
        # Mock external services
        with patch.multiple(
            'app.services.market_data_service',
            get_ticker_data=AsyncMock(return_value={
                'symbol': 'BTC/USDT',
                'last': 50000.0,
                'bid': 49950.0,
                'ask': 50050.0,
                'volume': 1000.0
            }),
            get_ohlcv_data=AsyncMock(return_value=[
                [1704067200000, 50000.0, 51000.0, 49000.0, 50500.0, 1000.0]
            ])
        ), patch.multiple(
            'app.services.ai_analysis_service',
            analyze_market_conditions=AsyncMock(return_value={
                'trend': 'bullish',
                'confidence': 0.8,
                'signals': ['buy'],
                'reasoning': 'Strong upward momentum'
            }),
            generate_trade_signal=AsyncMock(return_value={
                'symbol': 'BTC/USDT',
                'action': 'buy',
                'confidence': 0.8,
                'entry_price': 50000.0,
                'stop_loss': 48000.0,
                'take_profit': 55000.0
            })
        ):
            
            # Step 1: Market Analysis
            market_data = await market_data_service.get_ticker_data('BTC/USDT')
            assert market_data is not None
            
            # Step 2: AI Analysis
            analysis = await ai_analysis_service.analyze_market_conditions('BTC/USDT')
            assert analysis['confidence'] > 0.7
            
            # Step 3: Signal Generation
            signal = await ai_analysis_service.generate_trade_signal('BTC/USDT', analysis)
            assert signal['action'] in ['buy', 'sell']
            
            # Step 4: Risk Assessment
            risk_assessment = await risk_management_service.assess_trade_risk({
                'symbol': signal['symbol'],
                'entry_price': signal['entry_price'],
                'stop_loss': signal['stop_loss'],
                'take_profit': signal['take_profit'],
                'position_size': 0.1,
                'portfolio_value': sample_portfolio.current_balance
            })
            
            assert risk_assessment.risk_level in ['low', 'medium', 'high']
            
            # Step 5: Position Sizing
            position_result = await risk_management_service.calculate_position_size(
                symbol=signal['symbol'],
                entry_price=signal['entry_price'],
                portfolio_value=sample_portfolio.current_balance,
                signal_confidence=signal['confidence']
            )
            
            assert position_result.position_size > 0
            
            # Step 6: Trade Execution (mocked)
            with patch.object(trading_engine_service, 'execute_trade_signal') as mock_execute:
                mock_execute.return_value = {
                    'success': True,
                    'trade_id': 'test_trade_123',
                    'order_id': 'order_456',
                    'status': 'filled',
                    'executed_price': signal['entry_price'],
                    'executed_quantity': position_result.position_size
                }
                
                execution_result = await trading_engine_service.execute_trade_signal(
                    signal=signal,
                    portfolio_value=sample_portfolio.current_balance,
                    user_id=sample_user.id,
                    auto_execute=False
                )
                
                assert execution_result['success']
                assert execution_result['trade_id'] is not None
            
            # Step 7: Portfolio Update
            portfolio_summary = await portfolio_management_service.get_portfolio_summary(
                user_id=sample_user.id,
                portfolio_id=sample_portfolio.id
            )
            
            assert portfolio_summary is not None
    
    @pytest.mark.asyncio
    async def test_risk_management_integration(self, sample_user, sample_portfolio):
        """Test risk management integration with trading engine."""
        
        # Create a high-risk trade scenario
        high_risk_signal = {
            'symbol': 'BTC/USDT',
            'action': 'buy',
            'confidence': 0.9,
            'entry_price': 50000.0,
            'stop_loss': 45000.0,  # 10% stop loss (high risk)
            'take_profit': 55000.0
        }
        
        # Risk assessment should flag this as high risk
        risk_assessment = await risk_management_service.assess_trade_risk({
            'symbol': high_risk_signal['symbol'],
            'entry_price': high_risk_signal['entry_price'],
            'stop_loss': high_risk_signal['stop_loss'],
            'take_profit': high_risk_signal['take_profit'],
            'position_size': 0.2,  # 20% position size
            'portfolio_value': sample_portfolio.current_balance
        })
        
        assert risk_assessment.risk_level in ['medium', 'high']
        
        # Trading engine should respect risk limits
        with patch.object(trading_engine_service, 'execute_trade_signal') as mock_execute:
            mock_execute.return_value = {
                'success': False,
                'error': 'Trade rejected due to high risk',
                'risk_violations': ['Position size too large', 'Stop loss too wide']
            }
            
            execution_result = await trading_engine_service.execute_trade_signal(
                signal=high_risk_signal,
                portfolio_value=sample_portfolio.current_balance,
                user_id=sample_user.id,
                auto_execute=True
            )
            
            assert not execution_result['success']
            assert 'risk_violations' in execution_result
    
    @pytest.mark.asyncio
    async def test_portfolio_tracking_integration(self, sample_user, sample_portfolio, sample_trade):
        """Test portfolio tracking integration with trading operations."""
        
        # Initial portfolio state
        initial_summary = await portfolio_management_service.get_portfolio_summary(
            user_id=sample_user.id,
            portfolio_id=sample_portfolio.id
        )
        
        assert initial_summary is not None
        initial_balance = initial_summary.get('total_value', 0)
        
        # Simulate trade execution
        trade_data = {
            'symbol': 'BTC/USDT',
            'side': 'buy',
            'quantity': 0.1,
            'price': 50000.0,
            'fee': 10.0
        }
        
        # Update portfolio after trade
        await portfolio_management_service.update_portfolio_after_trade(
            portfolio_id=sample_portfolio.id,
            trade_data=trade_data
        )
        
        # Check portfolio update
        updated_summary = await portfolio_management_service.get_portfolio_summary(
            user_id=sample_user.id,
            portfolio_id=sample_portfolio.id
        )
        
        assert updated_summary is not None
        # Portfolio should reflect the trade
        assert len(updated_summary.get('positions', [])) > 0
    
    @pytest.mark.asyncio
    async def test_market_data_ai_integration(self):
        """Test integration between market data and AI analysis services."""
        
        symbol = 'BTC/USDT'
        
        # Mock market data
        with patch.object(market_data_service, 'get_ohlcv_data') as mock_ohlcv:
            mock_ohlcv.return_value = [
                [1704067200000, 50000.0, 51000.0, 49000.0, 50500.0, 1000.0],
                [1704070800000, 50500.0, 51500.0, 49500.0, 51000.0, 1100.0],
                [1704074400000, 51000.0, 52000.0, 50000.0, 51500.0, 1200.0]
            ]
            
            # AI service should be able to process market data
            with patch.object(ai_analysis_service, 'analyze_market_conditions') as mock_analyze:
                mock_analyze.return_value = {
                    'trend': 'bullish',
                    'confidence': 0.8,
                    'technical_indicators': {
                        'rsi': 65.0,
                        'macd': 'bullish',
                        'bollinger': 'middle'
                    },
                    'patterns': ['ascending_triangle'],
                    'support_resistance': {
                        'support': [49000.0, 48000.0],
                        'resistance': [52000.0, 53000.0]
                    }
                }
                
                # Get market data
                ohlcv_data = await market_data_service.get_ohlcv_data(symbol, '1h', 100)
                assert len(ohlcv_data) > 0
                
                # Analyze with AI
                analysis = await ai_analysis_service.analyze_market_conditions(symbol)
                assert analysis['confidence'] > 0
                assert 'technical_indicators' in analysis
    
    @pytest.mark.asyncio
    async def test_error_handling_in_workflow(self, sample_user, sample_portfolio):
        """Test error handling throughout the trading workflow."""
        
        # Test market data failure
        with patch.object(market_data_service, 'get_ticker_data') as mock_ticker:
            mock_ticker.side_effect = Exception("Market data unavailable")
            
            with pytest.raises(Exception):
                await market_data_service.get_ticker_data('BTC/USDT')
        
        # Test AI analysis failure
        with patch.object(ai_analysis_service, 'analyze_market_conditions') as mock_analyze:
            mock_analyze.side_effect = Exception("AI service unavailable")
            
            with pytest.raises(Exception):
                await ai_analysis_service.analyze_market_conditions('BTC/USDT')
        
        # Test trading engine failure
        with patch.object(trading_engine_service, 'execute_trade_signal') as mock_execute:
            mock_execute.return_value = {
                'success': False,
                'error': 'Exchange connection failed',
                'retry_after': 60
            }
            
            signal = {
                'symbol': 'BTC/USDT',
                'action': 'buy',
                'confidence': 0.8,
                'entry_price': 50000.0
            }
            
            result = await trading_engine_service.execute_trade_signal(
                signal=signal,
                portfolio_value=sample_portfolio.current_balance,
                user_id=sample_user.id
            )
            
            assert not result['success']
            assert 'error' in result
    
    @pytest.mark.asyncio
    async def test_concurrent_trading_operations(self, sample_user, sample_portfolio):
        """Test handling of concurrent trading operations."""
        
        # Create multiple concurrent trade signals
        signals = [
            {
                'symbol': 'BTC/USDT',
                'action': 'buy',
                'confidence': 0.8,
                'entry_price': 50000.0
            },
            {
                'symbol': 'ETH/USDT',
                'action': 'buy',
                'confidence': 0.7,
                'entry_price': 3000.0
            },
            {
                'symbol': 'ADA/USDT',
                'action': 'sell',
                'confidence': 0.6,
                'entry_price': 0.5
            }
        ]
        
        # Mock trading engine responses
        with patch.object(trading_engine_service, 'execute_trade_signal') as mock_execute:
            mock_execute.return_value = {
                'success': True,
                'trade_id': 'test_trade',
                'status': 'pending'
            }
            
            # Execute trades concurrently
            tasks = [
                trading_engine_service.execute_trade_signal(
                    signal=signal,
                    portfolio_value=sample_portfolio.current_balance,
                    user_id=sample_user.id
                )
                for signal in signals
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # All trades should complete (even if some fail)
            assert len(results) == len(signals)
            
            # Check that no exceptions were raised
            for result in results:
                assert not isinstance(result, Exception)

"""
Technical Analysis Database Models and Schemas
Author: inkbytefo
"""

from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, Text, ForeignKey, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum
import uuid

from app.core.database import Base


class SignalType(str, Enum):
    """Signal types"""
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"


class TrendDirection(str, Enum):
    """Trend directions"""
    BULLISH = "bullish"
    BEARISH = "bearish"
    SIDEWAYS = "sideways"


class PatternType(str, Enum):
    """Chart pattern types"""
    SUPPORT_RESISTANCE = "support_resistance"
    TREND_LINE = "trend_line"
    TRIANGLE = "triangle"
    HEAD_SHOULDERS = "head_shoulders"
    DOUBLE_TOP = "double_top"
    DOUBLE_BOTTOM = "double_bottom"
    FLAG = "flag"
    PENNANT = "pennant"
    WEDGE = "wedge"
    CHANNEL = "channel"


# Database Models

class TechnicalIndicator(Base):
    """Technical indicators storage"""
    __tablename__ = "technical_indicators"
    
    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String(20), nullable=False, index=True)
    exchange = Column(String(20), nullable=False, default="binance")
    timeframe = Column(String(10), nullable=False, index=True)
    
    # Price-based indicators
    sma_20 = Column(Float)
    sma_50 = Column(Float)
    ema_12 = Column(Float)
    ema_26 = Column(Float)
    
    # Momentum indicators
    rsi = Column(Float)
    macd = Column(Float)
    macd_signal = Column(Float)
    macd_histogram = Column(Float)
    
    # Volatility indicators
    bb_upper = Column(Float)
    bb_middle = Column(Float)
    bb_lower = Column(Float)
    bb_width = Column(Float)
    
    # Volume indicators
    volume_sma = Column(Float)
    volume_spike = Column(Boolean, default=False)
    
    # Support/Resistance
    support_level = Column(Float)
    resistance_level = Column(Float)
    
    # Trend indicators
    trend_direction = Column(String(20))  # bullish, bearish, sideways
    trend_strength = Column(Float)
    
    # Metadata
    timestamp = Column(DateTime, nullable=False, index=True)
    created_at = Column(DateTime, default=func.now())
    
    __table_args__ = (
        Index('idx_symbol_exchange_timeframe_timestamp', 'symbol', 'exchange', 'timeframe', 'timestamp'),
    )


class TechnicalSignalDB(Base):
    """Technical analysis signals storage"""
    __tablename__ = "technical_signals"
    
    id = Column(Integer, primary_key=True, index=True)
    signal_uuid = Column(String(36), unique=True, index=True, default=lambda: str(uuid.uuid4()))
    
    # Signal Information
    symbol = Column(String(20), nullable=False, index=True)
    exchange = Column(String(20), nullable=False, default="binance")
    timeframe = Column(String(10), nullable=False, index=True)
    signal_type = Column(String(20), nullable=False)  # buy, sell, hold
    strength = Column(Float, nullable=False)  # Signal strength (0-1)
    confidence = Column(Float, nullable=False)  # Confidence (0-1)
    
    # Price Targets
    entry_price = Column(Float)
    target_price = Column(Float)
    stop_loss_price = Column(Float)
    
    # Analysis Details
    reasoning = Column(Text)
    
    # Technical Indicators (snapshot)
    rsi_value = Column(Float)
    macd_value = Column(Float)
    trend_direction = Column(String(20))
    volume_spike = Column(Boolean, default=False)
    
    # Signal Status
    is_active = Column(Boolean, default=True)
    is_executed = Column(Boolean, default=False)
    
    # Metadata
    created_at = Column(DateTime, default=func.now(), index=True)
    expires_at = Column(DateTime)
    executed_at = Column(DateTime)
    
    # Foreign Keys
    indicator_id = Column(Integer, ForeignKey("technical_indicators.id"))
    
    # Relationships
    indicator = relationship("TechnicalIndicator")
    
    __table_args__ = (
        Index('idx_symbol_signal_type_active', 'symbol', 'signal_type', 'is_active'),
        Index('idx_created_at_active', 'created_at', 'is_active'),
    )


class PatternDetectionDB(Base):
    """Chart pattern detection storage"""
    __tablename__ = "pattern_detections"
    
    id = Column(Integer, primary_key=True, index=True)
    pattern_uuid = Column(String(36), unique=True, index=True, default=lambda: str(uuid.uuid4()))
    
    # Pattern Information
    symbol = Column(String(20), nullable=False, index=True)
    exchange = Column(String(20), nullable=False, default="binance")
    timeframe = Column(String(10), nullable=False, index=True)
    pattern_type = Column(String(50), nullable=False)
    pattern_name = Column(String(100), nullable=False)
    
    # Pattern Details
    confidence = Column(Float, nullable=False)  # 0-1
    breakout_direction = Column(String(20))  # bullish, bearish, sideways
    target_price = Column(Float)
    stop_loss = Column(Float)
    description = Column(Text)
    
    # Pattern Coordinates (JSON stored as text)
    pattern_data = Column(Text)  # JSON string with pattern coordinates
    
    # Status
    is_active = Column(Boolean, default=True)
    is_confirmed = Column(Boolean, default=False)
    
    # Metadata
    detected_at = Column(DateTime, default=func.now(), index=True)
    confirmed_at = Column(DateTime)
    
    # Foreign Keys
    signal_id = Column(Integer, ForeignKey("technical_signals.id"))
    
    # Relationships
    signal = relationship("TechnicalSignalDB")
    
    __table_args__ = (
        Index('idx_symbol_pattern_type_active', 'symbol', 'pattern_type', 'is_active'),
    )


class AnalysisSession(Base):
    """Analysis session tracking"""
    __tablename__ = "analysis_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    session_uuid = Column(String(36), unique=True, index=True, default=lambda: str(uuid.uuid4()))
    
    # Session Information
    symbols_analyzed = Column(Text)  # JSON array of symbols
    timeframes = Column(Text)  # JSON array of timeframes
    exchange = Column(String(20), nullable=False, default="binance")
    
    # Results Summary
    total_symbols = Column(Integer, default=0)
    successful_analysis = Column(Integer, default=0)
    signals_generated = Column(Integer, default=0)
    patterns_detected = Column(Integer, default=0)
    
    # Performance Metrics
    execution_time_ms = Column(Integer)
    cache_hit_rate = Column(Float)
    
    # Metadata
    started_at = Column(DateTime, default=func.now())
    completed_at = Column(DateTime)
    
    __table_args__ = (
        Index('idx_started_at', 'started_at'),
    )


# Pydantic Models for API

class TechnicalIndicatorResponse(BaseModel):
    """Technical indicators response model"""
    symbol: str
    timeframe: str
    exchange: str
    
    # Moving averages
    sma_20: Optional[float] = None
    sma_50: Optional[float] = None
    ema_12: Optional[float] = None
    ema_26: Optional[float] = None
    
    # Momentum
    rsi: Optional[float] = None
    macd: Optional[float] = None
    macd_signal: Optional[float] = None
    macd_histogram: Optional[float] = None
    
    # Bollinger Bands
    bb_upper: Optional[float] = None
    bb_middle: Optional[float] = None
    bb_lower: Optional[float] = None
    bb_width: Optional[float] = None
    
    # Volume
    volume_sma: Optional[float] = None
    volume_spike: bool = False
    
    # Support/Resistance
    support_level: Optional[float] = None
    resistance_level: Optional[float] = None
    
    # Trend
    trend_direction: Optional[TrendDirection] = None
    trend_strength: Optional[float] = None
    
    timestamp: datetime
    
    class Config:
        from_attributes = True


class TechnicalSignalResponse(BaseModel):
    """Technical signal response model"""
    signal_uuid: str
    symbol: str
    exchange: str
    timeframe: str
    signal_type: SignalType
    strength: float = Field(..., ge=0, le=1)
    confidence: float = Field(..., ge=0, le=1)
    
    entry_price: Optional[float] = None
    target_price: Optional[float] = None
    stop_loss_price: Optional[float] = None
    reasoning: str = ""
    
    # Indicator snapshot
    rsi_value: Optional[float] = None
    macd_value: Optional[float] = None
    trend_direction: Optional[TrendDirection] = None
    volume_spike: bool = False
    
    is_active: bool = True
    is_executed: bool = False
    created_at: datetime
    expires_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class PatternDetectionResponse(BaseModel):
    """Pattern detection response model"""
    pattern_uuid: str
    symbol: str
    exchange: str
    timeframe: str
    pattern_type: str
    pattern_name: str
    confidence: float = Field(..., ge=0, le=1)
    
    breakout_direction: Optional[TrendDirection] = None
    target_price: Optional[float] = None
    stop_loss: Optional[float] = None
    description: str = ""
    
    is_active: bool = True
    is_confirmed: bool = False
    detected_at: datetime
    confirmed_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class MultiTimeframeAnalysisResponse(BaseModel):
    """Multi-timeframe analysis response"""
    symbol: str
    exchange: str
    timeframes: Dict[str, Dict[str, Any]]
    overall_sentiment: Dict[str, Any]
    analysis_timestamp: datetime
    status: str = "success"


class MarketOverviewResponse(BaseModel):
    """Market overview response"""
    timeframe: str
    exchange: str
    symbols_analyzed: int
    successful_analysis: int
    market_sentiment: Dict[str, Any]
    symbol_analysis: Dict[str, Dict[str, Any]]
    analysis_timestamp: datetime
    status: str = "success"


class PositionSizeRequest(BaseModel):
    """Position size calculation request"""
    symbol: str
    account_balance: float = Field(..., gt=0)
    risk_percentage: float = Field(0.02, gt=0, le=0.1)
    timeframe: str = "1h"
    exchange: str = "binance"


class PositionSizeResponse(BaseModel):
    """Position size calculation response"""
    symbol: str
    signal: Dict[str, Any]
    position_sizing: Dict[str, float]
    risk_reward: Dict[str, float]
    timestamp: datetime
    status: str = "success"


# Signal Generation Database Models
class TradingSignalDB(Base):
    """Trading signal database model"""
    __tablename__ = "trading_signals"

    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String(20), nullable=False, index=True)
    timeframe = Column(String(10), nullable=False)
    exchange = Column(String(20), nullable=False, default="binance")

    # Signal details
    signal_type = Column(String(20), nullable=False)  # buy, sell, hold, strong_buy, strong_sell
    signal_strength = Column(String(20), nullable=False)  # weak, moderate, strong, very_strong
    overall_score = Column(Float, nullable=False)
    confidence = Column(Float, nullable=False)
    reasoning = Column(Text)

    # Entry/Exit points
    entry_price = Column(Float, nullable=False)
    entry_confidence = Column(Float, nullable=False)
    stop_loss = Column(Float)
    take_profit = Column(Float)
    risk_reward_ratio = Column(Float)

    # Position sizing
    position_size_percentage = Column(Float, nullable=False)
    position_size_usd = Column(Float)
    max_loss_percentage = Column(Float, nullable=False)
    risk_level = Column(String(20), nullable=False)  # low, medium, high, very_high
    leverage = Column(Float, default=1.0)

    # Risk metrics
    max_drawdown_risk = Column(Float, default=0.0)
    volatility_risk = Column(Float, default=0.0)
    market_correlation_risk = Column(Float, default=0.0)

    # Metadata
    generated_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    valid_until = Column(DateTime, nullable=False)
    is_active = Column(Boolean, default=True)

    # Relationships
    signal_factors = relationship("SignalFactorDB", back_populates="trading_signal", cascade="all, delete-orphan")
    exit_points = relationship("ExitPointDB", back_populates="trading_signal", cascade="all, delete-orphan")


class SignalFactorDB(Base):
    """Signal factor database model"""
    __tablename__ = "signal_factors"

    id = Column(Integer, primary_key=True, index=True)
    trading_signal_id = Column(Integer, ForeignKey("trading_signals.id"), nullable=False)

    # Factor details
    factor_category = Column(String(20), nullable=False)  # technical, pattern, market
    factor_name = Column(String(50), nullable=False)
    factor_value = Column(Float, nullable=False)  # -1 to 1
    factor_weight = Column(Float, nullable=False)  # 0 to 1
    factor_confidence = Column(Float, nullable=False)  # 0 to 1
    description = Column(Text)

    # Metadata
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)

    # Relationships
    trading_signal = relationship("TradingSignalDB", back_populates="signal_factors")


class ExitPointDB(Base):
    """Exit point database model"""
    __tablename__ = "exit_points"

    id = Column(Integer, primary_key=True, index=True)
    trading_signal_id = Column(Integer, ForeignKey("trading_signals.id"), nullable=False)

    # Exit point details
    exit_price = Column(Float, nullable=False)
    exit_confidence = Column(Float, nullable=False)
    exit_reasoning = Column(Text)
    risk_reward_ratio = Column(Float)
    position_percentage = Column(Float)  # What % of position to exit

    # Metadata
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)

    # Relationships
    trading_signal = relationship("TradingSignalDB", back_populates="exit_points")


class SignalPerformanceDB(Base):
    """Signal performance tracking model"""
    __tablename__ = "signal_performance"

    id = Column(Integer, primary_key=True, index=True)
    trading_signal_id = Column(Integer, ForeignKey("trading_signals.id"), nullable=False)

    # Performance metrics
    actual_entry_price = Column(Float)
    actual_exit_price = Column(Float)
    actual_profit_loss = Column(Float)
    actual_profit_loss_percentage = Column(Float)

    # Execution details
    executed_at = Column(DateTime)
    closed_at = Column(DateTime)
    execution_status = Column(String(20))  # pending, executed, closed, cancelled

    # Performance analysis
    signal_accuracy = Column(Float)  # How accurate was the signal
    timing_accuracy = Column(Float)  # How good was the timing

    # Metadata
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)


# Signal Generation Response Models
class SignalFactorResponse(BaseModel):
    """Signal factor response model"""
    name: str
    value: float
    weight: float
    confidence: float
    description: str


class EntryExitPointResponse(BaseModel):
    """Entry/exit point response model"""
    price: float
    confidence: float
    reasoning: str
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    risk_reward_ratio: Optional[float] = None


class PositionSizingResponse(BaseModel):
    """Position sizing response model"""
    position_size_percentage: float
    position_size_usd: Optional[float] = None
    max_loss_percentage: float
    risk_level: str
    leverage: float
    reasoning: str


class TradingSignalResponse(BaseModel):
    """Trading signal response model"""
    symbol: str
    timeframe: str
    exchange: str
    signal_type: str
    signal_strength: str
    overall_score: float
    confidence: float
    reasoning: str

    # Entry/Exit points
    entry_point: EntryExitPointResponse
    exit_points: List[EntryExitPointResponse]

    # Position sizing
    position_sizing: PositionSizingResponse

    # Signal factors
    technical_factors: List[SignalFactorResponse]
    pattern_factors: List[SignalFactorResponse]
    market_factors: List[SignalFactorResponse]

    # Risk metrics
    max_drawdown_risk: float
    volatility_risk: float
    market_correlation_risk: float

    # Metadata
    generated_at: datetime
    valid_until: datetime
    is_valid: bool

    class Config:
        from_attributes = True


class MarketSignalSummaryResponse(BaseModel):
    """Market signal summary response model"""
    market_sentiment: str
    signal_distribution: Dict[str, int]
    average_confidence: float
    total_symbols_analyzed: int
    valid_signals: int
    analysis_timestamp: str

"""
Notification Engine for Telegram Bot
Author: inkbytefo

Smart notification system for trading alerts, price alerts, and system notifications.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import json

logger = logging.getLogger(__name__)


class NotificationType(Enum):
    """Notification types."""
    PRICE_ALERT = "price_alert"
    PORTFOLIO_UPDATE = "portfolio_update"
    SYSTEM_ALERT = "system_alert"
    TRADE_SIGNAL = "trade_signal"
    NEWS_UPDATE = "news_update"


@dataclass
class Notification:
    """Notification data structure."""
    user_id: int
    type: NotificationType
    title: str
    message: str
    data: Dict[str, Any] = None
    created_at: datetime = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.data is None:
            self.data = {}


@dataclass
class PriceAlert:
    """Price alert configuration."""
    user_id: int
    symbol: str
    target_price: float
    condition: str  # 'above', 'below', 'equals'
    created_at: datetime
    is_active: bool = True
    recurring: bool = False
    triggered_at: Optional[datetime] = None


@dataclass
class NotificationTemplate:
    """Notification template."""
    template_id: str
    title: str
    message_template: str
    priority: str  # 'low', 'medium', 'high', 'critical'
    emoji: str


class NotificationEngine:
    """
    Smart notification engine for Telegram bot.
    Handles price alerts, trading signals, and system notifications.
    """
    
    def __init__(self):
        self.price_alerts: Dict[int, List[PriceAlert]] = {}
        self.notification_history: List[Dict] = []
        self.templates: Dict[str, NotificationTemplate] = {}
        self.user_preferences: Dict[int, Dict] = {}
        self._monitoring_task: Optional[asyncio.Task] = None
        
    async def initialize(self) -> None:
        """Initialize the notification engine."""
        try:
            # Load notification templates
            self._load_templates()
            
            # Start monitoring task
            self._monitoring_task = asyncio.create_task(self._monitoring_loop())
            
            logger.info("Notification engine initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize notification engine: {e}")
            raise
    
    def _load_templates(self) -> None:
        """Load notification templates."""
        templates = [
            NotificationTemplate(
                template_id="price_alert_triggered",
                title="🚨 Fiyat Alarmı",
                message_template="**{symbol}** fiyatı **${price}** seviyesini {condition_text}!\n\n📊 Mevcut Fiyat: ${current_price}\n⏰ Alarm Zamanı: {time}",
                priority="high",
                emoji="🚨"
            ),
            NotificationTemplate(
                template_id="trading_signal",
                title="📈 Trading Sinyali",
                message_template="**{signal_type}** sinyali: **{symbol}**\n\n💪 Güven: {confidence}%\n🎯 Hedef: ${target_price}\n🛡️ Stop Loss: ${stop_loss}\n\n{reasoning}",
                priority="medium",
                emoji="📈"
            ),
            NotificationTemplate(
                template_id="portfolio_update",
                title="💼 Portföy Güncellemesi",
                message_template="**Portföy Durumu**\n\n💰 Toplam Değer: ${total_value}\n📊 Günlük Değişim: {daily_change}%\n\n{details}",
                priority="low",
                emoji="💼"
            ),
            NotificationTemplate(
                template_id="system_alert",
                title="⚠️ Sistem Bildirimi",
                message_template="**Sistem Durumu**\n\n{message}\n\n⏰ Zaman: {time}",
                priority="critical",
                emoji="⚠️"
            ),
            NotificationTemplate(
                template_id="market_news",
                title="📰 Piyasa Haberleri",
                message_template="**{title}**\n\n{summary}\n\n🔗 [Devamını Oku]({url})\n⏰ {time}",
                priority="low",
                emoji="📰"
            )
        ]
        
        for template in templates:
            self.templates[template.template_id] = template
    
    async def set_alert(self, user_id: int, alert_config: Dict[str, Any]) -> bool:
        """Set a price alert for user."""
        try:
            symbol = alert_config.get("symbol", "").upper()
            price = float(alert_config.get("price", 0))
            condition = alert_config.get("condition", "above").lower()
            
            if not symbol or price <= 0:
                return False
            
            # Create alert
            alert = PriceAlert(
                user_id=user_id,
                symbol=symbol,
                price=price,
                condition=condition,
                created_at=datetime.now()
            )
            
            # Add to user's alerts
            if user_id not in self.price_alerts:
                self.price_alerts[user_id] = []
            
            self.price_alerts[user_id].append(alert)
            
            logger.info(f"Price alert set for user {user_id}: {symbol} {condition} ${price}")
            return True
            
        except Exception as e:
            logger.error(f"Error setting price alert: {e}")
            return False
    
    async def remove_alert(self, user_id: int, symbol: str, price: float) -> bool:
        """Remove a specific price alert."""
        try:
            if user_id not in self.price_alerts:
                return False
            
            # Find and remove the alert
            alerts = self.price_alerts[user_id]
            for i, alert in enumerate(alerts):
                if alert.symbol == symbol.upper() and alert.price == price:
                    del alerts[i]
                    logger.info(f"Price alert removed for user {user_id}: {symbol} ${price}")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error removing price alert: {e}")
            return False
    
    async def get_user_alerts(self, user_id: int) -> List[Dict[str, Any]]:
        """Get all active alerts for user."""
        if user_id not in self.price_alerts:
            return []
        
        return [
            {
                "symbol": alert.symbol,
                "price": alert.price,
                "condition": alert.condition,
                "created_at": alert.created_at.isoformat(),
                "is_active": alert.is_active
            }
            for alert in self.price_alerts[user_id]
            if alert.is_active
        ]
    
    async def send_notification(
        self, 
        user_id: int, 
        template_id: str, 
        data: Dict[str, Any],
        bot_manager=None
    ) -> bool:
        """Send a notification to user."""
        try:
            if template_id not in self.templates:
                logger.error(f"Unknown notification template: {template_id}")
                return False
            
            template = self.templates[template_id]
            
            # Format message
            message = self._format_message(template, data)
            
            # Send via bot manager if available
            if bot_manager:
                success = await bot_manager.send_notification(user_id, message)
            else:
                # Log notification (for testing)
                logger.info(f"Notification for user {user_id}: {message}")
                success = True
            
            # Record in history
            self.notification_history.append({
                "user_id": user_id,
                "template_id": template_id,
                "message": message,
                "sent_at": datetime.now().isoformat(),
                "success": success
            })
            
            return success
            
        except Exception as e:
            logger.error(f"Error sending notification: {e}")
            return False
    
    def _format_message(self, template: NotificationTemplate, data: Dict[str, Any]) -> str:
        """Format notification message using template."""
        try:
            # Add emoji to title
            title = f"{template.emoji} {template.title}"
            
            # Format message template
            message = template.message_template.format(**data)
            
            return f"{title}\n\n{message}"
            
        except KeyError as e:
            logger.error(f"Missing template data: {e}")
            return f"{template.emoji} Bildirim formatlanırken hata oluştu."
        except Exception as e:
            logger.error(f"Error formatting message: {e}")
            return f"{template.emoji} Bildirim gönderilemedi."
    
    async def check_price_alerts(self, market_data: Dict[str, float]) -> List[Dict[str, Any]]:
        """Check price alerts against current market data."""
        triggered_alerts = []
        
        try:
            for user_id, alerts in self.price_alerts.items():
                for alert in alerts:
                    if not alert.is_active or alert.symbol not in market_data:
                        continue
                    
                    current_price = market_data[alert.symbol]
                    should_trigger = False
                    
                    if alert.condition == "above" and current_price >= alert.price:
                        should_trigger = True
                    elif alert.condition == "below" and current_price <= alert.price:
                        should_trigger = True
                    
                    if should_trigger:
                        # Mark alert as triggered
                        alert.is_active = False
                        alert.triggered_at = datetime.now()
                        
                        # Prepare notification data
                        condition_text = "aştı" if alert.condition == "above" else "düştü"
                        
                        triggered_alerts.append({
                            "user_id": user_id,
                            "template_id": "price_alert_triggered",
                            "data": {
                                "symbol": alert.symbol,
                                "price": alert.price,
                                "current_price": current_price,
                                "condition_text": condition_text,
                                "time": datetime.now().strftime("%H:%M:%S")
                            }
                        })
            
            return triggered_alerts
            
        except Exception as e:
            logger.error(f"Error checking price alerts: {e}")
            return []
    
    async def set_user_preferences(self, user_id: int, preferences: Dict[str, Any]) -> None:
        """Set notification preferences for user."""
        self.user_preferences[user_id] = preferences
        logger.info(f"Notification preferences updated for user {user_id}")
    
    async def get_user_preferences(self, user_id: int) -> Dict[str, Any]:
        """Get notification preferences for user."""
        return self.user_preferences.get(user_id, {
            "price_alerts": True,
            "trading_signals": True,
            "portfolio_updates": False,
            "market_news": True,
            "system_alerts": True
        })
    
    async def _monitoring_loop(self) -> None:
        """Background monitoring loop for alerts."""
        while True:
            try:
                # Get current market data and check alerts
                await self._check_price_alerts()
                await self._check_portfolio_alerts()
                await self._check_system_alerts()

                # Check every minute
                await asyncio.sleep(60)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(60)

    async def _check_price_alerts(self) -> None:
        """Check price alerts for all users."""
        try:
            from app.services.market_data_service import market_data_service

            # Check all active price alerts
            for user_id, alerts in self.price_alerts.items():
                for alert in alerts:
                    if not alert.is_active:
                        continue

                    try:
                        # Get current price
                        ticker_data = await market_data_service.get_ticker_data(alert.symbol)

                        if not ticker_data or 'price' not in ticker_data:
                            continue

                        current_price = float(ticker_data['price'])

                        # Check if alert should trigger
                        should_trigger = False

                        if alert.condition == "above" and current_price >= alert.target_price:
                            should_trigger = True
                        elif alert.condition == "below" and current_price <= alert.target_price:
                            should_trigger = True
                        elif alert.condition == "equals" and abs(current_price - alert.target_price) / alert.target_price < 0.01:  # 1% tolerance
                            should_trigger = True

                        if should_trigger:
                            # Send alert notification
                            await self._send_price_alert_notification(user_id, alert, current_price)

                            # Deactivate one-time alerts
                            if not alert.recurring:
                                alert.is_active = False

                    except Exception as e:
                        logger.error(f"Error checking alert for {alert.symbol}: {e}")

        except Exception as e:
            logger.error(f"Error checking price alerts: {e}")

    async def _get_active_price_alerts(self) -> List[Dict[str, Any]]:
        """Get active price alerts from database"""
        try:
            from app.core.database import get_db
            from app.models.user import UserNotification
            from sqlalchemy.orm import Session
            import json

            # Get database session
            db_gen = get_db()
            db: Session = next(db_gen)

            try:
                # Get active price alerts
                alerts = db.query(UserNotification).filter(
                    UserNotification.notification_type == "price_alert",
                    UserNotification.is_active == True
                ).all()

                alert_list = []
                for alert in alerts:
                    try:
                        # Parse extra_data for alert details
                        alert_data = json.loads(alert.extra_data) if alert.extra_data else {}

                        alert_list.append({
                            'id': alert.id,
                            'user_id': alert.user_id,
                            'symbol': alert.related_symbol,
                            'target_price': alert_data.get('target_price', 0.0),
                            'condition': alert_data.get('condition', 'above'),
                            'is_active': alert.is_active,
                            'recurring': alert_data.get('recurring', False),
                            'created_at': alert.created_at
                        })
                    except Exception as e:
                        logger.error(f"Error parsing alert data: {e}")
                        continue

                return alert_list

            finally:
                db.close()

        except Exception as e:
            logger.error(f"Error getting price alerts from database: {e}")
            return []

    async def _send_price_alert_notification(self, user_id: int, alert: PriceAlert, current_price: float) -> None:
        """Send price alert notification to user."""
        try:
            # Format price change
            price_change = ((current_price - alert.target_price) / alert.target_price) * 100

            # Create notification message
            message = f"🚨 **Fiyat Alarmı Tetiklendi!**\n\n"
            message += f"**{alert.symbol.upper()}**\n"
            message += f"Hedef Fiyat: ${alert.target_price:.4f}\n"
            message += f"Mevcut Fiyat: ${current_price:.4f}\n"
            message += f"Değişim: {price_change:+.2f}%\n"
            message += f"Koşul: {alert.condition}\n"
            message += f"Oluşturulma: {alert.created_at.strftime('%Y-%m-%d %H:%M')}"

            # Create notification
            notification = Notification(
                user_id=user_id,
                type=NotificationType.PRICE_ALERT,
                title=f"{alert.symbol.upper()} Fiyat Alarmı",
                message=message,
                data={
                    "symbol": alert.symbol,
                    "target_price": alert.target_price,
                    "current_price": current_price,
                    "condition": alert.condition,
                    "price_change": price_change
                }
            )

            # Send notification
            await self.send_notification(notification)

            # Add to history
            self.notification_history.append({
                "timestamp": datetime.now(),
                "user_id": user_id,
                "type": "price_alert",
                "symbol": alert.symbol,
                "target_price": alert.target_price,
                "current_price": current_price
            })

            logger.info(f"Price alert sent to user {user_id} for {alert.symbol}")

        except Exception as e:
            logger.error(f"Error sending price alert notification: {e}")

    async def _check_portfolio_alerts(self) -> None:
        """Check portfolio-related alerts."""
        try:
            from app.services.portfolio_management_service import portfolio_management_service

            # Check portfolio alerts for all users with active alerts
            for user_id in self.price_alerts.keys():
                try:
                    # Get portfolio summary
                    portfolio_summary = await portfolio_management_service.get_portfolio_summary(user_id)

                    if not portfolio_summary:
                        continue

                    # Check for significant daily changes
                    daily_change = portfolio_summary.get('performance', {}).get('day_1', {}).get('percentage', 0.0)

                    # Alert for large daily losses (>5%)
                    if daily_change < -5.0:
                        await self._send_portfolio_alert(
                            user_id,
                            "Büyük Günlük Kayıp",
                            f"Portföyünüz bugün %{abs(daily_change):.2f} değer kaybetti.",
                            "large_loss",
                            {"daily_change": daily_change}
                        )

                    # Alert for large daily gains (>10%)
                    elif daily_change > 10.0:
                        await self._send_portfolio_alert(
                            user_id,
                            "Büyük Günlük Kazanç",
                            f"Portföyünüz bugün %{daily_change:.2f} değer kazandı!",
                            "large_gain",
                            {"daily_change": daily_change}
                        )

                    # Check portfolio value
                    total_value = portfolio_summary.get('total_value_usd', 0.0)

                    # Alert for low portfolio value
                    if total_value < 100.0 and total_value > 0:
                        await self._send_portfolio_alert(
                            user_id,
                            "Düşük Portföy Değeri",
                            f"Portföy değeriniz ${total_value:.2f} seviyesinde. Yatırım yapmayı düşünebilirsiniz.",
                            "low_value",
                            {"total_value": total_value}
                        )

                except Exception as e:
                    logger.error(f"Error checking portfolio alerts for user {user_id}: {e}")

        except Exception as e:
            logger.error(f"Error checking portfolio alerts: {e}")

    async def _send_portfolio_alert(
        self,
        user_id: int,
        title: str,
        message: str,
        alert_type: str,
        data: Dict[str, Any]
    ) -> None:
        """Send portfolio alert notification to user."""
        try:
            # Create notification
            notification = Notification(
                user_id=user_id,
                type=NotificationType.PORTFOLIO_UPDATE,
                title=title,
                message=f"📊 **{title}**\n\n{message}",
                data=data
            )

            # Send notification
            await self.send_notification(notification)

            # Add to history
            self.notification_history.append({
                "timestamp": datetime.now(),
                "user_id": user_id,
                "type": "portfolio_alert",
                "alert_type": alert_type,
                "title": title,
                "data": data
            })

            logger.info(f"Portfolio alert sent to user {user_id}: {title}")

        except Exception as e:
            logger.error(f"Error sending portfolio alert: {e}")

    async def _check_system_alerts(self) -> None:
        """Check system health alerts."""
        try:
            from app.services.telegram_bot.bot_manager import TelegramBotManager

            # Get system status
            bot_manager = TelegramBotManager()
            status = await bot_manager._get_system_status()

            # Check for any red status indicators
            critical_issues = [key for key, value in status.items() if "🔴" in value]

            if critical_issues:
                # Send system alert to admin users (user_id 1 is considered admin)
                admin_user_ids = [1]  # In production, this would come from database

                for admin_id in admin_user_ids:
                    await self._send_system_alert(
                        admin_id,
                        "Sistem Uyarısı",
                        f"Kritik sistem sorunları tespit edildi: {', '.join(critical_issues)}",
                        "critical_issues",
                        {"issues": critical_issues, "status": status}
                    )

                logger.warning(f"Critical system issues detected: {critical_issues}")

        except Exception as e:
            logger.error(f"Error checking system alerts: {e}")

    async def _send_system_alert(
        self,
        user_id: int,
        title: str,
        message: str,
        alert_type: str,
        data: Dict[str, Any]
    ) -> None:
        """Send system alert notification to admin user."""
        try:
            # Create notification
            notification = Notification(
                user_id=user_id,
                type=NotificationType.SYSTEM_ALERT,
                title=title,
                message=f"⚠️ **{title}**\n\n{message}",
                data=data
            )

            # Send notification
            await self.send_notification(notification)

            # Add to history
            self.notification_history.append({
                "timestamp": datetime.now(),
                "user_id": user_id,
                "type": "system_alert",
                "alert_type": alert_type,
                "title": title,
                "data": data
            })

            logger.info(f"System alert sent to admin {user_id}: {title}")

        except Exception as e:
            logger.error(f"Error sending system alert: {e}")

    async def stop(self) -> None:
        """Stop the notification engine."""
        if self._monitoring_task:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Notification engine stopped")
    
    async def get_statistics(self) -> Dict[str, Any]:
        """Get notification statistics."""
        total_alerts = sum(len(alerts) for alerts in self.price_alerts.values())
        active_alerts = sum(
            sum(1 for alert in alerts if alert.is_active)
            for alerts in self.price_alerts.values()
        )
        
        return {
            "total_users_with_alerts": len(self.price_alerts),
            "total_alerts": total_alerts,
            "active_alerts": active_alerts,
            "notifications_sent": len(self.notification_history),
            "templates_available": len(self.templates)
        }

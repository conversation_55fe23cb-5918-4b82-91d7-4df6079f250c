"""
Database initialization and migration utilities
Author: inkbytefo
"""

import logging
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from typing import Optional

from app.core.config import settings
from app.core.database import Base, engine
from app.models import *  # Import all models to register them

logger = logging.getLogger(__name__)


async def create_database_tables():
    """Create all database tables"""
    try:
        logger.info("Creating database tables...")
        
        # Create all tables
        Base.metadata.create_all(bind=engine)
        
        logger.info("Database tables created successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error creating database tables: {e}")
        return False


async def drop_database_tables():
    """Drop all database tables (use with caution!)"""
    try:
        logger.warning("Dropping all database tables...")
        
        # Drop all tables
        Base.metadata.drop_all(bind=engine)
        
        logger.warning("All database tables dropped")
        return True
        
    except Exception as e:
        logger.error(f"Error dropping database tables: {e}")
        return False


async def reset_database():
    """Reset database by dropping and recreating all tables"""
    try:
        logger.warning("Resetting database...")
        
        # Drop all tables
        await drop_database_tables()
        
        # Create all tables
        await create_database_tables()
        
        logger.info("Database reset completed")
        return True
        
    except Exception as e:
        logger.error(f"Error resetting database: {e}")
        return False


async def create_admin_user(
    username: str = "admin",
    email: str = "<EMAIL>",
    password: str = "admin123",
    full_name: str = "System Administrator"
) -> bool:
    """Create initial admin user"""
    try:
        from app.core.database import get_db
        from app.models.user import User
        from app.core.security import get_password_hash
        from sqlalchemy.orm import Session
        
        # Get database session
        db_gen = get_db()
        db: Session = next(db_gen)
        
        try:
            # Check if admin user already exists
            existing_admin = db.query(User).filter(
                (User.username == username) | 
                (User.email == email) |
                (User.is_admin == True)
            ).first()
            
            if existing_admin:
                logger.info("Admin user already exists")
                return True
            
            # Create admin user
            hashed_password = get_password_hash(password)
            
            admin_user = User(
                username=username,
                email=email,
                full_name=full_name,
                hashed_password=hashed_password,
                is_active=True,
                is_verified=True,
                is_admin=True,
                trading_enabled=True,
                risk_level="high",
                max_daily_trades=100,
                max_position_size=1.0
            )
            
            db.add(admin_user)
            db.commit()
            db.refresh(admin_user)
            
            logger.info(f"Admin user created: {username} ({email})")
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Error creating admin user: {e}")
        return False


async def create_default_system_config():
    """Create default system configuration"""
    try:
        from app.core.database import get_db
        from app.models.system import SystemConfig
        from sqlalchemy.orm import Session
        
        # Get database session
        db_gen = get_db()
        db: Session = next(db_gen)
        
        try:
            # Check if config already exists
            existing_config = db.query(SystemConfig).first()
            
            if existing_config:
                logger.info("System configuration already exists")
                return True
            
            # Create default configuration
            default_configs = [
                {
                    "key": "trading_enabled",
                    "value": "true",
                    "description": "Global trading enable/disable switch"
                },
                {
                    "key": "max_concurrent_trades",
                    "value": "10",
                    "description": "Maximum number of concurrent trades"
                },
                {
                    "key": "default_risk_level",
                    "value": "medium",
                    "description": "Default risk level for new users"
                },
                {
                    "key": "maintenance_mode",
                    "value": "false",
                    "description": "System maintenance mode"
                },
                {
                    "key": "api_rate_limit",
                    "value": "100",
                    "description": "API requests per minute limit"
                },
                {
                    "key": "notification_enabled",
                    "value": "true",
                    "description": "Global notification enable/disable"
                }
            ]
            
            for config_data in default_configs:
                config = SystemConfig(
                    key=config_data["key"],
                    value=config_data["value"],
                    description=config_data["description"],
                    is_active=True
                )
                db.add(config)
            
            db.commit()
            
            logger.info("Default system configuration created")
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Error creating default system config: {e}")
        return False


async def create_default_exchanges():
    """Create default exchange configurations"""
    try:
        from app.core.database import get_db
        from app.models.system import Exchange
        from sqlalchemy.orm import Session
        
        # Get database session
        db_gen = get_db()
        db: Session = next(db_gen)
        
        try:
            # Check if exchanges already exist
            existing_exchanges = db.query(Exchange).count()
            
            if existing_exchanges > 0:
                logger.info("Exchanges already configured")
                return True
            
            # Create default exchanges
            default_exchanges = [
                {
                    "name": "binance",
                    "display_name": "Binance",
                    "api_url": "https://api.binance.com",
                    "testnet_url": "https://testnet.binance.vision",
                    "is_active": True,
                    "supports_spot": True,
                    "supports_futures": True,
                    "supports_margin": True
                },
                {
                    "name": "coinbase",
                    "display_name": "Coinbase Pro",
                    "api_url": "https://api.pro.coinbase.com",
                    "testnet_url": "https://api-public.sandbox.pro.coinbase.com",
                    "is_active": True,
                    "supports_spot": True,
                    "supports_futures": False,
                    "supports_margin": False
                },
                {
                    "name": "kraken",
                    "display_name": "Kraken",
                    "api_url": "https://api.kraken.com",
                    "testnet_url": "https://api.kraken.com",
                    "is_active": True,
                    "supports_spot": True,
                    "supports_futures": True,
                    "supports_margin": True
                }
            ]
            
            for exchange_data in default_exchanges:
                exchange = Exchange(
                    name=exchange_data["name"],
                    display_name=exchange_data["display_name"],
                    api_url=exchange_data["api_url"],
                    testnet_url=exchange_data["testnet_url"],
                    is_active=exchange_data["is_active"],
                    supports_spot=exchange_data["supports_spot"],
                    supports_futures=exchange_data["supports_futures"],
                    supports_margin=exchange_data["supports_margin"]
                )
                db.add(exchange)
            
            db.commit()
            
            logger.info("Default exchanges created")
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Error creating default exchanges: {e}")
        return False


async def initialize_database():
    """Initialize database with tables and default data"""
    try:
        logger.info("Initializing database...")
        
        # Create tables
        if not await create_database_tables():
            return False
        
        # Create admin user
        if not await create_admin_user():
            return False
        
        # Create default system config
        if not await create_default_system_config():
            return False
        
        # Create default exchanges
        if not await create_default_exchanges():
            return False
        
        logger.info("Database initialization completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error initializing database: {e}")
        return False


async def check_database_connection():
    """Check if database connection is working"""
    try:
        from app.core.database import get_db
        from sqlalchemy.orm import Session
        
        # Get database session
        db_gen = get_db()
        db: Session = next(db_gen)
        
        try:
            # Execute simple query
            result = db.execute(text("SELECT 1"))
            result.fetchone()
            
            logger.info("Database connection successful")
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        return False


if __name__ == "__main__":
    import asyncio
    
    async def main():
        """Main initialization function"""
        print("Starting database initialization...")
        
        # Check connection
        if not await check_database_connection():
            print("❌ Database connection failed")
            return
        
        # Initialize database
        if await initialize_database():
            print("✅ Database initialization completed successfully")
        else:
            print("❌ Database initialization failed")
    
    asyncio.run(main())

"""
Market Regime Detection Service
Detects bull/bear/sideways market regimes and adjusts trading strategy accordingly
Author: inkbytefo
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
from enum import Enum
from dataclasses import dataclass

from app.services.market_data_service import market_data_service
from app.core.logging_config import performance_monitor

logger = logging.getLogger(__name__)

class MarketRegime(Enum):
    """Market regime types"""
    BULL = "bull"           # Strong uptrend
    BEAR = "bear"           # Strong downtrend  
    SIDEWAYS = "sideways"   # Range-bound/consolidation
    VOLATILE = "volatile"   # High volatility, unclear direction
    UNKNOWN = "unknown"     # Insufficient data

@dataclass
class RegimeSignal:
    """Market regime detection result"""
    regime: MarketRegime
    confidence: float  # 0.0 to 1.0
    strength: float    # How strong the regime is (0.0 to 1.0)
    duration_days: int # How long this regime has been active
    
    # Supporting indicators
    trend_direction: str  # "up", "down", "flat"
    volatility_level: str # "low", "medium", "high"
    momentum_strength: float
    
    # Regime-specific metrics
    ma_slope: float           # 200-day MA slope
    price_vs_ma: float        # Current price vs 200-day MA (percentage)
    adx_value: float          # Average Directional Index
    volatility_percentile: float  # Current volatility vs historical
    
    # Recommendations
    recommended_adjustments: Dict[str, float]
    reasoning: str

class MarketRegimeDetector:
    """Service for detecting market regimes"""
    
    def __init__(self):
        self.lookback_periods = {
            "short": 20,    # 20 periods for short-term analysis
            "medium": 50,   # 50 periods for medium-term
            "long": 200     # 200 periods for long-term trend
        }
    
    @performance_monitor("market_regime_detection")
    async def detect_regime(
        self,
        symbol: str,
        timeframe: str = "1d",
        exchange: str = "binance"
    ) -> RegimeSignal:
        """
        Detect current market regime for a symbol
        
        Args:
            symbol: Trading symbol (e.g., "BTC/USDT")
            timeframe: Data timeframe (default: "1d" for daily)
            exchange: Exchange name
            
        Returns:
            RegimeSignal with detected regime and recommendations
        """
        
        try:
            # Get historical data (need enough for 200-day MA)
            data = await market_data_service.get_ohlcv_data(
                symbol=symbol,
                timeframe=timeframe,
                limit=250,  # Extra buffer for calculations
                exchange=exchange
            )
            
            if not data or len(data) < 200:
                logger.warning(f"Insufficient data for regime detection: {symbol}")
                return self._create_unknown_regime(symbol)
            
            # Convert to DataFrame for analysis
            df = pd.DataFrame(data)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df.set_index('timestamp', inplace=True)
            
            # Calculate technical indicators
            indicators = self._calculate_regime_indicators(df)
            
            # Detect regime based on multiple factors
            regime_analysis = self._analyze_regime(indicators, df)
            
            # Generate recommendations
            recommendations = self._generate_regime_recommendations(regime_analysis)
            
            # Create regime signal
            regime_signal = RegimeSignal(
                regime=regime_analysis['regime'],
                confidence=regime_analysis['confidence'],
                strength=regime_analysis['strength'],
                duration_days=regime_analysis['duration_days'],
                trend_direction=regime_analysis['trend_direction'],
                volatility_level=regime_analysis['volatility_level'],
                momentum_strength=regime_analysis['momentum_strength'],
                ma_slope=indicators['ma_slope'],
                price_vs_ma=indicators['price_vs_ma'],
                adx_value=indicators['adx'],
                volatility_percentile=indicators['volatility_percentile'],
                recommended_adjustments=recommendations,
                reasoning=regime_analysis['reasoning']
            )
            
            logger.info(f"Regime detected for {symbol}: {regime_signal.regime.value} "
                       f"(confidence: {regime_signal.confidence:.2f})")
            
            return regime_signal
            
        except Exception as e:
            logger.error(f"Error detecting market regime for {symbol}: {e}")
            return self._create_unknown_regime(symbol, str(e))
    
    def _calculate_regime_indicators(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate technical indicators for regime detection"""
        
        indicators = {}
        
        # Moving averages
        df['ma_20'] = df['close'].rolling(window=20).mean()
        df['ma_50'] = df['close'].rolling(window=50).mean()
        df['ma_200'] = df['close'].rolling(window=200).mean()
        
        # Current price vs 200-day MA
        current_price = df['close'].iloc[-1]
        ma_200_current = df['ma_200'].iloc[-1]
        indicators['price_vs_ma'] = ((current_price - ma_200_current) / ma_200_current) * 100
        
        # 200-day MA slope (trend strength)
        ma_200_slope = (df['ma_200'].iloc[-1] - df['ma_200'].iloc[-20]) / 20
        indicators['ma_slope'] = ma_200_slope / df['ma_200'].iloc[-1] * 100  # Percentage slope
        
        # Average Directional Index (ADX) for trend strength
        indicators['adx'] = self._calculate_adx(df)
        
        # Volatility analysis
        df['returns'] = df['close'].pct_change()
        current_volatility = df['returns'].rolling(window=20).std().iloc[-1]
        historical_volatility = df['returns'].rolling(window=20).std()
        indicators['volatility_percentile'] = (
            (historical_volatility <= current_volatility).sum() / len(historical_volatility) * 100
        )
        
        # Momentum indicators
        indicators['rsi'] = self._calculate_rsi(df['close'])
        indicators['momentum'] = ((current_price - df['close'].iloc[-20]) / df['close'].iloc[-20]) * 100
        
        # Support/Resistance levels
        indicators['near_resistance'] = self._check_resistance_level(df)
        indicators['near_support'] = self._check_support_level(df)
        
        return indicators
    
    def _analyze_regime(self, indicators: Dict[str, float], df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze indicators to determine market regime"""
        
        # Initialize scores for each regime
        bull_score = 0
        bear_score = 0
        sideways_score = 0
        volatile_score = 0
        
        reasoning_parts = []
        
        # 1. Trend Analysis (200-day MA)
        if indicators['price_vs_ma'] > 5:  # Price 5% above 200-day MA
            bull_score += 3
            reasoning_parts.append(f"Price {indicators['price_vs_ma']:.1f}% above 200-day MA")
        elif indicators['price_vs_ma'] < -5:  # Price 5% below 200-day MA
            bear_score += 3
            reasoning_parts.append(f"Price {indicators['price_vs_ma']:.1f}% below 200-day MA")
        else:
            sideways_score += 2
            reasoning_parts.append("Price near 200-day MA")
        
        # 2. MA Slope Analysis
        if indicators['ma_slope'] > 0.1:  # Strong upward slope
            bull_score += 2
            reasoning_parts.append("200-day MA trending up")
        elif indicators['ma_slope'] < -0.1:  # Strong downward slope
            bear_score += 2
            reasoning_parts.append("200-day MA trending down")
        else:
            sideways_score += 1
            reasoning_parts.append("200-day MA flat")
        
        # 3. ADX Analysis (trend strength)
        if indicators['adx'] > 25:  # Strong trend
            if indicators['price_vs_ma'] > 0:
                bull_score += 2
            else:
                bear_score += 2
            reasoning_parts.append(f"Strong trend (ADX: {indicators['adx']:.1f})")
        elif indicators['adx'] < 20:  # Weak trend
            sideways_score += 2
            reasoning_parts.append(f"Weak trend (ADX: {indicators['adx']:.1f})")
        
        # 4. Volatility Analysis
        if indicators['volatility_percentile'] > 80:  # High volatility
            volatile_score += 2
            reasoning_parts.append("High volatility environment")
        elif indicators['volatility_percentile'] < 20:  # Low volatility
            sideways_score += 1
            reasoning_parts.append("Low volatility environment")
        
        # 5. Momentum Analysis
        if indicators['momentum'] > 10:  # Strong positive momentum
            bull_score += 1
        elif indicators['momentum'] < -10:  # Strong negative momentum
            bear_score += 1
        
        # 6. RSI Analysis
        if indicators['rsi'] > 70:  # Overbought
            bear_score += 1
            reasoning_parts.append("Overbought conditions")
        elif indicators['rsi'] < 30:  # Oversold
            bull_score += 1
            reasoning_parts.append("Oversold conditions")
        
        # Determine regime based on scores
        scores = {
            'bull': bull_score,
            'bear': bear_score,
            'sideways': sideways_score,
            'volatile': volatile_score
        }
        
        max_score = max(scores.values())
        if max_score == 0:
            regime = MarketRegime.UNKNOWN
            confidence = 0.0
        else:
            regime_name = max(scores, key=scores.get)
            regime = MarketRegime(regime_name)
            confidence = min(max_score / 8.0, 1.0)  # Normalize to 0-1
        
        # Calculate additional metrics
        strength = min(max_score / 6.0, 1.0)  # Regime strength
        
        # Estimate regime duration (simplified)
        duration_days = self._estimate_regime_duration(df, indicators)
        
        # Determine trend direction and volatility level
        if indicators['ma_slope'] > 0.05:
            trend_direction = "up"
        elif indicators['ma_slope'] < -0.05:
            trend_direction = "down"
        else:
            trend_direction = "flat"
        
        if indicators['volatility_percentile'] > 70:
            volatility_level = "high"
        elif indicators['volatility_percentile'] < 30:
            volatility_level = "low"
        else:
            volatility_level = "medium"
        
        return {
            'regime': regime,
            'confidence': confidence,
            'strength': strength,
            'duration_days': duration_days,
            'trend_direction': trend_direction,
            'volatility_level': volatility_level,
            'momentum_strength': abs(indicators['momentum']) / 20.0,  # Normalize
            'reasoning': "; ".join(reasoning_parts),
            'scores': scores
        }
    
    def _calculate_adx(self, df: pd.DataFrame, period: int = 14) -> float:
        """Calculate Average Directional Index"""
        try:
            high = df['high']
            low = df['low']
            close = df['close']
            
            # True Range
            tr1 = high - low
            tr2 = abs(high - close.shift())
            tr3 = abs(low - close.shift())
            tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            
            # Directional Movement
            dm_plus = np.where((high - high.shift()) > (low.shift() - low), 
                              np.maximum(high - high.shift(), 0), 0)
            dm_minus = np.where((low.shift() - low) > (high - high.shift()), 
                               np.maximum(low.shift() - low, 0), 0)
            
            # Smoothed values
            tr_smooth = pd.Series(tr).rolling(window=period).mean()
            dm_plus_smooth = pd.Series(dm_plus).rolling(window=period).mean()
            dm_minus_smooth = pd.Series(dm_minus).rolling(window=period).mean()
            
            # Directional Indicators
            di_plus = (dm_plus_smooth / tr_smooth) * 100
            di_minus = (dm_minus_smooth / tr_smooth) * 100
            
            # ADX
            dx = abs(di_plus - di_minus) / (di_plus + di_minus) * 100
            adx = dx.rolling(window=period).mean().iloc[-1]
            
            return adx if not pd.isna(adx) else 20.0
            
        except Exception:
            return 20.0  # Default neutral value
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> float:
        """Calculate RSI"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi.iloc[-1] if not pd.isna(rsi.iloc[-1]) else 50.0
        except Exception:
            return 50.0  # Default neutral value
    
    def _check_resistance_level(self, df: pd.DataFrame) -> bool:
        """Check if price is near resistance level"""
        try:
            recent_highs = df['high'].rolling(window=20).max()
            current_price = df['close'].iloc[-1]
            resistance = recent_highs.iloc[-1]
            return (current_price / resistance) > 0.98
        except Exception:
            return False
    
    def _check_support_level(self, df: pd.DataFrame) -> bool:
        """Check if price is near support level"""
        try:
            recent_lows = df['low'].rolling(window=20).min()
            current_price = df['close'].iloc[-1]
            support = recent_lows.iloc[-1]
            return (current_price / support) < 1.02
        except Exception:
            return False
    
    def _estimate_regime_duration(self, df: pd.DataFrame, indicators: Dict[str, float]) -> int:
        """Estimate how long the current regime has been active"""
        try:
            # Simple estimation based on MA crossovers
            ma_50 = df['close'].rolling(window=50).mean()
            ma_200 = df['close'].rolling(window=200).mean()
            
            # Find last crossover
            crossover_signals = (ma_50 > ma_200).astype(int).diff()
            last_crossover_idx = crossover_signals[crossover_signals != 0].index[-1] if len(crossover_signals[crossover_signals != 0]) > 0 else df.index[0]
            
            duration = len(df.loc[last_crossover_idx:])
            return min(duration, 365)  # Cap at 1 year
            
        except Exception:
            return 30  # Default 30 days
    
    def _generate_regime_recommendations(self, regime_analysis: Dict[str, Any]) -> Dict[str, float]:
        """Generate trading strategy adjustments based on regime"""
        
        regime = regime_analysis['regime']
        confidence = regime_analysis['confidence']
        
        # Base adjustments
        adjustments = {
            "signal_weight_multiplier": 1.0,
            "long_bias": 0.0,  # -1.0 to 1.0 (negative = short bias)
            "position_size_multiplier": 1.0,
            "stop_loss_multiplier": 1.0,
            "take_profit_multiplier": 1.0
        }
        
        if regime == MarketRegime.BULL:
            # Bull market: favor long positions, reduce stop losses
            adjustments["long_bias"] = 0.3 * confidence
            adjustments["position_size_multiplier"] = 1.0 + (0.2 * confidence)
            adjustments["stop_loss_multiplier"] = 0.8  # Tighter stops
            adjustments["take_profit_multiplier"] = 1.2  # Higher targets
            
        elif regime == MarketRegime.BEAR:
            # Bear market: favor short positions, tighter risk management
            adjustments["long_bias"] = -0.3 * confidence
            adjustments["position_size_multiplier"] = 0.8  # Smaller positions
            adjustments["stop_loss_multiplier"] = 0.7  # Much tighter stops
            adjustments["take_profit_multiplier"] = 0.9  # Lower targets
            
        elif regime == MarketRegime.SIDEWAYS:
            # Sideways market: reduce position sizes, favor mean reversion
            adjustments["position_size_multiplier"] = 0.7
            adjustments["stop_loss_multiplier"] = 1.1  # Slightly wider stops
            adjustments["take_profit_multiplier"] = 0.8  # Quick profits
            
        elif regime == MarketRegime.VOLATILE:
            # Volatile market: reduce exposure, tighter risk management
            adjustments["signal_weight_multiplier"] = 0.6
            adjustments["position_size_multiplier"] = 0.5  # Much smaller positions
            adjustments["stop_loss_multiplier"] = 0.6  # Very tight stops
            adjustments["take_profit_multiplier"] = 0.7  # Quick exits
        
        return adjustments
    
    def _create_unknown_regime(self, symbol: str, error: str = "") -> RegimeSignal:
        """Create unknown regime signal for error cases"""
        return RegimeSignal(
            regime=MarketRegime.UNKNOWN,
            confidence=0.0,
            strength=0.0,
            duration_days=0,
            trend_direction="unknown",
            volatility_level="unknown",
            momentum_strength=0.0,
            ma_slope=0.0,
            price_vs_ma=0.0,
            adx_value=0.0,
            volatility_percentile=50.0,
            recommended_adjustments={
                "signal_weight_multiplier": 1.0,
                "long_bias": 0.0,
                "position_size_multiplier": 1.0,
                "stop_loss_multiplier": 1.0,
                "take_profit_multiplier": 1.0
            },
            reasoning=f"Unable to detect regime for {symbol}" + (f": {error}" if error else "")
        )

# Global instance
market_regime_detector = MarketRegimeDetector()

"""
Pattern Detection API Endpoints
Author: inkbytefo
"""

from fastapi import APIRouter, HTTPException, Query, BackgroundTasks
from typing import List, Dict, Optional, Any
import logging
from datetime import datetime

from app.services.pattern_detection_service import pattern_detection_service
from app.core.config import settings

router = APIRouter()
logger = logging.getLogger("pattern_detection_api")


@router.get("/analyze/{symbol}")
async def analyze_patterns(
    symbol: str,
    timeframe: str = Query("1h", description="Timeframe (1m, 5m, 15m, 1h, 4h, 1d)"),
    exchange: str = Query("binance", description="Exchange name"),
    limit: int = Query(500, description="Number of data points to analyze")
):
    """Get comprehensive pattern analysis for a symbol"""
    try:
        logger.info(f"Pattern analysis requested for {symbol} ({timeframe})")
        
        # Validate symbol format
        if '/' not in symbol:
            symbol = f"{symbol}/USDT"
        
        # Perform pattern analysis
        result = await pattern_detection_service.analyze_patterns(
            symbol=symbol,
            timeframe=timeframe,
            limit=limit,
            exchange=exchange
        )
        
        if not result:
            raise HTTPException(
                status_code=404, 
                detail=f"Unable to perform pattern analysis for {symbol}"
            )
        
        # Format response
        response = {
            "symbol": result.symbol,
            "timeframe": result.timeframe,
            "exchange": exchange,
            "analysis_time": result.analysis_time.isoformat(),
            "support_resistance": {
                "support_levels": [
                    {
                        "price": level.price,
                        "strength": round(level.strength, 3),
                        "confidence": round(level.confidence, 3),
                        "touch_count": level.touch_count,
                        "first_touch": level.first_touch.isoformat(),
                        "last_touch": level.last_touch.isoformat()
                    }
                    for level in result.support_levels
                ],
                "resistance_levels": [
                    {
                        "price": level.price,
                        "strength": round(level.strength, 3),
                        "confidence": round(level.confidence, 3),
                        "touch_count": level.touch_count,
                        "first_touch": level.first_touch.isoformat(),
                        "last_touch": level.last_touch.isoformat()
                    }
                    for level in result.resistance_levels
                ]
            },
            "trend_lines": [
                {
                    "line_type": tl.line_type,
                    "start_price": tl.start_price,
                    "end_price": tl.end_price,
                    "start_time": tl.start_time.isoformat(),
                    "end_time": tl.end_time.isoformat(),
                    "slope": round(tl.slope, 6),
                    "r_squared": round(tl.r_squared, 3),
                    "touch_points": len(tl.touch_points),
                    "is_broken": tl.is_broken
                }
                for tl in result.trend_lines
            ],
            "chart_patterns": [
                {
                    "pattern_name": pattern.pattern_name,
                    "pattern_type": pattern.pattern_type,
                    "confidence": round(pattern.confidence, 3),
                    "breakout_direction": pattern.breakout_direction,
                    "target_price": pattern.target_price,
                    "stop_loss": pattern.stop_loss,
                    "start_time": pattern.start_time.isoformat(),
                    "end_time": pattern.end_time.isoformat(),
                    "completion_percentage": round(pattern.completion_percentage, 2),
                    "description": pattern.description,
                    "key_points_count": len(pattern.key_points)
                }
                for pattern in result.chart_patterns
            ],
            "fibonacci_levels": [
                {
                    "level": fib.level,
                    "price": fib.price,
                    "level_type": fib.level_type,
                    "is_active": fib.is_active,
                    "touches": fib.touches
                }
                for fib in result.fibonacci_levels
            ],
            "overall_analysis": {
                "trend": result.overall_trend,
                "trend_strength": round(result.trend_strength, 3),
                "pattern_reliability": round(result.pattern_reliability, 3),
                "next_key_levels": result.next_key_levels
            },
            "status": "success"
        }
        
        return response
        
    except Exception as e:
        logger.error(f"Error in pattern analysis for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/support-resistance/{symbol}")
async def get_support_resistance(
    symbol: str,
    timeframe: str = Query("1h", description="Timeframe"),
    exchange: str = Query("binance", description="Exchange name"),
    limit: int = Query(300, description="Number of data points")
):
    """Get support and resistance levels for a symbol"""
    try:
        logger.info(f"Support/Resistance analysis requested for {symbol}")
        
        # Validate symbol format
        if '/' not in symbol:
            symbol = f"{symbol}/USDT"
        
        # Get pattern analysis
        result = await pattern_detection_service.analyze_patterns(
            symbol=symbol,
            timeframe=timeframe,
            limit=limit,
            exchange=exchange
        )
        
        if not result:
            raise HTTPException(
                status_code=404,
                detail=f"Unable to analyze support/resistance for {symbol}"
            )
        
        # Get current price for context
        from app.services.market_data_service import market_data_service
        current_price = await market_data_service.get_current_price(symbol, exchange)
        
        # Categorize levels relative to current price
        levels_below = []
        levels_above = []
        
        for level in result.support_levels:
            level_data = {
                "price": level.price,
                "type": "support",
                "strength": round(level.strength, 3),
                "confidence": round(level.confidence, 3),
                "touch_count": level.touch_count,
                "distance_percentage": round(((level.price - current_price) / current_price) * 100, 2),
                "last_touch": level.last_touch.isoformat()
            }
            
            if level.price < current_price:
                levels_below.append(level_data)
            else:
                levels_above.append(level_data)
        
        for level in result.resistance_levels:
            level_data = {
                "price": level.price,
                "type": "resistance",
                "strength": round(level.strength, 3),
                "confidence": round(level.confidence, 3),
                "touch_count": level.touch_count,
                "distance_percentage": round(((level.price - current_price) / current_price) * 100, 2),
                "last_touch": level.last_touch.isoformat()
            }
            
            if level.price > current_price:
                levels_above.append(level_data)
            else:
                levels_below.append(level_data)
        
        # Sort by distance from current price
        levels_below.sort(key=lambda x: x['distance_percentage'], reverse=True)  # Closest first
        levels_above.sort(key=lambda x: x['distance_percentage'])  # Closest first
        
        return {
            "symbol": symbol,
            "timeframe": timeframe,
            "current_price": current_price,
            "levels_below": levels_below[:5],  # 5 closest below
            "levels_above": levels_above[:5],   # 5 closest above
            "analysis_time": result.analysis_time.isoformat(),
            "status": "success"
        }
        
    except Exception as e:
        logger.error(f"Error getting support/resistance for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/trend-lines/{symbol}")
async def get_trend_lines(
    symbol: str,
    timeframe: str = Query("1h", description="Timeframe"),
    exchange: str = Query("binance", description="Exchange name"),
    min_r_squared: float = Query(0.7, description="Minimum R-squared for trend line quality")
):
    """Get trend lines for a symbol"""
    try:
        logger.info(f"Trend line analysis requested for {symbol}")
        
        # Validate symbol format
        if '/' not in symbol:
            symbol = f"{symbol}/USDT"
        
        # Get pattern analysis
        result = await pattern_detection_service.analyze_patterns(
            symbol=symbol,
            timeframe=timeframe,
            exchange=exchange
        )
        
        if not result:
            raise HTTPException(
                status_code=404,
                detail=f"Unable to analyze trend lines for {symbol}"
            )
        
        # Filter trend lines by quality
        quality_trend_lines = [
            tl for tl in result.trend_lines 
            if tl.r_squared >= min_r_squared
        ]
        
        # Format trend lines
        formatted_trend_lines = []
        for tl in quality_trend_lines:
            # Calculate current trend line value
            time_diff = (datetime.now() - tl.start_time).total_seconds() / 3600  # hours
            current_trend_value = tl.start_price + (tl.slope * time_diff)
            
            formatted_trend_lines.append({
                "line_type": tl.line_type,
                "start_price": tl.start_price,
                "end_price": tl.end_price,
                "current_value": current_trend_value,
                "start_time": tl.start_time.isoformat(),
                "end_time": tl.end_time.isoformat(),
                "slope": round(tl.slope, 6),
                "slope_percentage": round((tl.slope / tl.start_price) * 100, 4),
                "r_squared": round(tl.r_squared, 3),
                "quality": "excellent" if tl.r_squared > 0.9 else "good" if tl.r_squared > 0.8 else "fair",
                "touch_points": len(tl.touch_points),
                "is_broken": tl.is_broken,
                "break_time": tl.break_time.isoformat() if tl.break_time else None
            })
        
        # Separate support and resistance trend lines
        support_trendlines = [tl for tl in formatted_trend_lines if tl["line_type"] == "support_trendline"]
        resistance_trendlines = [tl for tl in formatted_trend_lines if tl["line_type"] == "resistance_trendline"]
        
        return {
            "symbol": symbol,
            "timeframe": timeframe,
            "support_trendlines": support_trendlines,
            "resistance_trendlines": resistance_trendlines,
            "total_trend_lines": len(formatted_trend_lines),
            "overall_trend": result.overall_trend,
            "trend_strength": round(result.trend_strength, 3),
            "analysis_time": result.analysis_time.isoformat(),
            "status": "success"
        }
        
    except Exception as e:
        logger.error(f"Error getting trend lines for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/chart-patterns/{symbol}")
async def get_chart_patterns(
    symbol: str,
    timeframe: str = Query("1h", description="Timeframe"),
    exchange: str = Query("binance", description="Exchange name"),
    min_confidence: float = Query(0.6, description="Minimum confidence for patterns")
):
    """Get chart patterns for a symbol"""
    try:
        logger.info(f"Chart pattern analysis requested for {symbol}")
        
        # Validate symbol format
        if '/' not in symbol:
            symbol = f"{symbol}/USDT"
        
        # Get pattern analysis
        result = await pattern_detection_service.analyze_patterns(
            symbol=symbol,
            timeframe=timeframe,
            exchange=exchange
        )
        
        if not result:
            raise HTTPException(
                status_code=404,
                detail=f"Unable to analyze chart patterns for {symbol}"
            )
        
        # Filter patterns by confidence
        quality_patterns = [
            pattern for pattern in result.chart_patterns 
            if pattern.confidence >= min_confidence
        ]
        
        # Categorize patterns
        reversal_patterns = []
        continuation_patterns = []
        
        for pattern in quality_patterns:
            pattern_data = {
                "pattern_name": pattern.pattern_name,
                "confidence": round(pattern.confidence, 3),
                "breakout_direction": pattern.breakout_direction,
                "target_price": pattern.target_price,
                "stop_loss": pattern.stop_loss,
                "start_time": pattern.start_time.isoformat(),
                "end_time": pattern.end_time.isoformat(),
                "completion_percentage": round(pattern.completion_percentage, 2),
                "description": pattern.description,
                "key_points_count": len(pattern.key_points),
                "time_span_hours": round((pattern.end_time - pattern.start_time).total_seconds() / 3600, 1)
            }
            
            # Calculate potential profit/loss
            if pattern.target_price and pattern.stop_loss:
                from app.services.market_data_service import market_data_service
                current_price = await market_data_service.get_current_price(symbol, exchange)
                
                if pattern.breakout_direction == "bullish":
                    potential_profit = ((pattern.target_price - current_price) / current_price) * 100
                    potential_loss = ((current_price - pattern.stop_loss) / current_price) * 100
                else:
                    potential_profit = ((current_price - pattern.target_price) / current_price) * 100
                    potential_loss = ((pattern.stop_loss - current_price) / current_price) * 100
                
                pattern_data["potential_profit_percentage"] = round(potential_profit, 2)
                pattern_data["potential_loss_percentage"] = round(potential_loss, 2)
                pattern_data["risk_reward_ratio"] = round(potential_profit / potential_loss, 2) if potential_loss > 0 else None
            
            if pattern.pattern_type == "reversal":
                reversal_patterns.append(pattern_data)
            else:
                continuation_patterns.append(pattern_data)
        
        # Sort by confidence
        reversal_patterns.sort(key=lambda x: x['confidence'], reverse=True)
        continuation_patterns.sort(key=lambda x: x['confidence'], reverse=True)
        
        return {
            "symbol": symbol,
            "timeframe": timeframe,
            "reversal_patterns": reversal_patterns,
            "continuation_patterns": continuation_patterns,
            "total_patterns": len(quality_patterns),
            "pattern_summary": {
                "reversal_count": len(reversal_patterns),
                "continuation_count": len(continuation_patterns),
                "bullish_patterns": len([p for p in quality_patterns if p.breakout_direction == "bullish"]),
                "bearish_patterns": len([p for p in quality_patterns if p.breakout_direction == "bearish"])
            },
            "analysis_time": result.analysis_time.isoformat(),
            "status": "success"
        }
        
    except Exception as e:
        logger.error(f"Error getting chart patterns for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/fibonacci/{symbol}")
async def get_fibonacci_levels(
    symbol: str,
    timeframe: str = Query("1h", description="Timeframe"),
    exchange: str = Query("binance", description="Exchange name")
):
    """Get Fibonacci retracement and extension levels for a symbol"""
    try:
        logger.info(f"Fibonacci analysis requested for {symbol}")
        
        # Validate symbol format
        if '/' not in symbol:
            symbol = f"{symbol}/USDT"
        
        # Get pattern analysis
        result = await pattern_detection_service.analyze_patterns(
            symbol=symbol,
            timeframe=timeframe,
            exchange=exchange
        )
        
        if not result:
            raise HTTPException(
                status_code=404,
                detail=f"Unable to analyze Fibonacci levels for {symbol}"
            )
        
        # Get current price for context
        from app.services.market_data_service import market_data_service
        current_price = await market_data_service.get_current_price(symbol, exchange)
        
        # Separate retracement and extension levels
        retracement_levels = []
        extension_levels = []
        
        for fib in result.fibonacci_levels:
            fib_data = {
                "level": fib.level,
                "price": fib.price,
                "is_active": fib.is_active,
                "touches": fib.touches,
                "distance_percentage": round(((fib.price - current_price) / current_price) * 100, 2),
                "above_current": fib.price > current_price
            }
            
            if fib.level_type == "retracement":
                retracement_levels.append(fib_data)
            else:
                extension_levels.append(fib_data)
        
        # Sort by level
        retracement_levels.sort(key=lambda x: x['level'])
        extension_levels.sort(key=lambda x: x['level'])
        
        # Find most relevant levels
        active_levels = [fib for fib in result.fibonacci_levels if fib.is_active or fib.touches > 0]
        
        return {
            "symbol": symbol,
            "timeframe": timeframe,
            "current_price": current_price,
            "retracement_levels": retracement_levels,
            "extension_levels": extension_levels,
            "active_levels_count": len(active_levels),
            "key_levels": [
                {
                    "level": fib.level,
                    "price": fib.price,
                    "level_type": fib.level_type,
                    "significance": "high" if fib.touches >= 3 else "medium" if fib.touches >= 1 else "low"
                }
                for fib in active_levels
            ],
            "analysis_time": result.analysis_time.isoformat(),
            "status": "success"
        }
        
    except Exception as e:
        logger.error(f"Error getting Fibonacci levels for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/key-levels/{symbol}")
async def get_key_levels(
    symbol: str,
    timeframe: str = Query("1h", description="Timeframe"),
    exchange: str = Query("binance", description="Exchange name")
):
    """Get all key levels (support, resistance, Fibonacci) for a symbol"""
    try:
        logger.info(f"Key levels analysis requested for {symbol}")
        
        # Validate symbol format
        if '/' not in symbol:
            symbol = f"{symbol}/USDT"
        
        # Get pattern analysis
        result = await pattern_detection_service.analyze_patterns(
            symbol=symbol,
            timeframe=timeframe,
            exchange=exchange
        )
        
        if not result:
            raise HTTPException(
                status_code=404,
                detail=f"Unable to analyze key levels for {symbol}"
            )
        
        # Get current price
        from app.services.market_data_service import market_data_service
        current_price = await market_data_service.get_current_price(symbol, exchange)
        
        # Combine all levels
        all_levels = []
        
        # Add support levels
        for level in result.support_levels:
            all_levels.append({
                "price": level.price,
                "type": "support",
                "strength": round(level.strength, 3),
                "confidence": round(level.confidence, 3),
                "source": "support_resistance",
                "touch_count": level.touch_count,
                "distance_percentage": round(((level.price - current_price) / current_price) * 100, 2)
            })
        
        # Add resistance levels
        for level in result.resistance_levels:
            all_levels.append({
                "price": level.price,
                "type": "resistance",
                "strength": round(level.strength, 3),
                "confidence": round(level.confidence, 3),
                "source": "support_resistance",
                "touch_count": level.touch_count,
                "distance_percentage": round(((level.price - current_price) / current_price) * 100, 2)
            })
        
        # Add significant Fibonacci levels
        for fib in result.fibonacci_levels:
            if fib.is_active or fib.touches > 0:
                all_levels.append({
                    "price": fib.price,
                    "type": f"fibonacci_{fib.level_type}",
                    "strength": min(1.0, fib.touches / 5.0),
                    "confidence": 0.8 if fib.is_active else 0.6,
                    "source": "fibonacci",
                    "level": fib.level,
                    "touches": fib.touches,
                    "distance_percentage": round(((fib.price - current_price) / current_price) * 100, 2)
                })
        
        # Sort by distance from current price
        all_levels.sort(key=lambda x: abs(x['distance_percentage']))
        
        # Separate levels above and below current price
        levels_below = [level for level in all_levels if level['price'] < current_price][:5]
        levels_above = [level for level in all_levels if level['price'] > current_price][:5]
        
        return {
            "symbol": symbol,
            "timeframe": timeframe,
            "current_price": current_price,
            "next_key_levels": result.next_key_levels,
            "levels_below": levels_below,
            "levels_above": levels_above,
            "total_levels": len(all_levels),
            "overall_trend": result.overall_trend,
            "pattern_reliability": round(result.pattern_reliability, 3),
            "analysis_time": result.analysis_time.isoformat(),
            "status": "success"
        }
        
    except Exception as e:
        logger.error(f"Error getting key levels for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

"""
User Manager for Telegram Bot
Author: inkbytefo

Manages user authentication, authorization, and user data for the Telegram bot.
"""

import logging
from typing import Dict, Set, Optional, List
from datetime import datetime, timedelta
import json

from app.core.config import settings

logger = logging.getLogger(__name__)


class UserManager:
    """
    Manages Telegram bot users, authentication, and authorization.
    """
    
    def __init__(self):
        self.authorized_users: Set[int] = set()
        self.user_data: Dict[int, Dict] = {}
        self.admin_users: Set[int] = set()
        self.user_sessions: Dict[int, datetime] = {}
        
        # Load authorized users from config
        self._load_authorized_users()
    
    async def initialize(self) -> None:
        """Initialize the user manager."""
        try:
            logger.info(f"User manager initialized with {len(self.authorized_users)} authorized users")
        except Exception as e:
            logger.error(f"Failed to initialize user manager: {e}")
            raise
    
    def _load_authorized_users(self) -> None:
        """Load authorized users from configuration."""
        try:
            # Load from environment variable or config
            authorized_user_ids = getattr(settings, 'TELEGRAM_AUTHORIZED_USERS', '')
            
            if authorized_user_ids:
                # Parse comma-separated user IDs
                user_ids = [int(uid.strip()) for uid in authorized_user_ids.split(',') if uid.strip()]
                self.authorized_users.update(user_ids)
            
            # Load admin users
            admin_user_ids = getattr(settings, 'TELEGRAM_ADMIN_USERS', '')
            if admin_user_ids:
                admin_ids = [int(uid.strip()) for uid in admin_user_ids.split(',') if uid.strip()]
                self.admin_users.update(admin_ids)
                # Admins are also authorized users
                self.authorized_users.update(admin_ids)
            
            logger.info(f"Loaded {len(self.authorized_users)} authorized users, {len(self.admin_users)} admins")
            
        except Exception as e:
            logger.error(f"Error loading authorized users: {e}")
            # Default: no users authorized (security first)
    
    async def authenticate_user(self, user_id: int, username: str = None) -> bool:
        """
        Authenticate a user and create session.
        
        Args:
            user_id: Telegram user ID
            username: Telegram username
            
        Returns:
            True if user is authorized, False otherwise
        """
        try:
            # Check if user is authorized
            if user_id not in self.authorized_users:
                logger.warning(f"Unauthorized access attempt from user {user_id} (@{username})")
                return False
            
            # Create or update user session
            self.user_sessions[user_id] = datetime.now()
            
            # Store/update user data
            if user_id not in self.user_data:
                self.user_data[user_id] = {
                    "user_id": user_id,
                    "username": username,
                    "first_seen": datetime.now(),
                    "last_active": datetime.now(),
                    "session_count": 1,
                    "is_admin": user_id in self.admin_users
                }
            else:
                self.user_data[user_id].update({
                    "username": username,
                    "last_active": datetime.now(),
                    "session_count": self.user_data[user_id].get("session_count", 0) + 1
                })
            
            logger.info(f"User {user_id} (@{username}) authenticated successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error authenticating user {user_id}: {e}")
            return False
    
    async def is_authorized(self, user_id: int) -> bool:
        """Check if user is authorized."""
        return user_id in self.authorized_users
    
    async def is_admin(self, user_id: int) -> bool:
        """Check if user is admin."""
        return user_id in self.admin_users
    
    async def is_session_valid(self, user_id: int, max_age_hours: int = 24) -> bool:
        """Check if user session is still valid."""
        if user_id not in self.user_sessions:
            return False
        
        session_time = self.user_sessions[user_id]
        max_age = timedelta(hours=max_age_hours)
        
        return datetime.now() - session_time < max_age
    
    async def update_user_activity(self, user_id: int) -> None:
        """Update user's last activity timestamp."""
        if user_id in self.user_data:
            self.user_data[user_id]["last_active"] = datetime.now()
        
        if user_id in self.user_sessions:
            self.user_sessions[user_id] = datetime.now()
    
    async def get_user_info(self, user_id: int) -> Optional[Dict]:
        """Get user information."""
        return self.user_data.get(user_id)
    
    async def get_all_users(self) -> List[Dict]:
        """Get all user information (admin only)."""
        return list(self.user_data.values())
    
    async def add_authorized_user(self, user_id: int, added_by: int) -> bool:
        """Add a new authorized user (admin only)."""
        if added_by not in self.admin_users:
            logger.warning(f"Non-admin user {added_by} tried to add authorized user")
            return False
        
        self.authorized_users.add(user_id)
        logger.info(f"User {user_id} added to authorized users by admin {added_by}")
        return True
    
    async def remove_authorized_user(self, user_id: int, removed_by: int) -> bool:
        """Remove an authorized user (admin only)."""
        if removed_by not in self.admin_users:
            logger.warning(f"Non-admin user {removed_by} tried to remove authorized user")
            return False
        
        if user_id in self.admin_users:
            logger.warning(f"Cannot remove admin user {user_id}")
            return False
        
        self.authorized_users.discard(user_id)
        if user_id in self.user_data:
            del self.user_data[user_id]
        if user_id in self.user_sessions:
            del self.user_sessions[user_id]
        
        logger.info(f"User {user_id} removed from authorized users by admin {removed_by}")
        return True
    
    async def get_user_stats(self) -> Dict:
        """Get user statistics."""
        active_sessions = sum(
            1 for user_id in self.user_sessions
            if await self.is_session_valid(user_id)
        )
        
        return {
            "total_authorized": len(self.authorized_users),
            "total_admins": len(self.admin_users),
            "active_sessions": active_sessions,
            "total_registered": len(self.user_data),
            "last_activity": max(
                (data.get("last_active", datetime.min) for data in self.user_data.values()),
                default=datetime.min
            )
        }
    
    async def cleanup_expired_sessions(self, max_age_hours: int = 24) -> int:
        """Clean up expired user sessions."""
        expired_count = 0
        max_age = timedelta(hours=max_age_hours)
        current_time = datetime.now()
        
        expired_users = [
            user_id for user_id, session_time in self.user_sessions.items()
            if current_time - session_time > max_age
        ]
        
        for user_id in expired_users:
            del self.user_sessions[user_id]
            expired_count += 1
        
        if expired_count > 0:
            logger.info(f"Cleaned up {expired_count} expired user sessions")
        
        return expired_count
    
    async def export_user_data(self) -> str:
        """Export user data as JSON (admin only)."""
        export_data = {
            "authorized_users": list(self.authorized_users),
            "admin_users": list(self.admin_users),
            "user_data": {
                str(k): {
                    **v,
                    "first_seen": v.get("first_seen", datetime.now()).isoformat(),
                    "last_active": v.get("last_active", datetime.now()).isoformat()
                }
                for k, v in self.user_data.items()
            },
            "export_time": datetime.now().isoformat()
        }
        
        return json.dumps(export_data, indent=2, ensure_ascii=False)

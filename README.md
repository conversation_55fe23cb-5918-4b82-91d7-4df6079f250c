# AI Crypto Trading System

🤖 AI destekli kripto trading sistemi - Trend analizi, pattern detection ve yarı-otomatik trading

## 🎯 Özellikler

### ✅ <PERSON>amlana<PERSON>ller
- **Market Data Service**: Real-time kripto veri çekme (CCXT, Binance API)
- **Database Schema**: PostgreSQL tabloları ve Redis cache sistemi
- **Data Storage System**: Verimli veri saklama ve arşivleme
- **Technical Analysis Module**: RSI, MACD, Bollinger Bands, volume analizi
- **Pattern Detection**: Support/resistance, trend lines, chart patterns
- **Signal Generation**: Trading sinyalleri ve güven skorları
- **AI Analysis Service**: GPT-4 ile haber ve sentiment analizi
- **Telegram Bot AI Assistant**: ✨ **YENİ** - Doğal dil ile sistem yönetimi

### 🔄 Geliştirme Aşamasında
- **Risk Yönetimi**: Stop-loss, take-profit, pozisyon boyutlandırma
- **Trading Engine**: Binance API ile otomatik trading
- **Web Dashboard**: React tabanlı portföy takibi

## 🛠️ Teknoloji Stack

### Backend
- **Python 3.11+** - Ana backend dili
- **FastAPI** - Modern, hızlı web framework
- **CCXT** - Kripto exchange API library
- **TA-Lib** - Teknik analiz kütüphanesi
- **OpenAI GPT-4** - AI analiz motoru
- **aiohttp** - Async HTTP client (news fetching)
- **feedparser** - RSS feed parsing
- **LiteLLM** - OpenAI-compatible API proxy

### Frontend (Telegram Bot Interface)
- **Telegram Bot API** - Native mobile interface
- **AI Assistant** - Doğal dil işleme ile sistem kontrolü
- **OpenAI/Mistral** - AI-powered responses
- **Real-time notifications** - Anlık bildirimler

### Database & Infrastructure
- **PostgreSQL** - Ana veritabanı
- **Redis** - Cache ve session yönetimi
- **Docker** - Containerization
- **Telegram Bot API** - Bildirim sistemi

## 🚀 Kurulum

### Gereksinimler
- Python 3.11+
- Telegram hesabı (Bot için)
- Docker & Docker Compose (opsiyonel)
- PostgreSQL 15+ (opsiyonel - SQLite de kullanılabilir)

### Hızlı Başlangıç

```bash
# Repository'yi klonla
git clone <repo-url>
cd crypto-trading-system

# Backend kurulumu
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt

# .env dosyasını düzenle
cp .env.example .env
# .env dosyasında API anahtarlarını güncelle

# Telegram Bot'u çalıştır
python simple_working_bot.py
```

## 📊 Mevcut Durum

### ✅ Tamamlanan Özellikler
- **Telegram Bot Interface** - AI destekli doğal dil arayüzü
- **Backend API** - FastAPI ile RESTful API
- **Veritabanı** - PostgreSQL/SQLite ile veri yönetimi
- **Teknik Analiz** - RSI, MACD, Bollinger Bands (AI entegreli)
- **Pattern Detection** - Grafik pattern tanıma ve destek/direnç
- **Signal Generation** - Kapsamlı trading sinyalleri (AI entegreli)
- **AI Analysis** - Mistral AI ile piyasa analizi ve haber değerlendirme
- **Combined Analysis** - Tüm modüllerin entegre analizi
- **AI Integration** - %25 AI ağırlığıyla akıllı trading kararları
- **Real-time Notifications** - Telegram üzerinden anlık bildirimler

## 🤖 Telegram Bot AI Assistant

### Özellikler
- **Doğal Dil İşleme**: "BTC analizi yap", "Portföyümü göster" gibi komutlar
- **Akıllı Bildirimler**: Fiyat alarmları ve trading sinyalleri
- **Kullanıcı Yetkilendirme**: Güvenli erişim kontrolü
- **Sistem Entegrasyonu**: Tüm trading servisleriyle entegre

### Kurulum
1. **Telegram Bot Oluştur**:
   ```bash
   # @BotFather ile konuş ve yeni bot oluştur
   /newbot
   # Bot token'ını al
   ```

2. **Kullanıcı ID'ni Öğren**:
   ```bash
   # @userinfobot ile konuş
   # Veya @RawDataBot kullan
   ```

3. **Environment Variables**:
   ```bash
   # .env dosyasına ekle
   TELEGRAM_BOT_TOKEN=your_bot_token_here
   TELEGRAM_AUTHORIZED_USERS=123456789,987654321
   TELEGRAM_ADMIN_USERS=123456789
   OPENAI_API_KEY=your_openai_key_here
   ```

4. **Bot'u Başlat**:
   ```bash
   # Test et
   python backend/test_telegram_bot.py

   # Standalone başlat
   python backend/start_telegram_bot.py

   # FastAPI ile başlat
   cd backend && uvicorn app.main:app --reload
   ```

### Kullanım
- **Doğal Dil Komutları**:
  - "BTC analizi yap"
  - "Portföyümü göster"
  - "Son trading sinyallerini getir"
  - "ETH için 3000$ alarm kur"
  - "Sistem durumu nasıl?"

- **Bot Komutları**:
  - `/start` - Bot'u başlat
  - `/help` - Yardım menüsü
  - `/status` - Sistem durumu

## 📊 MVP Hedefleri

- [x] Temel proje yapısı
- [x] Veri toplama (CoinGecko/Binance API)
- [x] Basit pattern detection
- [x] **AI destekli analiz sistemi**
- [x] **Telegram bot AI assistant**
- [ ] Onaylı işlem yapma mekanizması
- [ ] Basit dashboard

## 🔗 API Endpoints

### Market Data & Analysis
- `GET /api/v1/data/symbols` - Mevcut trading çiftleri
- `GET /api/v1/data/ohlcv/{symbol}` - OHLCV verileri
- `GET /api/v1/technical/{symbol}` - Teknik analiz göstergeleri
- `GET /api/v1/patterns/{symbol}` - Pattern detection sonuçları
- `GET /api/v1/signals/{symbol}` - Trading sinyalleri

### AI Analysis ✨ **YENİ**
- `GET /api/v1/ai/news/{symbol}` - Kripto haberleri
- `GET /api/v1/ai/sentiment/{symbol}` - Sentiment analizi
- `GET /api/v1/ai/analysis/{symbol}` - Kapsamlı AI analizi
- `GET /api/v1/ai/market-overview` - Piyasa genel durumu
- `GET /api/v1/ai/health` - AI servis durumu

### Combined Analysis 🔥 **YENİ**
- `GET /api/v1/signals/combined-analysis/{symbol}` - Teknik, pattern ve AI analizini birleştiren kapsamlı analiz

### Telegram Bot 🤖 **YENİ**
- `POST /api/v1/telegram/webhook` - Telegram webhook endpoint
- `POST /api/v1/telegram/start` - Bot'u başlat
- `POST /api/v1/telegram/stop` - Bot'u durdur
- `GET /api/v1/telegram/status` - Bot durumu
- `POST /api/v1/telegram/send-notification` - Bildirim gönder
- `POST /api/v1/telegram/set-alert` - Fiyat alarmı kur
- `GET /api/v1/telegram/alerts/{user_id}` - Kullanıcı alarmları
- `POST /api/v1/telegram/broadcast` - Toplu mesaj gönder

### Test & Health
- `GET /health` - Sistem durumu
- `GET /api/v1/signals/market-summary` - Piyasa özeti
- `GET /api/v1/telegram/health` - Telegram bot durumu

## 🗺️ Geliştirme Roadmap

### Faz 1: Temel Altyapı ✅
- [x] Proje yapısı kurulumu
- [x] Database schema tasarımı
- [x] API endpoint'leri tanımı
- [x] Docker containerization

### Faz 2: Veri Toplama ✅
- [x] Market data service (CoinGecko, Binance API)
- [x] Real-time price tracking
- [x] Historical data storage
- [x] Data validation ve cleaning

### Faz 3: Teknik Analiz ✅
- [x] Technical indicators (RSI, MACD, Bollinger Bands)
- [x] Pattern detection algorithms
- [x] Signal generation logic
- [x] Multi-timeframe analysis

### Faz 4: AI Entegrasyonu ✅
- [x] News aggregation (CoinTelegraph, CoinDesk, Reddit)
- [x] Sentiment analysis
- [x] AI-powered market insights
- [x] OpenAI GPT-4 integration
- [x] **AI-Signal Generation entegrasyonu**
- [x] **Combined analysis endpoint**
- [x] **Enhanced technical analysis with AI**

### Faz 5: Risk Yönetimi 🔄
- [ ] Position sizing algorithms
- [ ] Stop-loss optimization
- [ ] Portfolio risk assessment
- [ ] Drawdown protection

### Faz 6: Trading Engine 🔄
- [ ] Binance API integration
- [ ] Order management system
- [ ] Execution algorithms
- [ ] Trade logging

### Faz 7: Telegram Bot AI Assistant ✅
- [x] **Telegram bot AI assistant**
- [x] **Natural language command processing**
- [x] **User authentication and authorization**
- [x] **Smart notifications and price alerts**
- [x] **System integration with all services**
- [ ] Discord integration
- [ ] Email notifications

### Faz 8: Web Dashboard 🔄
- [ ] React frontend development
- [ ] Portfolio tracking
- [ ] Performance analytics
- [ ] User management

## ⚠️ Güvenlik ve Uyarılar

- **Yatırım Danışmanlığı Değildir**: Bu sistem sadece analiz aracıdır
- **Deneysel Fonlar**: Sadece kaybetmeyi göze alabileceğiniz fonlarla test edin
- **Risk Yönetimi**: Her zaman stop-loss kullanın
- **API Güvenliği**: API anahtarlarınızı güvenli saklayın

## 📝 Lisans

MIT License - Detaylar için LICENSE dosyasına bakın.

## 👨‍💻 Geliştirici

**inkbytefo** - AI Destekli Trading Sistemi

"""
Trading Models for AI Crypto Trading System
Author: inkbytefo
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Text, ForeignKey, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field
from enum import Enum as PyEnum
import uuid

from app.core.database import Base


class TradeStatus(PyEnum):
    """Trade status enumeration"""
    PENDING = "pending"
    OPEN = "open"
    CLOSED = "closed"
    CANCELLED = "cancelled"
    FAILED = "failed"


class TradeType(PyEnum):
    """Trade type enumeration"""
    BUY = "buy"
    SELL = "sell"


class OrderType(PyEnum):
    """Order type enumeration"""
    MARKET = "market"
    LIMIT = "limit"
    STOP_LOSS = "stop_loss"
    TAKE_PROFIT = "take_profit"


class Trade(Base):
    """Trading transactions table"""
    __tablename__ = "trades"
    
    id = Column(Integer, primary_key=True, index=True)
    trade_uuid = Column(String(36), unique=True, index=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Trade Information
    symbol = Column(String(20), nullable=False, index=True)
    exchange = Column(String(20), nullable=False, default="binance")
    trade_type = Column(Enum(TradeType), nullable=False)
    order_type = Column(Enum(OrderType), nullable=False, default=OrderType.MARKET)
    
    # Price and Quantity
    quantity = Column(Float, nullable=False)
    price = Column(Float)  # For limit orders
    executed_price = Column(Float)  # Actual execution price
    executed_quantity = Column(Float)  # Actual executed quantity
    
    # Order Management
    exchange_order_id = Column(String(100), index=True)  # Exchange's order ID
    status = Column(Enum(TradeStatus), default=TradeStatus.PENDING, index=True)
    
    # Financial Data
    total_value = Column(Float)  # Total trade value
    fees = Column(Float, default=0.0)
    net_value = Column(Float)  # Total value minus fees
    
    # Risk Management
    stop_loss_price = Column(Float)
    take_profit_price = Column(Float)
    
    # AI Analysis
    ai_confidence = Column(Float)  # AI confidence score (0-1)
    ai_reasoning = Column(Text)  # AI reasoning for the trade
    technical_indicators = Column(Text)  # JSON string of technical indicators
    
    # Metadata
    created_at = Column(DateTime, default=func.now(), index=True)
    executed_at = Column(DateTime)
    closed_at = Column(DateTime)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="trades")
    trade_signals = relationship("TradeSignal", back_populates="trade", cascade="all, delete-orphan")


class TradeSignal(Base):
    """AI trading signals and analysis"""
    __tablename__ = "trade_signals"
    
    id = Column(Integer, primary_key=True, index=True)
    signal_uuid = Column(String(36), unique=True, index=True, default=lambda: str(uuid.uuid4()))
    trade_id = Column(Integer, ForeignKey("trades.id"), nullable=True)  # Can exist without trade
    
    # Signal Information
    symbol = Column(String(20), nullable=False, index=True)
    signal_type = Column(String(20), nullable=False)  # buy, sell, hold
    strength = Column(Float, nullable=False)  # Signal strength (0-1)
    confidence = Column(Float, nullable=False)  # AI confidence (0-1)
    
    # Price Targets
    entry_price = Column(Float)
    target_price = Column(Float)
    stop_loss_price = Column(Float)
    
    # Technical Analysis
    rsi_value = Column(Float)
    macd_value = Column(Float)
    volume_spike = Column(Boolean, default=False)
    pattern_detected = Column(String(50))
    
    # AI Analysis
    ai_reasoning = Column(Text)
    market_sentiment = Column(String(20))  # bullish, bearish, neutral
    news_sentiment = Column(Float)  # -1 to 1
    
    # Signal Status
    is_active = Column(Boolean, default=True)
    is_executed = Column(Boolean, default=False)
    
    # Metadata
    created_at = Column(DateTime, default=func.now(), index=True)
    expires_at = Column(DateTime)
    executed_at = Column(DateTime)
    
    # Relationships
    trade = relationship("Trade", back_populates="trade_signals")


class Portfolio(Base):
    """User portfolio tracking"""
    __tablename__ = "portfolios"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Portfolio Information
    name = Column(String(100), nullable=False, default="Main Portfolio")
    exchange = Column(String(20), nullable=False, default="binance")
    is_active = Column(Boolean, default=True)
    
    # Portfolio Value
    total_value_usd = Column(Float, default=0.0)
    total_invested = Column(Float, default=0.0)
    total_profit_loss = Column(Float, default=0.0)
    profit_loss_percentage = Column(Float, default=0.0)
    
    # Risk Metrics
    max_drawdown = Column(Float, default=0.0)
    sharpe_ratio = Column(Float)
    win_rate = Column(Float, default=0.0)
    
    # Metadata
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    last_sync = Column(DateTime)
    
    # Relationships
    user = relationship("User", back_populates="portfolios")
    holdings = relationship("PortfolioHolding", back_populates="portfolio", cascade="all, delete-orphan")


class PortfolioHolding(Base):
    """Individual cryptocurrency holdings"""
    __tablename__ = "portfolio_holdings"
    
    id = Column(Integer, primary_key=True, index=True)
    portfolio_id = Column(Integer, ForeignKey("portfolios.id"), nullable=False)
    
    # Asset Information
    symbol = Column(String(20), nullable=False, index=True)
    asset_name = Column(String(100))
    
    # Holding Data
    quantity = Column(Float, nullable=False)
    average_buy_price = Column(Float, nullable=False)
    current_price = Column(Float)
    total_value_usd = Column(Float)
    
    # Performance
    unrealized_pnl = Column(Float, default=0.0)
    unrealized_pnl_percentage = Column(Float, default=0.0)
    realized_pnl = Column(Float, default=0.0)
    
    # Metadata
    first_purchase_date = Column(DateTime)
    last_update = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    portfolio = relationship("Portfolio", back_populates="holdings")


# Pydantic models for API
class TradeCreate(BaseModel):
    """Trade creation model"""
    symbol: str
    trade_type: str = Field(..., pattern="^(buy|sell)$")
    order_type: str = Field(default="market", pattern="^(market|limit|stop_loss|take_profit)$")
    quantity: float = Field(..., gt=0)
    price: Optional[float] = Field(None, gt=0)
    stop_loss_price: Optional[float] = Field(None, gt=0)
    take_profit_price: Optional[float] = Field(None, gt=0)
    exchange: str = "binance"


class TradeResponse(BaseModel):
    """Trade response model"""
    id: int
    trade_uuid: str
    symbol: str
    exchange: str
    trade_type: str
    order_type: str
    quantity: float
    price: Optional[float] = None
    executed_price: Optional[float] = None
    executed_quantity: Optional[float] = None
    status: str
    total_value: Optional[float] = None
    fees: Optional[float] = None
    ai_confidence: Optional[float] = None
    created_at: datetime
    executed_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class SignalCreate(BaseModel):
    """Signal creation model"""
    symbol: str
    signal_type: str = Field(..., pattern="^(buy|sell|hold)$")
    strength: float = Field(..., ge=0, le=1)
    confidence: float = Field(..., ge=0, le=1)
    entry_price: Optional[float] = None
    target_price: Optional[float] = None
    stop_loss_price: Optional[float] = None
    ai_reasoning: Optional[str] = None


class SignalResponse(BaseModel):
    """Signal response model"""
    id: int
    signal_uuid: str
    symbol: str
    signal_type: str
    strength: float
    confidence: float
    entry_price: Optional[float] = None
    target_price: Optional[float] = None
    stop_loss_price: Optional[float] = None
    is_active: bool
    is_executed: bool
    created_at: datetime
    
    class Config:
        from_attributes = True


class PortfolioResponse(BaseModel):
    """Portfolio response model"""
    id: int
    name: str
    exchange: str
    total_value_usd: float
    total_invested: float
    total_profit_loss: float
    profit_loss_percentage: float
    win_rate: float
    created_at: datetime
    last_sync: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class HoldingResponse(BaseModel):
    """Holding response model"""
    id: int
    symbol: str
    asset_name: Optional[str] = None
    quantity: float
    average_buy_price: float
    current_price: Optional[float] = None
    total_value_usd: Optional[float] = None
    unrealized_pnl: float
    unrealized_pnl_percentage: float
    first_purchase_date: Optional[datetime] = None
    
    class Config:
        from_attributes = True

"""
Technical Analysis Service - Core technical indicators and pattern detection
Author: inkbytefo
"""

import numpy as np
import pandas as pd
# import pandas_ta as ta  # Using custom implementations
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

from app.core.config import settings
from app.core.database import cache_manager
from app.services.market_data_service import MarketDataService

logger = logging.getLogger("technical_analysis")


class SignalType(Enum):
    """Signal types"""
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"


class TrendDirection(Enum):
    """Trend directions"""
    BULLISH = "bullish"
    BEARISH = "bearish"
    SIDEWAYS = "sideways"


@dataclass
class TechnicalIndicators:
    """Technical indicators data structure"""
    # Price-based indicators
    sma_20: Optional[float] = None
    sma_50: Optional[float] = None
    ema_12: Optional[float] = None
    ema_26: Optional[float] = None
    
    # Momentum indicators
    rsi: Optional[float] = None
    macd: Optional[float] = None
    macd_signal: Optional[float] = None
    macd_histogram: Optional[float] = None
    
    # Volatility indicators
    bb_upper: Optional[float] = None
    bb_middle: Optional[float] = None
    bb_lower: Optional[float] = None
    bb_width: Optional[float] = None
    
    # Volume indicators
    volume_sma: Optional[float] = None
    volume_spike: bool = False
    
    # Support/Resistance
    support_level: Optional[float] = None
    resistance_level: Optional[float] = None
    
    # Trend indicators
    trend_direction: Optional[TrendDirection] = None
    trend_strength: Optional[float] = None


@dataclass
class TechnicalSignal:
    """Technical analysis signal"""
    symbol: str
    signal_type: SignalType
    strength: float  # 0-1
    confidence: float  # 0-1
    entry_price: Optional[float] = None
    target_price: Optional[float] = None
    stop_loss_price: Optional[float] = None
    reasoning: str = ""
    indicators: Optional[TechnicalIndicators] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class PatternDetection:
    """Pattern detection result"""
    pattern_name: str
    confidence: float  # 0-1
    breakout_direction: Optional[TrendDirection] = None
    target_price: Optional[float] = None
    stop_loss: Optional[float] = None
    description: str = ""


class TechnicalAnalysisService:
    """Technical analysis service with TA-Lib integration"""
    
    def __init__(self):
        self.cache_ttl = 300  # 5 minutes cache
        self.min_data_points = 50  # Minimum data points for analysis
        
        # Technical analysis parameters
        self.rsi_period = 14
        self.macd_fast = 12
        self.macd_slow = 26
        self.macd_signal = 9
        self.bb_period = 20
        self.bb_std = 2
        self.sma_short = 20
        self.sma_long = 50
        
        # Signal thresholds
        self.rsi_oversold = 30
        self.rsi_overbought = 70
        self.volume_spike_threshold = 2.0  # 2x average volume
        
    async def analyze_symbol(
        self, 
        symbol: str, 
        timeframe: str = "1h", 
        limit: int = 200,
        exchange: str = "binance"
    ) -> Optional[TechnicalSignal]:
        """Perform comprehensive technical analysis for a symbol"""
        try:
            # Check cache first
            cache_key = f"ta_analysis:{exchange}:{symbol}:{timeframe}"
            cached_analysis = await cache_manager.get(cache_key)
            
            if cached_analysis:
                logger.debug(f"Using cached technical analysis for {symbol}")
                return TechnicalSignal(**cached_analysis)
            
            # Get market data
            market_data = await market_data_service.get_ohlcv_data(
                symbol, timeframe, limit, exchange
            )
            
            if not market_data or len(market_data) < self.min_data_points:
                logger.warning(f"Insufficient data for technical analysis: {symbol}")
                return None
            
            # Convert to DataFrame
            df = pd.DataFrame(market_data)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df = df.sort_values('timestamp')
            
            # Calculate technical indicators
            indicators = await self._calculate_indicators(df)
            
            # Detect patterns
            patterns = await self._detect_patterns(df, indicators)
            
            # Generate signal
            signal = await self._generate_signal(symbol, df, indicators, patterns)
            
            # Cache the result
            if signal:
                signal_dict = {
                    'symbol': signal.symbol,
                    'signal_type': signal.signal_type.value,
                    'strength': signal.strength,
                    'confidence': signal.confidence,
                    'entry_price': signal.entry_price,
                    'target_price': signal.target_price,
                    'stop_loss_price': signal.stop_loss_price,
                    'reasoning': signal.reasoning,
                    'timestamp': signal.timestamp.isoformat()
                }
                await cache_manager.set(cache_key, signal_dict, self.cache_ttl)
            
            logger.info(f"Technical analysis completed for {symbol}: {signal.signal_type.value if signal else 'No signal'}")
            return signal
            
        except Exception as e:
            logger.error(f"Error in technical analysis for {symbol}: {e}")
            return None
    
    async def _calculate_indicators(self, df: pd.DataFrame) -> TechnicalIndicators:
        """Calculate all technical indicators"""
        try:
            # Data preparation for pandas-ta
            
            # Moving averages
            sma_20_series = ta.sma(df['close'], length=self.sma_short)
            sma_20 = sma_20_series.iloc[-1] if not pd.isna(sma_20_series.iloc[-1]) else None

            sma_50_series = ta.sma(df['close'], length=self.sma_long)
            sma_50 = sma_50_series.iloc[-1] if not pd.isna(sma_50_series.iloc[-1]) else None

            ema_12_series = ta.ema(df['close'], length=self.macd_fast)
            ema_12 = ema_12_series.iloc[-1] if not pd.isna(ema_12_series.iloc[-1]) else None

            ema_26_series = ta.ema(df['close'], length=self.macd_slow)
            ema_26 = ema_26_series.iloc[-1] if not pd.isna(ema_26_series.iloc[-1]) else None

            # RSI
            rsi_series = ta.rsi(df['close'], length=self.rsi_period)
            rsi = rsi_series.iloc[-1] if not pd.isna(rsi_series.iloc[-1]) else None

            # MACD
            macd_data = ta.macd(df['close'], fast=self.macd_fast, slow=self.macd_slow, signal=self.macd_signal)
            if macd_data is not None and not macd_data.empty:
                macd = macd_data['MACD_12_26_9'].iloc[-1] if not pd.isna(macd_data['MACD_12_26_9'].iloc[-1]) else None
                macd_signal = macd_data['MACDs_12_26_9'].iloc[-1] if not pd.isna(macd_data['MACDs_12_26_9'].iloc[-1]) else None
                macd_hist = macd_data['MACDh_12_26_9'].iloc[-1] if not pd.isna(macd_data['MACDh_12_26_9'].iloc[-1]) else None
            else:
                macd = macd_signal = macd_hist = None

            # Bollinger Bands
            bb_data = ta.bbands(df['close'], length=self.bb_period, std=self.bb_std)
            if bb_data is not None and not bb_data.empty:
                bb_upper = bb_data['BBU_20_2.0'].iloc[-1] if not pd.isna(bb_data['BBU_20_2.0'].iloc[-1]) else None
                bb_middle = bb_data['BBM_20_2.0'].iloc[-1] if not pd.isna(bb_data['BBM_20_2.0'].iloc[-1]) else None
                bb_lower = bb_data['BBL_20_2.0'].iloc[-1] if not pd.isna(bb_data['BBL_20_2.0'].iloc[-1]) else None
            else:
                bb_upper = bb_middle = bb_lower = None

            # Volume analysis
            volume_sma_series = ta.sma(df['volume'], length=20)
            volume_sma = volume_sma_series.iloc[-1] if not pd.isna(volume_sma_series.iloc[-1]) else None
            current_volume = df['volume'].iloc[-1]
            volume_spike = volume_sma and current_volume > (volume_sma * self.volume_spike_threshold)
            
            # Support and resistance levels
            support, resistance = self._calculate_support_resistance(df)
            
            # Trend analysis
            trend_direction, trend_strength = self._analyze_trend(df['close'].values, sma_20, sma_50)
            
            return TechnicalIndicators(
                sma_20=float(sma_20) if sma_20 is not None else None,
                sma_50=float(sma_50) if sma_50 is not None else None,
                ema_12=float(ema_12) if ema_12 is not None else None,
                ema_26=float(ema_26) if ema_26 is not None else None,
                rsi=float(rsi) if rsi is not None else None,
                macd=float(macd) if macd is not None else None,
                macd_signal=float(macd_signal) if macd_signal is not None else None,
                macd_histogram=float(macd_hist) if macd_hist is not None else None,
                bb_upper=float(bb_upper) if bb_upper is not None else None,
                bb_middle=float(bb_middle) if bb_middle is not None else None,
                bb_lower=float(bb_lower) if bb_lower is not None else None,
                bb_width=float(bb_upper - bb_lower) if all([bb_upper, bb_lower, bb_middle]) else None,
                volume_sma=float(volume_sma) if volume_sma is not None else None,
                volume_spike=volume_spike,
                support_level=support,
                resistance_level=resistance,
                trend_direction=trend_direction,
                trend_strength=trend_strength
            )
            
        except Exception as e:
            logger.error(f"Error calculating technical indicators: {e}")
            return TechnicalIndicators()
    
    def _calculate_support_resistance(self, df: pd.DataFrame) -> Tuple[Optional[float], Optional[float]]:
        """Calculate support and resistance levels"""
        try:
            # Use recent data for support/resistance calculation
            recent_data = df.tail(50)
            
            # Find local minima and maxima
            highs = recent_data['high'].values
            lows = recent_data['low'].values
            
            # Simple support/resistance calculation
            # Support: recent significant low
            support = np.percentile(lows, 10)  # 10th percentile of lows
            
            # Resistance: recent significant high
            resistance = np.percentile(highs, 90)  # 90th percentile of highs
            
            return float(support), float(resistance)
            
        except Exception as e:
            logger.error(f"Error calculating support/resistance: {e}")
            return None, None
    
    def _analyze_trend(self, close: np.ndarray, sma_20: float, sma_50: float) -> Tuple[TrendDirection, float]:
        """Analyze trend direction and strength"""
        try:
            current_price = close[-1]
            
            # Trend direction based on moving averages
            if sma_20 > sma_50 and current_price > sma_20:
                trend_direction = TrendDirection.BULLISH
            elif sma_20 < sma_50 and current_price < sma_20:
                trend_direction = TrendDirection.BEARISH
            else:
                trend_direction = TrendDirection.SIDEWAYS
            
            # Trend strength based on price distance from moving averages
            if sma_20 and sma_50:
                ma_spread = abs(sma_20 - sma_50) / sma_50
                price_ma_distance = abs(current_price - sma_20) / sma_20
                trend_strength = min(1.0, (ma_spread + price_ma_distance) * 10)
            else:
                trend_strength = 0.5
            
            return trend_direction, float(trend_strength)
            
        except Exception as e:
            logger.error(f"Error analyzing trend: {e}")
            return TrendDirection.SIDEWAYS, 0.5
    
    async def _detect_patterns(self, df: pd.DataFrame, indicators: TechnicalIndicators) -> List[PatternDetection]:
        """Detect chart patterns"""
        patterns = []
        
        try:
            current_price = df['close'].iloc[-1]
            
            # Bollinger Band squeeze pattern
            if indicators.bb_width and indicators.bb_width < (current_price * 0.02):  # 2% width
                patterns.append(PatternDetection(
                    pattern_name="Bollinger Band Squeeze",
                    confidence=0.7,
                    description="Low volatility, potential breakout incoming"
                ))
            
            # RSI divergence patterns
            if indicators.rsi:
                if indicators.rsi < self.rsi_oversold:
                    patterns.append(PatternDetection(
                        pattern_name="RSI Oversold",
                        confidence=0.8,
                        breakout_direction=TrendDirection.BULLISH,
                        description="RSI indicates oversold conditions"
                    ))
                elif indicators.rsi > self.rsi_overbought:
                    patterns.append(PatternDetection(
                        pattern_name="RSI Overbought",
                        confidence=0.8,
                        breakout_direction=TrendDirection.BEARISH,
                        description="RSI indicates overbought conditions"
                    ))
            
            # MACD crossover patterns
            if indicators.macd and indicators.macd_signal:
                if indicators.macd > indicators.macd_signal and indicators.macd_histogram > 0:
                    patterns.append(PatternDetection(
                        pattern_name="MACD Bullish Crossover",
                        confidence=0.75,
                        breakout_direction=TrendDirection.BULLISH,
                        description="MACD crossed above signal line"
                    ))
                elif indicators.macd < indicators.macd_signal and indicators.macd_histogram < 0:
                    patterns.append(PatternDetection(
                        pattern_name="MACD Bearish Crossover",
                        confidence=0.75,
                        breakout_direction=TrendDirection.BEARISH,
                        description="MACD crossed below signal line"
                    ))
            
            # Support/Resistance breakout
            if indicators.support_level and indicators.resistance_level:
                if current_price > indicators.resistance_level:
                    patterns.append(PatternDetection(
                        pattern_name="Resistance Breakout",
                        confidence=0.8,
                        breakout_direction=TrendDirection.BULLISH,
                        target_price=current_price + (current_price - indicators.resistance_level),
                        description="Price broke above resistance level"
                    ))
                elif current_price < indicators.support_level:
                    patterns.append(PatternDetection(
                        pattern_name="Support Breakdown",
                        confidence=0.8,
                        breakout_direction=TrendDirection.BEARISH,
                        target_price=current_price - (indicators.support_level - current_price),
                        description="Price broke below support level"
                    ))
            
            return patterns

        except Exception as e:
            logger.error(f"Error detecting patterns: {e}")
            return []

    async def _generate_signal(
        self,
        symbol: str,
        df: pd.DataFrame,
        indicators: TechnicalIndicators,
        patterns: List[PatternDetection]
    ) -> Optional[TechnicalSignal]:
        """Generate trading signal based on technical analysis"""
        try:
            current_price = df['close'].iloc[-1]
            signal_strength = 0.0
            signal_type = SignalType.HOLD
            reasoning_parts = []

            # RSI-based signals
            if indicators.rsi:
                if indicators.rsi < self.rsi_oversold:
                    signal_strength += 0.3
                    reasoning_parts.append(f"RSI oversold ({indicators.rsi:.1f})")
                    if signal_type == SignalType.HOLD:
                        signal_type = SignalType.BUY
                elif indicators.rsi > self.rsi_overbought:
                    signal_strength += 0.3
                    reasoning_parts.append(f"RSI overbought ({indicators.rsi:.1f})")
                    if signal_type == SignalType.HOLD:
                        signal_type = SignalType.SELL

            # MACD-based signals
            if indicators.macd and indicators.macd_signal and indicators.macd_histogram:
                if indicators.macd > indicators.macd_signal and indicators.macd_histogram > 0:
                    if signal_type != SignalType.SELL:
                        signal_strength += 0.25
                        reasoning_parts.append("MACD bullish crossover")
                        if signal_type == SignalType.HOLD:
                            signal_type = SignalType.BUY
                elif indicators.macd < indicators.macd_signal and indicators.macd_histogram < 0:
                    if signal_type != SignalType.BUY:
                        signal_strength += 0.25
                        reasoning_parts.append("MACD bearish crossover")
                        if signal_type == SignalType.HOLD:
                            signal_type = SignalType.SELL

            # Moving average trend signals
            if indicators.sma_20 and indicators.sma_50:
                if indicators.sma_20 > indicators.sma_50 and current_price > indicators.sma_20:
                    if signal_type != SignalType.SELL:
                        signal_strength += 0.2
                        reasoning_parts.append("Bullish trend (price above MA)")
                        if signal_type == SignalType.HOLD:
                            signal_type = SignalType.BUY
                elif indicators.sma_20 < indicators.sma_50 and current_price < indicators.sma_20:
                    if signal_type != SignalType.BUY:
                        signal_strength += 0.2
                        reasoning_parts.append("Bearish trend (price below MA)")
                        if signal_type == SignalType.HOLD:
                            signal_type = SignalType.SELL

            # Volume confirmation
            if indicators.volume_spike:
                signal_strength += 0.15
                reasoning_parts.append("Volume spike detected")

            # Pattern-based signals
            for pattern in patterns:
                if pattern.confidence > 0.7:
                    if pattern.breakout_direction == TrendDirection.BULLISH:
                        if signal_type != SignalType.SELL:
                            signal_strength += 0.1 * pattern.confidence
                            reasoning_parts.append(f"Bullish pattern: {pattern.pattern_name}")
                            if signal_type == SignalType.HOLD:
                                signal_type = SignalType.BUY
                    elif pattern.breakout_direction == TrendDirection.BEARISH:
                        if signal_type != SignalType.BUY:
                            signal_strength += 0.1 * pattern.confidence
                            reasoning_parts.append(f"Bearish pattern: {pattern.pattern_name}")
                            if signal_type == SignalType.HOLD:
                                signal_type = SignalType.SELL

            # Normalize signal strength
            signal_strength = min(1.0, signal_strength)

            # Calculate confidence based on multiple confirmations
            confidence = len(reasoning_parts) / 5.0  # Max 5 factors
            confidence = min(1.0, confidence)

            # Only generate signal if strength is above threshold
            if signal_strength < 0.3:
                signal_type = SignalType.HOLD
                signal_strength = 0.1
                reasoning_parts = ["Insufficient signal strength"]

            # Calculate price targets
            entry_price = current_price
            target_price = None
            stop_loss_price = None

            if signal_type == SignalType.BUY:
                # Target: 3% above current price or resistance level
                if indicators.resistance_level and indicators.resistance_level > current_price:
                    target_price = indicators.resistance_level
                else:
                    target_price = current_price * 1.03

                # Stop loss: 2% below current price or support level
                if indicators.support_level and indicators.support_level < current_price:
                    stop_loss_price = max(indicators.support_level, current_price * 0.98)
                else:
                    stop_loss_price = current_price * 0.98

            elif signal_type == SignalType.SELL:
                # Target: 3% below current price or support level
                if indicators.support_level and indicators.support_level < current_price:
                    target_price = indicators.support_level
                else:
                    target_price = current_price * 0.97

                # Stop loss: 2% above current price or resistance level
                if indicators.resistance_level and indicators.resistance_level > current_price:
                    stop_loss_price = min(indicators.resistance_level, current_price * 1.02)
                else:
                    stop_loss_price = current_price * 1.02

            reasoning = "; ".join(reasoning_parts)

            return TechnicalSignal(
                symbol=symbol,
                signal_type=signal_type,
                strength=signal_strength,
                confidence=confidence,
                entry_price=entry_price,
                target_price=target_price,
                stop_loss_price=stop_loss_price,
                reasoning=reasoning,
                indicators=indicators
            )

        except Exception as e:
            logger.error(f"Error generating signal: {e}")
            return None

    async def get_multiple_timeframe_analysis(
        self,
        symbol: str,
        timeframes: List[str] = None,
        exchange: str = "binance"
    ) -> Dict[str, Optional[TechnicalSignal]]:
        """Get technical analysis for multiple timeframes"""
        if timeframes is None:
            timeframes = ["15m", "1h", "4h", "1d"]

        results = {}

        for timeframe in timeframes:
            try:
                signal = await self.analyze_symbol(symbol, timeframe, exchange=exchange)
                results[timeframe] = signal

            except Exception as e:
                logger.error(f"Error analyzing {symbol} on {timeframe}: {e}")
                results[timeframe] = None

        return results

    async def get_market_overview(
        self,
        symbols: List[str] = None,
        timeframe: str = "1h",
        exchange: str = "binance"
    ) -> Dict[str, Optional[TechnicalSignal]]:
        """Get technical analysis overview for multiple symbols"""
        if symbols is None:
            symbols = settings.DEFAULT_SYMBOLS

        results = {}

        for symbol in symbols:
            try:
                signal = await self.analyze_symbol(symbol, timeframe, exchange=exchange)
                results[symbol] = signal

            except Exception as e:
                logger.error(f"Error analyzing {symbol}: {e}")
                results[symbol] = None

        return results

    async def get_enhanced_analysis(
        self,
        symbol: str,
        timeframe: str = "1h",
        exchange: str = "binance",
        include_ai: bool = True
    ) -> Dict[str, Any]:
        """Get enhanced analysis combining technical indicators, pattern detection, and AI analysis"""
        try:
            # Import here to avoid circular imports
            from app.services.pattern_detection_service import pattern_detection_service

            # Get technical analysis
            technical_signal = await self.analyze_symbol(symbol, timeframe, exchange=exchange)

            # Get pattern analysis
            pattern_analysis = await pattern_detection_service.analyze_patterns(
                symbol, timeframe, exchange=exchange
            )

            # Get AI analysis if requested
            ai_analysis = None
            if include_ai:
                try:
                    from app.services.ai_analysis_service import ai_analysis_service
                    ai_analysis = await ai_analysis_service.generate_comprehensive_analysis(
                        symbol, timeframe, include_technical=False
                    )
                except Exception as e:
                    logger.warning(f"AI analysis failed for {symbol}: {e}")

            if not technical_signal:
                return {"error": "Technical analysis failed"}

            enhanced_analysis = {
                "symbol": symbol,
                "timeframe": timeframe,
                "technical_signal": {
                    "signal_type": technical_signal.signal_type.value,
                    "strength": technical_signal.strength,
                    "confidence": technical_signal.confidence,
                    "reasoning": technical_signal.reasoning,
                    "entry_price": technical_signal.entry_price,
                    "target_price": technical_signal.target_price,
                    "stop_loss_price": technical_signal.stop_loss_price
                },
                "technical_indicators": {
                    "rsi": technical_signal.indicators.rsi,
                    "macd": technical_signal.indicators.macd,
                    "trend_direction": technical_signal.indicators.trend_direction.value if technical_signal.indicators.trend_direction else None,
                    "support_level": technical_signal.indicators.support_level,
                    "resistance_level": technical_signal.indicators.resistance_level,
                    "volume_spike": technical_signal.indicators.volume_spike
                }
            }

            # Add pattern analysis if available
            if pattern_analysis:
                enhanced_analysis["pattern_analysis"] = {
                    "overall_trend": pattern_analysis.overall_trend,
                    "trend_strength": pattern_analysis.trend_strength,
                    "pattern_reliability": pattern_analysis.pattern_reliability,
                    "support_levels_count": len(pattern_analysis.support_levels),
                    "resistance_levels_count": len(pattern_analysis.resistance_levels),
                    "trend_lines_count": len(pattern_analysis.trend_lines),
                    "chart_patterns_count": len(pattern_analysis.chart_patterns),
                    "next_key_levels": pattern_analysis.next_key_levels
                }

                # Add strongest patterns
                if pattern_analysis.chart_patterns:
                    strongest_pattern = max(pattern_analysis.chart_patterns, key=lambda p: p.confidence)
                    enhanced_analysis["strongest_pattern"] = {
                        "pattern_name": strongest_pattern.pattern_name,
                        "confidence": strongest_pattern.confidence,
                        "breakout_direction": strongest_pattern.breakout_direction,
                        "target_price": strongest_pattern.target_price
                    }

            # Add AI analysis if available
            if ai_analysis:
                enhanced_analysis["ai_analysis"] = {
                    "sentiment": {
                        "overall_sentiment": ai_analysis.sentiment_analysis.overall_sentiment.value,
                        "sentiment_score": ai_analysis.sentiment_analysis.sentiment_score,
                        "confidence": ai_analysis.sentiment_analysis.confidence,
                        "news_count": ai_analysis.sentiment_analysis.news_count,
                        "bullish_signals": len(ai_analysis.sentiment_analysis.bullish_signals),
                        "bearish_signals": len(ai_analysis.sentiment_analysis.bearish_signals)
                    },
                    "market_insights": [
                        {
                            "insight_type": insight.insight_type.value,
                            "description": insight.description,
                            "impact_score": insight.impact_score,
                            "confidence": insight.confidence
                        }
                        for insight in ai_analysis.market_insights
                    ],
                    "ai_recommendation": ai_analysis.ai_recommendation,
                    "confidence_score": ai_analysis.confidence_score,
                    "risk_assessment": ai_analysis.risk_assessment,
                    "key_factors": ai_analysis.key_factors
                }

                # Add AI sentiment to overall assessment
                enhanced_analysis["combined_assessment"] = {
                    "technical_sentiment": technical_signal.signal_type.value,
                    "ai_sentiment": ai_analysis.sentiment_analysis.overall_sentiment.value,
                    "pattern_trend": pattern_analysis.overall_trend if pattern_analysis else "unknown",
                    "overall_confidence": (
                        technical_signal.confidence +
                        ai_analysis.confidence_score +
                        (pattern_analysis.pattern_reliability if pattern_analysis else 0.5)
                    ) / 3
                }

                # Add strongest support/resistance
                if pattern_analysis.support_levels:
                    strongest_support = max(pattern_analysis.support_levels, key=lambda s: s.strength)
                    enhanced_analysis["strongest_support"] = {
                        "price": strongest_support.price,
                        "strength": strongest_support.strength,
                        "touch_count": strongest_support.touch_count
                    }

                if pattern_analysis.resistance_levels:
                    strongest_resistance = max(pattern_analysis.resistance_levels, key=lambda r: r.strength)
                    enhanced_analysis["strongest_resistance"] = {
                        "price": strongest_resistance.price,
                        "strength": strongest_resistance.strength,
                        "touch_count": strongest_resistance.touch_count
                    }

            # Calculate combined confidence score
            base_confidence = technical_signal.confidence
            pattern_bonus = 0

            if pattern_analysis:
                # Bonus for pattern confirmation
                if pattern_analysis.pattern_reliability > 0.7:
                    pattern_bonus += 0.1

                # Bonus for trend confirmation
                tech_trend = technical_signal.indicators.trend_direction
                pattern_trend = pattern_analysis.overall_trend

                if tech_trend and pattern_trend:
                    if (tech_trend.value == "bullish" and pattern_trend == "bullish") or \
                       (tech_trend.value == "bearish" and pattern_trend == "bearish"):
                        pattern_bonus += 0.15

            enhanced_analysis["combined_confidence"] = min(1.0, base_confidence + pattern_bonus)
            enhanced_analysis["analysis_timestamp"] = datetime.now().isoformat()

            return enhanced_analysis

        except Exception as e:
            logger.error(f"Error in enhanced analysis for {symbol}: {e}")
            return {"error": str(e)}

    async def calculate_position_size(
        self,
        signal: TechnicalSignal,
        account_balance: float,
        risk_percentage: float = 0.02
    ) -> Dict[str, float]:
        """Calculate optimal position size based on risk management"""
        try:
            if not signal.entry_price or not signal.stop_loss_price:
                return {"position_size": 0, "risk_amount": 0}

            # Calculate risk per unit
            if signal.signal_type == SignalType.BUY:
                risk_per_unit = signal.entry_price - signal.stop_loss_price
            else:
                risk_per_unit = signal.stop_loss_price - signal.entry_price

            if risk_per_unit <= 0:
                return {"position_size": 0, "risk_amount": 0}

            # Calculate maximum risk amount
            max_risk_amount = account_balance * risk_percentage

            # Calculate position size
            position_size = max_risk_amount / risk_per_unit

            # Apply signal strength multiplier
            position_size *= signal.strength

            # Apply confidence multiplier
            position_size *= signal.confidence

            return {
                "position_size": round(position_size, 8),
                "risk_amount": round(max_risk_amount, 2),
                "risk_per_unit": round(risk_per_unit, 8),
                "max_position_value": round(position_size * signal.entry_price, 2)
            }

        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return {"position_size": 0, "risk_amount": 0}


# Global instance
technical_analysis_service = TechnicalAnalysisService()

#!/usr/bin/env python3
"""
Database Migration Script for AI Crypto Trading System
Author: inkbytefo
"""

import sys
import os
import asyncio
import logging
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from app.core.database import init_database, check_database_connection
from app.core.config import settings

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("migration")


async def run_migration():
    """Run database migration"""
    print("🗄️ AI Crypto Trading System - Database Migration")
    print("Author: inkbytefo")
    print("=" * 60)
    
    # Check configuration
    print(f"📋 Configuration:")
    print(f"   Database URL: {settings.DATABASE_URL}")
    print(f"   Environment: {settings.ENVIRONMENT}")
    print(f"   Debug Mode: {settings.DEBUG}")
    
    # Check database connection
    print(f"\n🔌 Checking database connection...")
    connection_ok = await check_database_connection()
    
    if not connection_ok:
        print("❌ Database connection failed!")
        print("   Please check:")
        print("   1. PostgreSQL is running")
        print("   2. Database credentials in .env file")
        print("   3. Database exists and is accessible")
        return False
    
    print("✅ Database connection successful")
    
    # Run migration
    print(f"\n🚀 Running database migration...")
    migration_success = await init_database()
    
    if migration_success:
        print("✅ Database migration completed successfully!")
        
        # Show created tables
        print(f"\n📊 Created Tables:")
        tables = [
            "market_data", "ticker_data", "market_stats",
            "users", "user_api_keys", "user_sessions", "user_notifications",
            "trades", "trade_signals", "portfolios", "portfolio_holdings",
            "system_config", "exchanges", "trading_pairs", "ai_models",
            "system_logs", "system_metrics"
        ]
        
        for table in tables:
            print(f"   ✓ {table}")
        
        print(f"\n🎯 Next Steps:")
        print("   1. Start the application: python start_dev.py")
        print("   2. Access API docs: http://localhost:8000/docs")
        print("   3. Test endpoints: python test_market_data.py")
        
        return True
    else:
        print("❌ Database migration failed!")
        print("   Check the logs above for error details")
        return False


async def verify_migration():
    """Verify migration was successful"""
    try:
        from app.core.database import SessionLocal
        from app.models import (
            MarketData, User, Trade, Portfolio, SystemConfig, Exchange
        )
        
        db = SessionLocal()
        
        # Test basic queries
        tests = [
            ("System Config", SystemConfig),
            ("Exchanges", Exchange),
            ("Users", User),
            ("Market Data", MarketData),
            ("Trades", Trade),
            ("Portfolios", Portfolio)
        ]
        
        print(f"\n🧪 Verifying Migration:")
        print("-" * 30)
        
        for test_name, model in tests:
            try:
                count = db.query(model).count()
                print(f"✅ {test_name}: {count} records")
            except Exception as e:
                print(f"❌ {test_name}: Error - {e}")
        
        db.close()
        return True
        
    except Exception as e:
        logger.error(f"Migration verification failed: {e}")
        return False


def main():
    """Main migration function"""
    try:
        # Run migration
        success = asyncio.run(run_migration())
        
        if success:
            # Verify migration
            asyncio.run(verify_migration())
            print(f"\n🎉 Migration completed successfully!")
            return 0
        else:
            print(f"\n💥 Migration failed!")
            return 1
            
    except KeyboardInterrupt:
        print("\n⏹️ Migration interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Migration error: {e}")
        print(f"\n❌ Migration failed with error: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

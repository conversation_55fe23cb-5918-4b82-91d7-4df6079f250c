"""
User Management Models for AI Crypto Trading System
Author: inkbytefo
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Text, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field, EmailStr
import uuid

from app.core.database import Base


class User(Base):
    """User accounts table"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    user_uuid = Column(String(36), unique=True, index=True, default=lambda: str(uuid.uuid4()))
    
    # Basic Information
    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(100), unique=True, nullable=False, index=True)
    full_name = Column(String(100))
    
    # Authentication
    hashed_password = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    is_admin = Column(Boolean, default=False)
    
    # Telegram Integration
    telegram_user_id = Column(String(50), unique=True, index=True)
    telegram_username = Column(String(50))
    telegram_chat_id = Column(String(50))
    
    # Trading Permissions
    trading_enabled = Column(Boolean, default=False)
    risk_level = Column(String(20), default="low")  # low, medium, high
    max_daily_trades = Column(Integer, default=10)
    max_position_size = Column(Float, default=0.1)  # 10% of portfolio
    
    # Metadata
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    last_login = Column(DateTime)
    
    # Relationships
    api_keys = relationship("UserAPIKey", back_populates="user", cascade="all, delete-orphan")
    portfolios = relationship("Portfolio", back_populates="user", cascade="all, delete-orphan")
    trades = relationship("Trade", back_populates="user", cascade="all, delete-orphan")
    notifications = relationship("UserNotification", back_populates="user", cascade="all, delete-orphan")
    walk_forward_analyses = relationship("WalkForwardAnalysis", back_populates="user", cascade="all, delete-orphan")


class UserAPIKey(Base):
    """User API keys for exchanges"""
    __tablename__ = "user_api_keys"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Exchange Information
    exchange = Column(String(20), nullable=False)  # binance, coinbase, etc.
    api_key = Column(Text, nullable=False)  # Encrypted
    secret_key = Column(Text, nullable=False)  # Encrypted
    passphrase = Column(Text)  # For some exchanges like Coinbase Pro
    
    # Configuration
    is_active = Column(Boolean, default=True)
    is_testnet = Column(Boolean, default=True)
    permissions = Column(Text)  # JSON string of permissions
    
    # Metadata
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    last_used = Column(DateTime)
    
    # Relationships
    user = relationship("User", back_populates="api_keys")

    def set_api_key(self, api_key: str):
        """Set API key with encryption"""
        from app.core.encryption import get_encryption_service
        encryption_service = get_encryption_service()
        if encryption_service:
            self.api_key = encryption_service.encrypt(api_key)
        else:
            # Fallback to plain text if encryption service is not available
            self.api_key = api_key

    def get_api_key(self) -> str:
        """Get decrypted API key"""
        from app.core.encryption import get_encryption_service
        encryption_service = get_encryption_service()
        if encryption_service:
            return encryption_service.decrypt_if_needed(self.api_key)
        else:
            return self.api_key

    def set_secret_key(self, secret_key: str):
        """Set secret key with encryption"""
        from app.core.encryption import get_encryption_service
        encryption_service = get_encryption_service()
        if encryption_service:
            self.secret_key = encryption_service.encrypt(secret_key)
        else:
            # Fallback to plain text if encryption service is not available
            self.secret_key = secret_key

    def get_secret_key(self) -> str:
        """Get decrypted secret key"""
        from app.core.encryption import get_encryption_service
        encryption_service = get_encryption_service()
        if encryption_service:
            return encryption_service.decrypt_if_needed(self.secret_key)
        else:
            return self.secret_key

    def set_passphrase(self, passphrase: str):
        """Set passphrase with encryption"""
        if not passphrase:
            self.passphrase = None
            return

        from app.core.encryption import get_encryption_service
        encryption_service = get_encryption_service()
        if encryption_service:
            self.passphrase = encryption_service.encrypt(passphrase)
        else:
            # Fallback to plain text if encryption service is not available
            self.passphrase = passphrase

    def get_passphrase(self) -> Optional[str]:
        """Get decrypted passphrase"""
        if not self.passphrase:
            return None

        from app.core.encryption import get_encryption_service
        encryption_service = get_encryption_service()
        if encryption_service:
            return encryption_service.decrypt_if_needed(self.passphrase)
        else:
            return self.passphrase


class UserSession(Base):
    """User session management"""
    __tablename__ = "user_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Session Information
    session_token = Column(String(255), unique=True, nullable=False, index=True)
    refresh_token = Column(String(255), unique=True, nullable=False)
    device_info = Column(Text)  # JSON string with device details
    ip_address = Column(String(45))  # IPv4 or IPv6
    user_agent = Column(Text)
    
    # Session Status
    is_active = Column(Boolean, default=True)
    expires_at = Column(DateTime, nullable=False)
    
    # Metadata
    created_at = Column(DateTime, default=func.now())
    last_activity = Column(DateTime, default=func.now())


class UserNotification(Base):
    """User notifications and alerts"""
    __tablename__ = "user_notifications"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Notification Content
    title = Column(String(200), nullable=False)
    message = Column(Text, nullable=False)
    notification_type = Column(String(50), nullable=False)  # trade, price_alert, system, etc.
    priority = Column(String(20), default="normal")  # low, normal, high, urgent
    
    # Notification Status
    is_read = Column(Boolean, default=False)
    is_sent = Column(Boolean, default=False)
    sent_via = Column(String(50))  # telegram, email, web
    
    # Related Data
    related_symbol = Column(String(20))
    related_trade_id = Column(Integer)
    extra_data = Column(Text)  # JSON string for additional data
    
    # Metadata
    created_at = Column(DateTime, default=func.now())
    read_at = Column(DateTime)
    sent_at = Column(DateTime)
    
    # Relationships
    user = relationship("User", back_populates="notifications")


# Pydantic models for API
class UserCreate(BaseModel):
    """User creation model"""
    username: str = Field(..., min_length=3, max_length=50)
    email: EmailStr
    full_name: Optional[str] = None
    password: str = Field(..., min_length=8)
    telegram_user_id: Optional[str] = None


class UserUpdate(BaseModel):
    """User update model"""
    full_name: Optional[str] = None
    telegram_username: Optional[str] = None
    risk_level: Optional[str] = Field(None, pattern="^(low|medium|high)$")
    max_daily_trades: Optional[int] = Field(None, ge=1, le=100)
    max_position_size: Optional[float] = Field(None, ge=0.01, le=1.0)


class UserResponse(BaseModel):
    """User response model"""
    id: int
    user_uuid: str
    username: str
    email: str
    full_name: Optional[str] = None
    is_active: bool
    is_verified: bool
    is_admin: bool
    telegram_username: Optional[str] = None
    trading_enabled: bool
    risk_level: str
    max_daily_trades: int
    max_position_size: float
    created_at: datetime
    last_login: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class APIKeyCreate(BaseModel):
    """API key creation model"""
    exchange: str
    api_key: str
    secret_key: str
    passphrase: Optional[str] = None
    is_testnet: bool = True


class APIKeyResponse(BaseModel):
    """API key response model (without sensitive data)"""
    id: int
    exchange: str
    is_active: bool
    is_testnet: bool
    created_at: datetime
    last_used: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class NotificationCreate(BaseModel):
    """Notification creation model"""
    title: str
    message: str
    notification_type: str
    priority: str = "normal"
    related_symbol: Optional[str] = None
    related_trade_id: Optional[int] = None


class NotificationResponse(BaseModel):
    """Notification response model"""
    id: int
    title: str
    message: str
    notification_type: str
    priority: str
    is_read: bool
    is_sent: bool
    related_symbol: Optional[str] = None
    created_at: datetime
    read_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

"""
Risk Management Service for AI Crypto Trading System
Author: inkbytefo

This service handles:
- Position sizing calculations
- Stop-loss optimization
- Portfolio risk assessment
- Drawdown protection
- Risk metrics calculation
"""

import logging
import asyncio
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import numpy as np
import pandas as pd

from app.core.cache import cache_manager
from app.services.market_data_service import market_data_service

logger = logging.getLogger(__name__)


class RiskLevel(Enum):
    """Risk level enumeration"""
    VERY_LOW = "very_low"
    LOW = "low"
    MODERATE = "moderate"
    HIGH = "high"
    VERY_HIGH = "very_high"


class PositionSizeMethod(Enum):
    """Position sizing methods"""
    FIXED_AMOUNT = "fixed_amount"
    FIXED_PERCENTAGE = "fixed_percentage"
    KELLY_CRITERION = "kelly_criterion"
    VOLATILITY_BASED = "volatility_based"
    ATR_BASED = "atr_based"


@dataclass
class RiskParameters:
    """Risk management parameters"""
    max_portfolio_risk: float = 0.02  # 2% max portfolio risk per trade
    max_position_size: float = 0.10   # 10% max position size
    max_daily_loss: float = 0.05      # 5% max daily loss
    max_drawdown: float = 0.15        # 15% max drawdown
    stop_loss_percentage: float = 0.02  # 2% stop loss
    take_profit_ratio: float = 2.0    # 2:1 risk/reward ratio
    correlation_limit: float = 0.7    # Max correlation between positions
    volatility_lookback: int = 20     # Days for volatility calculation


@dataclass
class PositionSizeResult:
    """Position sizing calculation result"""
    recommended_size: float
    max_size: float
    risk_amount: float
    stop_loss_price: float
    take_profit_price: float
    risk_reward_ratio: float
    confidence_score: float
    method_used: str
    warnings: List[str]


@dataclass
class RiskAssessment:
    """Portfolio risk assessment result"""
    overall_risk_level: RiskLevel
    portfolio_var: float  # Value at Risk
    max_drawdown: float
    sharpe_ratio: float
    correlation_matrix: Dict[str, Dict[str, float]]
    position_risks: Dict[str, float]
    recommendations: List[str]
    risk_score: float  # 0-100


class RiskManagementService:
    """Risk Management Service"""
    
    def __init__(self):
        self.risk_params = RiskParameters()
        self.cache_ttl = 300  # 5 minutes
        
    async def calculate_position_size(
        self,
        symbol: str,
        entry_price: float,
        portfolio_value: float,
        signal_confidence: float = 0.7,
        method: PositionSizeMethod = PositionSizeMethod.VOLATILITY_BASED,
        custom_risk_params: Optional[RiskParameters] = None
    ) -> PositionSizeResult:
        """Calculate optimal position size based on risk parameters"""
        try:
            params = custom_risk_params or self.risk_params
            warnings = []
            
            # Get market data for volatility calculation
            market_data = await market_data_service.get_ohlcv_data(
                symbol=symbol,
                timeframe="1d",
                limit=params.volatility_lookback + 10
            )
            
            if market_data is None or len(market_data) < params.volatility_lookback:
                warnings.append("Insufficient market data for volatility calculation")
                volatility = 0.02  # Default 2% daily volatility
            else:
                # Calculate historical volatility
                returns = market_data['close'].pct_change().dropna()
                volatility = returns.std()
            
            # Calculate ATR for stop loss
            atr = await self._calculate_atr(market_data) if market_data is not None else entry_price * 0.02
            
            # Calculate position size based on method
            if method == PositionSizeMethod.FIXED_PERCENTAGE:
                position_value = portfolio_value * params.max_position_size
            elif method == PositionSizeMethod.VOLATILITY_BASED:
                # Adjust position size based on volatility
                volatility_factor = min(1.0, 0.02 / max(volatility, 0.005))  # Target 2% volatility
                position_value = portfolio_value * params.max_position_size * volatility_factor
            elif method == PositionSizeMethod.ATR_BASED:
                # Position size based on ATR and risk tolerance
                risk_amount = portfolio_value * params.max_portfolio_risk
                stop_distance = atr * 2  # 2x ATR stop loss
                position_value = risk_amount / (stop_distance / entry_price)
            elif method == PositionSizeMethod.KELLY_CRITERION:
                # Kelly Criterion (simplified)
                win_rate = signal_confidence
                avg_win = params.take_profit_ratio * params.stop_loss_percentage
                avg_loss = params.stop_loss_percentage
                kelly_fraction = (win_rate * avg_win - (1 - win_rate) * avg_loss) / avg_win
                kelly_fraction = max(0, min(kelly_fraction, params.max_position_size))
                position_value = portfolio_value * kelly_fraction
            else:  # FIXED_AMOUNT
                position_value = portfolio_value * params.max_position_size
            
            # Apply maximum position size limit
            max_position_value = portfolio_value * params.max_position_size
            position_value = min(position_value, max_position_value)
            
            # Calculate position size in units
            recommended_size = position_value / entry_price
            max_size = max_position_value / entry_price
            
            # Calculate stop loss and take profit
            stop_loss_distance = max(atr * 1.5, entry_price * params.stop_loss_percentage)
            stop_loss_price = entry_price - stop_loss_distance
            take_profit_price = entry_price + (stop_loss_distance * params.take_profit_ratio)
            
            # Calculate risk amount
            risk_amount = recommended_size * stop_loss_distance
            
            # Calculate risk/reward ratio
            risk_reward_ratio = (take_profit_price - entry_price) / (entry_price - stop_loss_price)
            
            # Confidence score based on various factors
            confidence_score = self._calculate_confidence_score(
                signal_confidence, volatility, risk_reward_ratio, position_value / portfolio_value
            )
            
            # Add warnings
            if position_value / portfolio_value > params.max_position_size * 0.8:
                warnings.append("Position size approaching maximum limit")
            if volatility > 0.05:  # 5% daily volatility
                warnings.append("High volatility detected - consider reducing position size")
            if risk_reward_ratio < 1.5:
                warnings.append("Risk/reward ratio below recommended 1.5:1")
            
            return PositionSizeResult(
                recommended_size=recommended_size,
                max_size=max_size,
                risk_amount=risk_amount,
                stop_loss_price=stop_loss_price,
                take_profit_price=take_profit_price,
                risk_reward_ratio=risk_reward_ratio,
                confidence_score=confidence_score,
                method_used=method.value,
                warnings=warnings
            )
            
        except Exception as e:
            logger.error(f"Error calculating position size for {symbol}: {e}")
            # Return conservative default
            return PositionSizeResult(
                recommended_size=0,
                max_size=0,
                risk_amount=0,
                stop_loss_price=entry_price * 0.98,
                take_profit_price=entry_price * 1.04,
                risk_reward_ratio=2.0,
                confidence_score=0.0,
                method_used="error_fallback",
                warnings=["Error in calculation - using conservative defaults"]
            )
    
    async def _calculate_atr(self, df: pd.DataFrame, period: int = 14) -> float:
        """Calculate Average True Range"""
        try:
            if df is None or len(df) < period:
                return 0.0
            
            high = df['high']
            low = df['low']
            close = df['close']
            
            # True Range calculation
            tr1 = high - low
            tr2 = abs(high - close.shift(1))
            tr3 = abs(low - close.shift(1))
            
            true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            atr = true_range.rolling(window=period).mean().iloc[-1]
            
            return float(atr) if not pd.isna(atr) else 0.0
            
        except Exception as e:
            logger.error(f"Error calculating ATR: {e}")
            return 0.0
    
    def _calculate_confidence_score(
        self,
        signal_confidence: float,
        volatility: float,
        risk_reward_ratio: float,
        position_ratio: float
    ) -> float:
        """Calculate overall confidence score for position sizing"""
        try:
            # Base confidence from signal
            confidence = signal_confidence
            
            # Adjust for volatility (lower volatility = higher confidence)
            volatility_factor = max(0.5, 1 - (volatility - 0.02) * 10)  # Penalize high volatility
            confidence *= volatility_factor
            
            # Adjust for risk/reward ratio
            rr_factor = min(1.0, risk_reward_ratio / 2.0)  # Reward good risk/reward
            confidence *= rr_factor
            
            # Adjust for position size (smaller positions = higher confidence)
            size_factor = max(0.7, 1 - position_ratio)  # Penalize large positions
            confidence *= size_factor
            
            return max(0.0, min(1.0, confidence))
            
        except Exception as e:
            logger.error(f"Error calculating confidence score: {e}")
            return 0.5
    
    async def assess_portfolio_risk(
        self,
        portfolio_holdings: Dict[str, Dict[str, float]],
        portfolio_value: float,
        timeframe: str = "1d",
        lookback_days: int = 30
    ) -> RiskAssessment:
        """Assess overall portfolio risk"""
        try:
            cache_key = f"portfolio_risk:{hash(str(portfolio_holdings))}:{timeframe}"
            cached_result = await cache_manager.get(cache_key)
            
            if cached_result:
                logger.debug("Using cached portfolio risk assessment")
                return cached_result
            
            symbols = list(portfolio_holdings.keys())
            position_risks = {}
            correlation_matrix = {}
            
            # Calculate individual position risks
            for symbol, holding in portfolio_holdings.items():
                position_value = holding.get('value', 0)
                position_risk = position_value / portfolio_value
                position_risks[symbol] = position_risk
            
            # Calculate correlation matrix if multiple positions
            if len(symbols) > 1:
                correlation_matrix = await self._calculate_correlation_matrix(symbols, lookback_days)
            
            # Calculate portfolio VaR (Value at Risk)
            portfolio_var = await self._calculate_portfolio_var(
                portfolio_holdings, portfolio_value, lookback_days
            )
            
            # Calculate max drawdown
            max_drawdown = await self._calculate_max_drawdown(symbols, lookback_days)
            
            # Calculate Sharpe ratio
            sharpe_ratio = await self._calculate_sharpe_ratio(symbols, portfolio_holdings, lookback_days)
            
            # Determine overall risk level
            risk_score = self._calculate_risk_score(
                portfolio_var, max_drawdown, position_risks, correlation_matrix
            )
            overall_risk_level = self._determine_risk_level(risk_score)
            
            # Generate recommendations
            recommendations = self._generate_risk_recommendations(
                position_risks, correlation_matrix, portfolio_var, max_drawdown
            )
            
            result = RiskAssessment(
                overall_risk_level=overall_risk_level,
                portfolio_var=portfolio_var,
                max_drawdown=max_drawdown,
                sharpe_ratio=sharpe_ratio,
                correlation_matrix=correlation_matrix,
                position_risks=position_risks,
                recommendations=recommendations,
                risk_score=risk_score
            )
            
            # Cache result
            await cache_manager.set(cache_key, result, ttl=self.cache_ttl)
            
            return result
            
        except Exception as e:
            logger.error(f"Error assessing portfolio risk: {e}")
            return RiskAssessment(
                overall_risk_level=RiskLevel.MODERATE,
                portfolio_var=0.05,
                max_drawdown=0.10,
                sharpe_ratio=0.0,
                correlation_matrix={},
                position_risks={},
                recommendations=["Error in risk assessment - manual review recommended"],
                risk_score=50.0
            )


    async def _calculate_correlation_matrix(
        self,
        symbols: List[str],
        lookback_days: int = 30
    ) -> Dict[str, Dict[str, float]]:
        """Calculate correlation matrix between symbols"""
        try:
            correlation_matrix = {}

            # Get price data for all symbols
            price_data = {}
            for symbol in symbols:
                data = await market_data_service.get_ohlcv_data(
                    symbol=symbol,
                    timeframe="1d",
                    limit=lookback_days + 5
                )
                if data is not None and len(data) >= lookback_days:
                    price_data[symbol] = data['close'].pct_change().dropna()

            # Calculate correlations
            for symbol1 in symbols:
                correlation_matrix[symbol1] = {}
                for symbol2 in symbols:
                    if symbol1 == symbol2:
                        correlation_matrix[symbol1][symbol2] = 1.0
                    elif symbol1 in price_data and symbol2 in price_data:
                        # Align data by index
                        common_index = price_data[symbol1].index.intersection(price_data[symbol2].index)
                        if len(common_index) >= 10:  # Minimum data points
                            corr = price_data[symbol1].loc[common_index].corr(
                                price_data[symbol2].loc[common_index]
                            )
                            correlation_matrix[symbol1][symbol2] = float(corr) if not pd.isna(corr) else 0.0
                        else:
                            correlation_matrix[symbol1][symbol2] = 0.0
                    else:
                        correlation_matrix[symbol1][symbol2] = 0.0

            return correlation_matrix

        except Exception as e:
            logger.error(f"Error calculating correlation matrix: {e}")
            return {}

    async def _calculate_portfolio_var(
        self,
        portfolio_holdings: Dict[str, Dict[str, float]],
        portfolio_value: float,
        lookback_days: int = 30,
        confidence_level: float = 0.95
    ) -> float:
        """Calculate Portfolio Value at Risk"""
        try:
            portfolio_returns = []

            # Get historical returns for portfolio
            for i in range(lookback_days):
                daily_return = 0.0

                for symbol, holding in portfolio_holdings.items():
                    weight = holding.get('value', 0) / portfolio_value

                    # Get price data
                    data = await market_data_service.get_ohlcv_data(
                        symbol=symbol,
                        timeframe="1d",
                        limit=lookback_days + 5
                    )

                    if data is not None and len(data) > i + 1:
                        price_return = (data['close'].iloc[-(i+1)] - data['close'].iloc[-(i+2)]) / data['close'].iloc[-(i+2)]
                        daily_return += weight * price_return

                portfolio_returns.append(daily_return)

            if portfolio_returns:
                # Calculate VaR at confidence level
                portfolio_returns = np.array(portfolio_returns)
                var_percentile = (1 - confidence_level) * 100
                var = np.percentile(portfolio_returns, var_percentile)
                return abs(float(var))

            return 0.05  # Default 5% VaR

        except Exception as e:
            logger.error(f"Error calculating portfolio VaR: {e}")
            return 0.05

    async def _calculate_max_drawdown(self, symbols: List[str], lookback_days: int = 30) -> float:
        """Calculate maximum drawdown for portfolio"""
        try:
            max_drawdown = 0.0

            for symbol in symbols:
                data = await market_data_service.get_ohlcv_data(
                    symbol=symbol,
                    timeframe="1d",
                    limit=lookback_days + 5
                )

                if data is not None and len(data) >= lookback_days:
                    prices = data['close']

                    # Calculate running maximum
                    running_max = prices.expanding().max()

                    # Calculate drawdown
                    drawdown = (prices - running_max) / running_max

                    # Get maximum drawdown
                    symbol_max_dd = abs(drawdown.min())
                    max_drawdown = max(max_drawdown, symbol_max_dd)

            return float(max_drawdown) if max_drawdown > 0 else 0.10

        except Exception as e:
            logger.error(f"Error calculating max drawdown: {e}")
            return 0.10

    async def _calculate_sharpe_ratio(
        self,
        symbols: List[str],
        portfolio_holdings: Dict[str, Dict[str, float]],
        lookback_days: int = 30,
        risk_free_rate: float = 0.02
    ) -> float:
        """Calculate Sharpe ratio for portfolio"""
        try:
            portfolio_returns = []
            total_value = sum(holding.get('value', 0) for holding in portfolio_holdings.values())

            for i in range(lookback_days):
                daily_return = 0.0

                for symbol, holding in portfolio_holdings.items():
                    weight = holding.get('value', 0) / total_value if total_value > 0 else 0

                    data = await market_data_service.get_ohlcv_data(
                        symbol=symbol,
                        timeframe="1d",
                        limit=lookback_days + 5
                    )

                    if data is not None and len(data) > i + 1:
                        price_return = (data['close'].iloc[-(i+1)] - data['close'].iloc[-(i+2)]) / data['close'].iloc[-(i+2)]
                        daily_return += weight * price_return

                portfolio_returns.append(daily_return)

            if portfolio_returns:
                returns_array = np.array(portfolio_returns)
                excess_returns = returns_array - (risk_free_rate / 365)  # Daily risk-free rate

                if np.std(excess_returns) > 0:
                    sharpe = np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(365)  # Annualized
                    return float(sharpe)

            return 0.0

        except Exception as e:
            logger.error(f"Error calculating Sharpe ratio: {e}")
            return 0.0

    def _calculate_risk_score(
        self,
        portfolio_var: float,
        max_drawdown: float,
        position_risks: Dict[str, float],
        correlation_matrix: Dict[str, Dict[str, float]]
    ) -> float:
        """Calculate overall risk score (0-100)"""
        try:
            risk_score = 0.0

            # VaR component (0-30 points)
            var_score = min(30, portfolio_var * 600)  # 5% VaR = 30 points
            risk_score += var_score

            # Drawdown component (0-25 points)
            dd_score = min(25, max_drawdown * 250)  # 10% DD = 25 points
            risk_score += dd_score

            # Concentration risk (0-25 points)
            max_position = max(position_risks.values()) if position_risks else 0
            concentration_score = min(25, max_position * 250)  # 10% position = 25 points
            risk_score += concentration_score

            # Correlation risk (0-20 points)
            if correlation_matrix:
                avg_correlation = 0.0
                count = 0
                for symbol1, correlations in correlation_matrix.items():
                    for symbol2, corr in correlations.items():
                        if symbol1 != symbol2:
                            avg_correlation += abs(corr)
                            count += 1

                if count > 0:
                    avg_correlation /= count
                    correlation_score = min(20, avg_correlation * 20)  # 100% correlation = 20 points
                    risk_score += correlation_score

            return min(100.0, risk_score)

        except Exception as e:
            logger.error(f"Error calculating risk score: {e}")
            return 50.0

    def _determine_risk_level(self, risk_score: float) -> RiskLevel:
        """Determine risk level based on risk score"""
        if risk_score <= 20:
            return RiskLevel.VERY_LOW
        elif risk_score <= 40:
            return RiskLevel.LOW
        elif risk_score <= 60:
            return RiskLevel.MODERATE
        elif risk_score <= 80:
            return RiskLevel.HIGH
        else:
            return RiskLevel.VERY_HIGH

    def _generate_risk_recommendations(
        self,
        position_risks: Dict[str, float],
        correlation_matrix: Dict[str, Dict[str, float]],
        portfolio_var: float,
        max_drawdown: float
    ) -> List[str]:
        """Generate risk management recommendations"""
        recommendations = []

        try:
            # Check position concentration
            max_position = max(position_risks.values()) if position_risks else 0
            if max_position > 0.15:  # 15%
                recommendations.append(f"Consider reducing largest position ({max_position:.1%} of portfolio)")

            # Check correlations
            if correlation_matrix:
                high_correlations = []
                for symbol1, correlations in correlation_matrix.items():
                    for symbol2, corr in correlations.items():
                        if symbol1 != symbol2 and abs(corr) > 0.7:
                            high_correlations.append((symbol1, symbol2, corr))

                if high_correlations:
                    recommendations.append("High correlation detected between some positions - consider diversification")

            # Check VaR
            if portfolio_var > 0.05:  # 5%
                recommendations.append(f"Portfolio VaR is high ({portfolio_var:.1%}) - consider reducing position sizes")

            # Check drawdown
            if max_drawdown > 0.15:  # 15%
                recommendations.append(f"Maximum drawdown is concerning ({max_drawdown:.1%}) - review stop-loss levels")

            # General recommendations
            if not recommendations:
                recommendations.append("Portfolio risk levels are within acceptable ranges")

            return recommendations

        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            return ["Error generating recommendations - manual review suggested"]

    async def optimize_stop_loss(
        self,
        symbol: str,
        entry_price: float,
        position_size: float,
        max_loss_percentage: float = 0.02
    ) -> Dict[str, float]:
        """Optimize stop-loss level based on market conditions"""
        try:
            # Get market data for ATR calculation
            data = await market_data_service.get_ohlcv_data(
                symbol=symbol,
                timeframe="1h",
                limit=100
            )

            if data is None:
                # Fallback to percentage-based stop loss
                return {
                    "stop_loss_price": entry_price * (1 - max_loss_percentage),
                    "stop_loss_percentage": max_loss_percentage,
                    "method": "percentage_fallback"
                }

            # Calculate ATR-based stop loss
            atr = await self._calculate_atr(data, period=14)
            atr_multiplier = 2.0  # 2x ATR
            atr_stop_distance = atr * atr_multiplier
            atr_stop_price = entry_price - atr_stop_distance

            # Calculate percentage-based stop loss
            percentage_stop_price = entry_price * (1 - max_loss_percentage)

            # Use the more conservative (closer to entry) stop loss
            final_stop_price = max(atr_stop_price, percentage_stop_price)
            final_stop_percentage = (entry_price - final_stop_price) / entry_price

            return {
                "stop_loss_price": final_stop_price,
                "stop_loss_percentage": final_stop_percentage,
                "atr_stop_price": atr_stop_price,
                "percentage_stop_price": percentage_stop_price,
                "atr_value": atr,
                "method": "optimized"
            }

        except Exception as e:
            logger.error(f"Error optimizing stop loss for {symbol}: {e}")
            return {
                "stop_loss_price": entry_price * (1 - max_loss_percentage),
                "stop_loss_percentage": max_loss_percentage,
                "method": "error_fallback"
            }


# Create global instance
risk_management_service = RiskManagementService()

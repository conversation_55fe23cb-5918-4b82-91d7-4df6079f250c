"""
Database Integration Tests
Author: inkbytefo
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from app.core.database_init import (
    create_database_tables,
    create_admin_user,
    create_default_system_config,
    initialize_database,
    check_database_connection
)
from app.models.user import User, UserAPIKey
from app.models.trading import Portfolio, PortfolioHolding
from app.models.system import SystemConfig, Exchange


class TestDatabaseInitialization:
    """Test database initialization functionality"""
    
    @pytest.fixture
    def mock_db_session(self):
        """Mock database session"""
        session = MagicMock(spec=Session)
        session.query.return_value = session
        session.filter.return_value = session
        session.first.return_value = None
        session.count.return_value = 0
        session.all.return_value = []
        session.add = MagicMock()
        session.commit = MagicMock()
        session.refresh = MagicMock()
        session.close = MagicMock()
        return session
    
    @pytest.fixture
    def mock_get_db(self, mock_db_session):
        """Mock get_db dependency"""
        def mock_generator():
            yield mock_db_session
        return mock_generator
    
    @pytest.mark.asyncio
    async def test_create_database_tables(self):
        """Test database table creation"""
        with patch('app.core.database_init.Base') as mock_base:
            mock_base.metadata.create_all = MagicMock()
            
            result = await create_database_tables()
            
            assert result is True
            mock_base.metadata.create_all.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_admin_user_success(self, mock_get_db, mock_db_session):
        """Test successful admin user creation"""
        with patch('app.core.database_init.get_db', return_value=mock_get_db()):
            with patch('app.core.security.get_password_hash', return_value="hashed_password"):
                
                result = await create_admin_user(
                    username="testadmin",
                    email="<EMAIL>",
                    password="testpass123"
                )
                
                assert result is True
                mock_db_session.add.assert_called_once()
                mock_db_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_admin_user_already_exists(self, mock_get_db, mock_db_session):
        """Test admin user creation when user already exists"""
        # Mock existing user
        existing_user = MagicMock(spec=User)
        mock_db_session.first.return_value = existing_user
        
        with patch('app.core.database_init.get_db', return_value=mock_get_db()):
            result = await create_admin_user()
            
            assert result is True
            mock_db_session.add.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_create_default_system_config(self, mock_get_db, mock_db_session):
        """Test default system configuration creation"""
        with patch('app.core.database_init.get_db', return_value=mock_get_db()):
            result = await create_default_system_config()
            
            assert result is True
            # Should add multiple config entries
            assert mock_db_session.add.call_count >= 5
            mock_db_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_check_database_connection_success(self, mock_get_db, mock_db_session):
        """Test successful database connection check"""
        mock_result = MagicMock()
        mock_result.fetchone.return_value = (1,)
        mock_db_session.execute.return_value = mock_result
        
        with patch('app.core.database_init.get_db', return_value=mock_get_db()):
            result = await check_database_connection()
            
            assert result is True
            mock_db_session.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_check_database_connection_failure(self, mock_get_db, mock_db_session):
        """Test database connection failure"""
        mock_db_session.execute.side_effect = Exception("Connection failed")
        
        with patch('app.core.database_init.get_db', return_value=mock_get_db()):
            result = await check_database_connection()
            
            assert result is False
    
    @pytest.mark.asyncio
    async def test_initialize_database_complete(self):
        """Test complete database initialization"""
        with patch('app.core.database_init.create_database_tables', return_value=True):
            with patch('app.core.database_init.create_admin_user', return_value=True):
                with patch('app.core.database_init.create_default_system_config', return_value=True):
                    with patch('app.core.database_init.create_default_exchanges', return_value=True):
                        
                        result = await initialize_database()
                        
                        assert result is True
    
    @pytest.mark.asyncio
    async def test_initialize_database_failure(self):
        """Test database initialization failure"""
        with patch('app.core.database_init.create_database_tables', return_value=False):
            result = await initialize_database()
            
            assert result is False


class TestUserDatabaseOperations:
    """Test user-related database operations"""
    
    @pytest.fixture
    def mock_user(self):
        """Mock user object"""
        user = MagicMock(spec=User)
        user.id = 1
        user.username = "testuser"
        user.email = "<EMAIL>"
        user.is_active = True
        user.is_admin = False
        user.trading_enabled = True
        return user
    
    @pytest.fixture
    def mock_db_session(self):
        """Mock database session"""
        session = MagicMock(spec=Session)
        session.query.return_value = session
        session.filter.return_value = session
        session.first.return_value = None
        session.all.return_value = []
        session.add = MagicMock()
        session.commit = MagicMock()
        session.refresh = MagicMock()
        session.close = MagicMock()
        return session
    
    def test_user_api_key_encryption(self):
        """Test API key encryption/decryption"""
        with patch('app.core.encryption.get_encryption_service') as mock_encryption:
            mock_service = MagicMock()
            mock_service.encrypt.return_value = "encrypted_key"
            mock_service.decrypt_if_needed.return_value = "decrypted_key"
            mock_encryption.return_value = mock_service
            
            api_key = UserAPIKey()
            
            # Test encryption
            api_key.set_api_key("test_api_key")
            assert api_key.api_key == "encrypted_key"
            
            # Test decryption
            decrypted = api_key.get_api_key()
            assert decrypted == "decrypted_key"
    
    def test_user_api_key_fallback(self):
        """Test API key fallback when encryption service unavailable"""
        with patch('app.core.encryption.get_encryption_service', return_value=None):
            api_key = UserAPIKey()
            
            # Test fallback to plain text
            api_key.set_api_key("test_api_key")
            assert api_key.api_key == "test_api_key"
            
            decrypted = api_key.get_api_key()
            assert decrypted == "test_api_key"


class TestPortfolioDatabaseOperations:
    """Test portfolio-related database operations"""
    
    @pytest.fixture
    def mock_portfolio(self):
        """Mock portfolio object"""
        portfolio = MagicMock(spec=Portfolio)
        portfolio.id = 1
        portfolio.user_id = 1
        portfolio.name = "Test Portfolio"
        portfolio.exchange = "binance"
        portfolio.total_value_usd = 1000.0
        return portfolio
    
    @pytest.fixture
    def mock_holding(self):
        """Mock portfolio holding"""
        holding = MagicMock(spec=PortfolioHolding)
        holding.id = 1
        holding.portfolio_id = 1
        holding.symbol = "BTCUSDT"
        holding.quantity = 0.1
        holding.average_buy_price = 45000.0
        holding.current_price = 46000.0
        return holding
    
    @pytest.mark.asyncio
    async def test_portfolio_sync_integration(self):
        """Test portfolio synchronization with exchange"""
        from app.services.portfolio_management_service import PortfolioManagementService
        
        service = PortfolioManagementService()
        
        # Mock exchange balance data
        exchange_balance = {
            'total_value_usd': 1500.0,
            'balances': {
                'BTC': {'total': 0.15, 'free': 0.15, 'used': 0.0},
                'ETH': {'total': 2.5, 'free': 2.5, 'used': 0.0}
            }
        }
        
        with patch.object(service, '_get_exchange_balance', return_value=exchange_balance):
            with patch.object(service, '_update_portfolio_holdings') as mock_update:
                
                result = await service.sync_portfolio_with_exchange(
                    user_id=1,
                    portfolio_id=1
                )
                
                assert result['status'] == 'success'
                assert result['total_value_usd'] == 1500.0
                mock_update.assert_called_once()


class TestSystemConfigDatabase:
    """Test system configuration database operations"""
    
    @pytest.fixture
    def mock_config(self):
        """Mock system config"""
        config = MagicMock(spec=SystemConfig)
        config.key = "test_key"
        config.value = "test_value"
        config.is_active = True
        return config
    
    def test_system_config_creation(self, mock_config):
        """Test system configuration creation"""
        assert mock_config.key == "test_key"
        assert mock_config.value == "test_value"
        assert mock_config.is_active is True


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

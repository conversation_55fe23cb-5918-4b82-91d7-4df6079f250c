"""
Test Configuration and Fixtures
Author: inkbytefo

This module provides pytest fixtures and configuration for the test suite.
"""

import os
import asyncio
import pytest
import pytest_asyncio
from typing import AsyncGenerator, Generator
from unittest.mock import Mock, AsyncMock
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

# Import application modules
from app.core.database import Base, get_database
from app.core.config import settings
from app.models import User, Portfolio, Trade, MarketData
from app.services.market_data_service import MarketDataService
from app.services.risk_management_service import RiskManagementService
from app.services.trading_engine_service import TradingEngineService
from app.services.portfolio_management_service import PortfolioManagementService

# Test database URL
TEST_DATABASE_URL = "sqlite:///./test.db"


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
def test_db():
    """Create a test database session."""
    # Create test database engine
    engine = create_engine(
        TEST_DATABASE_URL,
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )
    
    # Create all tables
    Base.metadata.create_all(bind=engine)
    
    # Create session
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    session = TestingSessionLocal()
    
    try:
        yield session
    finally:
        session.close()
        # Drop all tables
        Base.metadata.drop_all(bind=engine)


@pytest.fixture
def override_get_db(test_db):
    """Override the get_database dependency."""
    def _override_get_db():
        try:
            yield test_db
        finally:
            pass
    return _override_get_db


@pytest.fixture
def sample_user(test_db) -> User:
    """Create a sample user for testing."""
    user = User(
        email="<EMAIL>",
        username="testuser",
        telegram_user_id=123456789,
        is_active=True,
        risk_tolerance="medium"
    )
    test_db.add(user)
    test_db.commit()
    test_db.refresh(user)
    return user


@pytest.fixture
def sample_portfolio(test_db, sample_user) -> Portfolio:
    """Create a sample portfolio for testing."""
    portfolio = Portfolio(
        user_id=sample_user.id,
        name="Test Portfolio",
        initial_balance=10000.0,
        current_balance=10000.0,
        is_active=True
    )
    test_db.add(portfolio)
    test_db.commit()
    test_db.refresh(portfolio)
    return portfolio


@pytest.fixture
def sample_market_data(test_db) -> MarketData:
    """Create sample market data for testing."""
    market_data = MarketData(
        symbol="BTC/USDT",
        timeframe="1h",
        timestamp="2024-01-01T00:00:00",
        open_price=50000.0,
        high_price=51000.0,
        low_price=49000.0,
        close_price=50500.0,
        volume=1000.0
    )
    test_db.add(market_data)
    test_db.commit()
    test_db.refresh(market_data)
    return market_data


@pytest.fixture
def sample_trade(test_db, sample_user, sample_portfolio) -> Trade:
    """Create a sample trade for testing."""
    trade = Trade(
        user_id=sample_user.id,
        portfolio_id=sample_portfolio.id,
        symbol="BTC/USDT",
        trade_type="buy",
        order_type="market",
        quantity=0.1,
        price=50000.0,
        status="completed",
        exchange="binance"
    )
    test_db.add(trade)
    test_db.commit()
    test_db.refresh(trade)
    return trade


@pytest_asyncio.fixture
async def mock_market_data_service():
    """Create a mock market data service."""
    service = Mock(spec=MarketDataService)
    
    # Mock methods
    service.get_ticker_data = AsyncMock(return_value={
        'symbol': 'BTC/USDT',
        'last': 50000.0,
        'bid': 49950.0,
        'ask': 50050.0,
        'volume': 1000.0,
        'timestamp': '2024-01-01T00:00:00Z'
    })
    
    service.get_ohlcv_data = AsyncMock(return_value=[
        [1704067200000, 50000.0, 51000.0, 49000.0, 50500.0, 1000.0]
    ])
    
    service.get_orderbook_data = AsyncMock(return_value={
        'bids': [[49950.0, 1.0], [49900.0, 2.0]],
        'asks': [[50050.0, 1.0], [50100.0, 2.0]]
    })
    
    return service


@pytest_asyncio.fixture
async def mock_risk_management_service():
    """Create a mock risk management service."""
    service = Mock(spec=RiskManagementService)
    
    service.calculate_position_size = AsyncMock(return_value={
        'position_size': 0.1,
        'risk_amount': 100.0,
        'max_loss': 50.0,
        'confidence': 0.8
    })
    
    service.assess_trade_risk = AsyncMock(return_value={
        'risk_score': 0.3,
        'risk_level': 'low',
        'recommendations': ['Use stop loss', 'Monitor closely']
    })
    
    return service


@pytest_asyncio.fixture
async def mock_trading_engine_service():
    """Create a mock trading engine service."""
    service = Mock(spec=TradingEngineService)
    
    service.execute_trade_signal = AsyncMock(return_value={
        'success': True,
        'trade_id': 'test_trade_123',
        'order_id': 'order_456',
        'status': 'filled',
        'executed_price': 50000.0,
        'executed_quantity': 0.1
    })
    
    service.place_order = AsyncMock(return_value={
        'order_id': 'order_123',
        'status': 'pending',
        'symbol': 'BTC/USDT',
        'side': 'buy',
        'amount': 0.1,
        'price': 50000.0
    })
    
    return service


@pytest_asyncio.fixture
async def mock_portfolio_service():
    """Create a mock portfolio management service."""
    service = Mock(spec=PortfolioManagementService)
    
    service.get_portfolio_summary = AsyncMock(return_value={
        'total_value': 10000.0,
        'available_balance': 5000.0,
        'invested_amount': 5000.0,
        'total_pnl': 500.0,
        'pnl_percentage': 5.0,
        'positions': []
    })
    
    service.update_portfolio_balance = AsyncMock(return_value=True)
    
    return service


@pytest.fixture
def mock_binance_client():
    """Create a mock Binance client."""
    client = Mock()
    
    # Mock ticker data
    client.fetch_ticker = AsyncMock(return_value={
        'symbol': 'BTC/USDT',
        'last': 50000.0,
        'bid': 49950.0,
        'ask': 50050.0,
        'volume': 1000.0
    })
    
    # Mock OHLCV data
    client.fetch_ohlcv = AsyncMock(return_value=[
        [1704067200000, 50000.0, 51000.0, 49000.0, 50500.0, 1000.0]
    ])
    
    # Mock order placement
    client.create_market_buy_order = AsyncMock(return_value={
        'id': 'order_123',
        'symbol': 'BTC/USDT',
        'side': 'buy',
        'amount': 0.1,
        'price': 50000.0,
        'status': 'closed',
        'filled': 0.1
    })
    
    return client


@pytest.fixture
def sample_trade_signal():
    """Create a sample trade signal for testing."""
    return {
        'symbol': 'BTC/USDT',
        'action': 'buy',
        'confidence': 0.8,
        'entry_price': 50000.0,
        'stop_loss': 48000.0,
        'take_profit': 55000.0,
        'reasoning': 'Strong bullish pattern detected',
        'timestamp': '2024-01-01T00:00:00Z'
    }


@pytest.fixture
def sample_market_analysis():
    """Create sample market analysis data."""
    return {
        'symbol': 'BTC/USDT',
        'trend': 'bullish',
        'support_levels': [48000.0, 46000.0],
        'resistance_levels': [52000.0, 55000.0],
        'rsi': 65.0,
        'macd': {
            'macd': 500.0,
            'signal': 450.0,
            'histogram': 50.0
        },
        'bollinger_bands': {
            'upper': 52000.0,
            'middle': 50000.0,
            'lower': 48000.0
        },
        'volume_analysis': 'above_average',
        'sentiment': 'positive'
    }


# Test utilities
class TestUtils:
    """Utility functions for testing."""
    
    @staticmethod
    def create_sample_ohlcv_data(symbol: str, count: int = 100):
        """Create sample OHLCV data for testing."""
        import random
        from datetime import datetime, timedelta
        
        data = []
        base_price = 50000.0
        timestamp = datetime.now() - timedelta(hours=count)
        
        for i in range(count):
            open_price = base_price + random.uniform(-1000, 1000)
            high_price = open_price + random.uniform(0, 500)
            low_price = open_price - random.uniform(0, 500)
            close_price = open_price + random.uniform(-200, 200)
            volume = random.uniform(100, 1000)
            
            data.append([
                int(timestamp.timestamp() * 1000),
                open_price,
                high_price,
                low_price,
                close_price,
                volume
            ])
            
            timestamp += timedelta(hours=1)
            base_price = close_price
        
        return data
    
    @staticmethod
    def assert_trade_signal_valid(signal: dict):
        """Assert that a trade signal has valid structure."""
        required_fields = ['symbol', 'action', 'confidence', 'entry_price']
        for field in required_fields:
            assert field in signal, f"Missing required field: {field}"
        
        assert signal['action'] in ['buy', 'sell'], "Invalid action"
        assert 0 <= signal['confidence'] <= 1, "Invalid confidence value"
        assert signal['entry_price'] > 0, "Invalid entry price"


@pytest.fixture
def test_utils():
    """Provide test utilities."""
    return TestUtils

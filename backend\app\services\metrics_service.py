"""
Prometheus Metrics Service
Comprehensive system metrics collection and monitoring
Author: inkbytefo
"""

import time
import logging
import asyncio
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime, timedelta
from functools import wraps
from dataclasses import dataclass, field
import psutil
import threading
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

@dataclass
class MetricValue:
    """Individual metric value with timestamp"""
    value: float
    timestamp: datetime
    labels: Dict[str, str] = field(default_factory=dict)

@dataclass
class MetricSeries:
    """Time series of metric values"""
    name: str
    metric_type: str  # counter, gauge, histogram, summary
    help_text: str
    values: deque = field(default_factory=lambda: deque(maxlen=1000))
    labels: Dict[str, str] = field(default_factory=dict)

class MetricsCollector:
    """Prometheus-style metrics collector"""
    
    def __init__(self):
        self.metrics: Dict[str, MetricSeries] = {}
        self.lock = threading.Lock()
        
        # Initialize core metrics
        self._initialize_core_metrics()
    
    def _initialize_core_metrics(self):
        """Initialize core system metrics"""
        
        # Trading metrics
        self.register_counter(
            "trades_total",
            "Total number of trades executed",
            ["symbol", "exchange", "trade_type", "status"]
        )
        
        self.register_gauge(
            "portfolio_value_usd",
            "Current portfolio value in USD",
            ["user_id", "exchange"]
        )
        
        self.register_gauge(
            "open_positions_count",
            "Number of open positions",
            ["user_id", "exchange"]
        )
        
        self.register_histogram(
            "trade_execution_duration_seconds",
            "Time taken to execute trades",
            ["exchange", "trade_type"]
        )
        
        # Signal generation metrics
        self.register_counter(
            "signals_generated_total",
            "Total number of trading signals generated",
            ["symbol", "signal_type", "confidence_level"]
        )
        
        self.register_gauge(
            "signal_confidence_score",
            "Confidence score of generated signals",
            ["symbol", "signal_type"]
        )
        
        # Risk management metrics
        self.register_gauge(
            "portfolio_drawdown_percent",
            "Current portfolio drawdown percentage",
            ["user_id"]
        )
        
        self.register_counter(
            "risk_alerts_total",
            "Total number of risk alerts triggered",
            ["alert_type", "severity"]
        )
        
        self.register_gauge(
            "circuit_breaker_state",
            "Circuit breaker state (0=closed, 1=half_open, 2=open)",
            ["user_id"]
        )
        
        # System performance metrics
        self.register_gauge(
            "system_cpu_usage_percent",
            "System CPU usage percentage"
        )
        
        self.register_gauge(
            "system_memory_usage_percent",
            "System memory usage percentage"
        )
        
        self.register_gauge(
            "database_connections_active",
            "Number of active database connections"
        )
        
        # API performance metrics
        self.register_histogram(
            "api_request_duration_seconds",
            "API request duration in seconds",
            ["method", "endpoint", "status_code"]
        )
        
        self.register_counter(
            "api_requests_total",
            "Total number of API requests",
            ["method", "endpoint", "status_code"]
        )
        
        # Market data metrics
        self.register_gauge(
            "market_data_latency_seconds",
            "Market data feed latency",
            ["exchange", "symbol"]
        )
        
        self.register_counter(
            "market_data_updates_total",
            "Total market data updates received",
            ["exchange", "symbol", "data_type"]
        )
        
        # Backtesting metrics
        self.register_counter(
            "backtests_total",
            "Total number of backtests executed",
            ["strategy", "status"]
        )
        
        self.register_histogram(
            "backtest_duration_seconds",
            "Backtest execution duration",
            ["strategy", "data_period"]
        )
        
        logger.info("Core metrics initialized")
    
    def register_counter(self, name: str, help_text: str, labels: List[str] = None):
        """Register a counter metric"""
        with self.lock:
            self.metrics[name] = MetricSeries(
                name=name,
                metric_type="counter",
                help_text=help_text,
                labels={label: "" for label in (labels or [])}
            )
    
    def register_gauge(self, name: str, help_text: str, labels: List[str] = None):
        """Register a gauge metric"""
        with self.lock:
            self.metrics[name] = MetricSeries(
                name=name,
                metric_type="gauge",
                help_text=help_text,
                labels={label: "" for label in (labels or [])}
            )
    
    def register_histogram(self, name: str, help_text: str, labels: List[str] = None):
        """Register a histogram metric"""
        with self.lock:
            self.metrics[name] = MetricSeries(
                name=name,
                metric_type="histogram",
                help_text=help_text,
                labels={label: "" for label in (labels or [])}
            )
    
    def increment_counter(self, name: str, value: float = 1.0, labels: Dict[str, str] = None):
        """Increment a counter metric"""
        with self.lock:
            if name in self.metrics:
                metric_value = MetricValue(
                    value=value,
                    timestamp=datetime.utcnow(),
                    labels=labels or {}
                )
                self.metrics[name].values.append(metric_value)
    
    def set_gauge(self, name: str, value: float, labels: Dict[str, str] = None):
        """Set a gauge metric value"""
        with self.lock:
            if name in self.metrics:
                metric_value = MetricValue(
                    value=value,
                    timestamp=datetime.utcnow(),
                    labels=labels or {}
                )
                self.metrics[name].values.append(metric_value)
    
    def observe_histogram(self, name: str, value: float, labels: Dict[str, str] = None):
        """Observe a value for histogram metric"""
        with self.lock:
            if name in self.metrics:
                metric_value = MetricValue(
                    value=value,
                    timestamp=datetime.utcnow(),
                    labels=labels or {}
                )
                self.metrics[name].values.append(metric_value)
    
    def get_metric(self, name: str) -> Optional[MetricSeries]:
        """Get metric by name"""
        with self.lock:
            return self.metrics.get(name)
    
    def get_all_metrics(self) -> Dict[str, MetricSeries]:
        """Get all metrics"""
        with self.lock:
            return self.metrics.copy()
    
    def export_prometheus_format(self) -> str:
        """Export metrics in Prometheus format"""
        output = []
        
        with self.lock:
            for metric_name, metric_series in self.metrics.items():
                # Add help text
                output.append(f"# HELP {metric_name} {metric_series.help_text}")
                output.append(f"# TYPE {metric_name} {metric_series.metric_type}")
                
                # Add metric values
                if metric_series.values:
                    latest_value = metric_series.values[-1]
                    
                    # Format labels
                    label_str = ""
                    if latest_value.labels:
                        label_pairs = [f'{k}="{v}"' for k, v in latest_value.labels.items()]
                        label_str = "{" + ",".join(label_pairs) + "}"
                    
                    output.append(f"{metric_name}{label_str} {latest_value.value}")
                
                output.append("")  # Empty line between metrics
        
        return "\n".join(output)

class MetricsService:
    """Main metrics service for system monitoring"""
    
    def __init__(self):
        self.collector = MetricsCollector()
        self.monitoring_active = False
        self.monitoring_task = None
        self.system_metrics_interval = 30  # seconds
        
    async def start_monitoring(self):
        """Start background metrics collection"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("Metrics monitoring started")
    
    async def stop_monitoring(self):
        """Stop background metrics collection"""
        self.monitoring_active = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        logger.info("Metrics monitoring stopped")
    
    async def _monitoring_loop(self):
        """Background monitoring loop"""
        while self.monitoring_active:
            try:
                await self._collect_system_metrics()
                await asyncio.sleep(self.system_metrics_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(5)  # Brief pause before retry
    
    async def _collect_system_metrics(self):
        """Collect system performance metrics"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            self.collector.set_gauge("system_cpu_usage_percent", cpu_percent)
            
            # Memory usage
            memory = psutil.virtual_memory()
            self.collector.set_gauge("system_memory_usage_percent", memory.percent)
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            self.collector.set_gauge("system_disk_usage_percent", disk_percent)
            
            # Network I/O
            net_io = psutil.net_io_counters()
            self.collector.set_gauge("network_bytes_sent_total", net_io.bytes_sent)
            self.collector.set_gauge("network_bytes_received_total", net_io.bytes_recv)
            
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")
    
    # Trading metrics methods
    def record_trade_executed(self, symbol: str, exchange: str, trade_type: str, status: str):
        """Record a trade execution"""
        self.collector.increment_counter(
            "trades_total",
            labels={
                "symbol": symbol,
                "exchange": exchange,
                "trade_type": trade_type,
                "status": status
            }
        )
    
    def update_portfolio_value(self, user_id: int, exchange: str, value_usd: float):
        """Update portfolio value metric"""
        self.collector.set_gauge(
            "portfolio_value_usd",
            value_usd,
            labels={"user_id": str(user_id), "exchange": exchange}
        )
    
    def update_open_positions(self, user_id: int, exchange: str, count: int):
        """Update open positions count"""
        self.collector.set_gauge(
            "open_positions_count",
            count,
            labels={"user_id": str(user_id), "exchange": exchange}
        )
    
    def record_trade_duration(self, exchange: str, trade_type: str, duration_seconds: float):
        """Record trade execution duration"""
        self.collector.observe_histogram(
            "trade_execution_duration_seconds",
            duration_seconds,
            labels={"exchange": exchange, "trade_type": trade_type}
        )
    
    # Signal generation metrics
    def record_signal_generated(self, symbol: str, signal_type: str, confidence: float):
        """Record signal generation"""
        confidence_level = "high" if confidence > 0.7 else "medium" if confidence > 0.5 else "low"
        
        self.collector.increment_counter(
            "signals_generated_total",
            labels={
                "symbol": symbol,
                "signal_type": signal_type,
                "confidence_level": confidence_level
            }
        )
        
        self.collector.set_gauge(
            "signal_confidence_score",
            confidence,
            labels={"symbol": symbol, "signal_type": signal_type}
        )
    
    # Risk management metrics
    def update_portfolio_drawdown(self, user_id: int, drawdown_percent: float):
        """Update portfolio drawdown"""
        self.collector.set_gauge(
            "portfolio_drawdown_percent",
            drawdown_percent,
            labels={"user_id": str(user_id)}
        )
    
    def record_risk_alert(self, alert_type: str, severity: str):
        """Record risk alert"""
        self.collector.increment_counter(
            "risk_alerts_total",
            labels={"alert_type": alert_type, "severity": severity}
        )
    
    def update_circuit_breaker_state(self, user_id: int, state: str):
        """Update circuit breaker state"""
        state_value = {"closed": 0, "half_open": 1, "open": 2}.get(state, 0)
        self.collector.set_gauge(
            "circuit_breaker_state",
            state_value,
            labels={"user_id": str(user_id)}
        )
    
    # API metrics
    def record_api_request(self, method: str, endpoint: str, status_code: int, duration: float):
        """Record API request metrics"""
        self.collector.increment_counter(
            "api_requests_total",
            labels={
                "method": method,
                "endpoint": endpoint,
                "status_code": str(status_code)
            }
        )
        
        self.collector.observe_histogram(
            "api_request_duration_seconds",
            duration,
            labels={
                "method": method,
                "endpoint": endpoint,
                "status_code": str(status_code)
            }
        )
    
    # Market data metrics
    def update_market_data_latency(self, exchange: str, symbol: str, latency_seconds: float):
        """Update market data latency"""
        self.collector.set_gauge(
            "market_data_latency_seconds",
            latency_seconds,
            labels={"exchange": exchange, "symbol": symbol}
        )
    
    def record_market_data_update(self, exchange: str, symbol: str, data_type: str):
        """Record market data update"""
        self.collector.increment_counter(
            "market_data_updates_total",
            labels={
                "exchange": exchange,
                "symbol": symbol,
                "data_type": data_type
            }
        )
    
    # Backtesting metrics
    def record_backtest_completed(self, strategy: str, status: str, duration: float, data_period: str):
        """Record backtest completion"""
        self.collector.increment_counter(
            "backtests_total",
            labels={"strategy": strategy, "status": status}
        )
        
        self.collector.observe_histogram(
            "backtest_duration_seconds",
            duration,
            labels={"strategy": strategy, "data_period": data_period}
        )
    
    def get_metrics_export(self) -> str:
        """Get metrics in Prometheus export format"""
        return self.collector.export_prometheus_format()
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get metrics summary for dashboard"""
        metrics = self.collector.get_all_metrics()
        summary = {}
        
        for name, series in metrics.items():
            if series.values:
                latest = series.values[-1]
                summary[name] = {
                    "value": latest.value,
                    "timestamp": latest.timestamp.isoformat(),
                    "labels": latest.labels,
                    "type": series.metric_type
                }
        
        return summary

# Decorator for automatic API metrics collection
def track_api_metrics(endpoint: str):
    """Decorator to automatically track API metrics"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            method = "GET"  # Default, should be extracted from request
            status_code = 200
            
            try:
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                status_code = 500
                raise
            finally:
                duration = time.time() - start_time
                metrics_service.record_api_request(method, endpoint, status_code, duration)
        
        return wrapper
    return decorator

# Global metrics service instance
metrics_service = MetricsService()

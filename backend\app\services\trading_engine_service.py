"""
Trading Engine Service for AI Crypto Trading System
Author: inkbytefo

This service handles:
- Binance API integration
- Order management system
- Execution algorithms
- Trade logging and tracking
- Position management
"""

import logging
import asyncio
import ccxt
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass
from enum import Enum
import json

from app.core.config import settings
from app.core.cache import cache_manager
from app.services.risk_management_service import risk_management_service, PositionSizeResult
from app.models.trading import Trade, TradeSignal

logger = logging.getLogger(__name__)


class OrderType(Enum):
    """Order types"""
    MARKET = "market"
    LIMIT = "limit"
    STOP_LOSS = "stop_loss"
    TAKE_PROFIT = "take_profit"
    STOP_LIMIT = "stop_limit"


class OrderSide(Enum):
    """Order sides"""
    BUY = "buy"
    SELL = "sell"


class OrderStatus(Enum):
    """Order status"""
    PENDING = "pending"
    OPEN = "open"
    FILLED = "filled"
    PARTIALLY_FILLED = "partially_filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    EXPIRED = "expired"


class TradeStatus(Enum):
    """Trade status"""
    PENDING = "pending"
    ACTIVE = "active"
    CLOSED = "closed"
    CANCELLED = "cancelled"


@dataclass
class OrderRequest:
    """Order request structure"""
    symbol: str
    side: OrderSide
    order_type: OrderType
    amount: float
    price: Optional[float] = None
    stop_price: Optional[float] = None
    time_in_force: str = "GTC"  # Good Till Cancelled
    reduce_only: bool = False
    post_only: bool = False


@dataclass
class OrderResult:
    """Order execution result"""
    order_id: str
    symbol: str
    side: str
    amount: float
    price: float
    status: OrderStatus
    filled_amount: float
    remaining_amount: float
    average_price: float
    fees: Dict[str, float]
    timestamp: datetime
    exchange_response: Dict[str, Any]


@dataclass
class TradePosition:
    """Active trade position"""
    trade_id: str
    symbol: str
    side: str
    entry_price: float
    current_price: float
    amount: float
    unrealized_pnl: float
    unrealized_pnl_percentage: float
    stop_loss_price: Optional[float]
    take_profit_price: Optional[float]
    entry_time: datetime
    last_update: datetime


class TradingEngineService:
    """Trading Engine Service"""
    
    def __init__(self):
        self.exchange = None
        self.is_testnet = getattr(settings, 'TRADING_TESTNET', True)
        self.trading_enabled = getattr(settings, 'ENABLE_TRADING', False)
        self.active_positions: Dict[str, TradePosition] = {}
        self.pending_orders: Dict[str, OrderResult] = {}
        
    async def initialize(self) -> bool:
        """Initialize trading engine and exchange connection"""
        try:
            if not self.trading_enabled:
                logger.info("Trading is disabled in configuration")
                return False
            
            # Initialize Binance exchange
            api_key = getattr(settings, 'BINANCE_API_KEY', None)
            secret_key = getattr(settings, 'BINANCE_SECRET_KEY', None)
            
            if not api_key or not secret_key:
                logger.error("Binance API credentials not configured")
                return False
            
            self.exchange = ccxt.binance({
                'apiKey': api_key,
                'secret': secret_key,
                'sandbox': self.is_testnet,
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'spot',  # spot, future, margin
                }
            })
            
            # Test connection
            await self._test_connection()
            
            logger.info(f"Trading engine initialized (testnet: {self.is_testnet})")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing trading engine: {e}")
            return False
    
    async def _test_connection(self) -> bool:
        """Test exchange connection"""
        try:
            if not self.exchange:
                return False
            
            # Test API connection
            balance = await self.exchange.fetch_balance()
            logger.info("Exchange connection test successful")
            return True
            
        except Exception as e:
            logger.error(f"Exchange connection test failed: {e}")
            return False
    
    async def execute_trade_signal(
        self,
        signal: TradeSignal,
        portfolio_value: float,
        user_id: int,
        auto_execute: bool = False
    ) -> Dict[str, Any]:
        """Execute a trade signal"""
        try:
            if not self.trading_enabled or not auto_execute:
                return {
                    "status": "pending_approval",
                    "message": "Trade signal requires manual approval",
                    "signal_id": signal.id if hasattr(signal, 'id') else None
                }
            
            if not self.exchange:
                await self.initialize()
                if not self.exchange:
                    return {
                        "status": "error",
                        "message": "Trading engine not initialized"
                    }
            
            # Calculate position size
            position_size_result = await risk_management_service.calculate_position_size(
                symbol=signal.symbol,
                entry_price=signal.entry_price,
                portfolio_value=portfolio_value,
                signal_confidence=signal.confidence
            )
            
            if position_size_result.recommended_size <= 0:
                return {
                    "status": "rejected",
                    "message": "Position size calculation resulted in zero size",
                    "warnings": position_size_result.warnings
                }
            
            # Create order request
            order_request = OrderRequest(
                symbol=signal.symbol,
                side=OrderSide.BUY if signal.signal_type.lower() == "buy" else OrderSide.SELL,
                order_type=OrderType.MARKET,  # Start with market orders
                amount=position_size_result.recommended_size
            )
            
            # Execute order
            order_result = await self._execute_order(order_request)
            
            if order_result.status in [OrderStatus.FILLED, OrderStatus.PARTIALLY_FILLED]:
                # Create trade position
                trade_position = TradePosition(
                    trade_id=f"trade_{order_result.order_id}",
                    symbol=signal.symbol,
                    side=signal.signal_type.lower(),
                    entry_price=order_result.average_price,
                    current_price=order_result.average_price,
                    amount=order_result.filled_amount,
                    unrealized_pnl=0.0,
                    unrealized_pnl_percentage=0.0,
                    stop_loss_price=position_size_result.stop_loss_price,
                    take_profit_price=position_size_result.take_profit_price,
                    entry_time=order_result.timestamp,
                    last_update=datetime.now()
                )
                
                self.active_positions[trade_position.trade_id] = trade_position
                
                # Set stop loss and take profit orders
                await self._set_stop_loss_take_profit(trade_position)
                
                return {
                    "status": "executed",
                    "trade_id": trade_position.trade_id,
                    "order_id": order_result.order_id,
                    "entry_price": order_result.average_price,
                    "amount": order_result.filled_amount,
                    "fees": order_result.fees
                }
            else:
                return {
                    "status": "failed",
                    "message": f"Order execution failed: {order_result.status.value}",
                    "order_id": order_result.order_id
                }
            
        except Exception as e:
            logger.error(f"Error executing trade signal: {e}")
            return {
                "status": "error",
                "message": str(e)
            }
    
    async def _execute_order(self, order_request: OrderRequest) -> OrderResult:
        """Execute an order on the exchange"""
        try:
            if not self.exchange:
                raise Exception("Exchange not initialized")
            
            # Prepare order parameters
            order_params = {
                'symbol': order_request.symbol,
                'type': order_request.order_type.value,
                'side': order_request.side.value,
                'amount': order_request.amount,
            }
            
            # Add price for limit orders
            if order_request.order_type == OrderType.LIMIT and order_request.price:
                order_params['price'] = order_request.price
            
            # Add stop price for stop orders
            if order_request.order_type in [OrderType.STOP_LOSS, OrderType.STOP_LIMIT] and order_request.stop_price:
                order_params['stopPrice'] = order_request.stop_price
            
            # Execute order
            response = await self.exchange.create_order(**order_params)
            
            # Parse response
            order_result = OrderResult(
                order_id=response['id'],
                symbol=response['symbol'],
                side=response['side'],
                amount=float(response['amount']),
                price=float(response.get('price', 0)),
                status=self._parse_order_status(response['status']),
                filled_amount=float(response.get('filled', 0)),
                remaining_amount=float(response.get('remaining', response['amount'])),
                average_price=float(response.get('average', response.get('price', 0))),
                fees=response.get('fees', {}),
                timestamp=datetime.fromtimestamp(response['timestamp'] / 1000),
                exchange_response=response
            )
            
            logger.info(f"Order executed: {order_result.order_id} - {order_result.status.value}")
            return order_result
            
        except Exception as e:
            logger.error(f"Error executing order: {e}")
            raise
    
    def _parse_order_status(self, status: str) -> OrderStatus:
        """Parse exchange order status to internal status"""
        status_mapping = {
            'open': OrderStatus.OPEN,
            'closed': OrderStatus.FILLED,
            'canceled': OrderStatus.CANCELLED,
            'cancelled': OrderStatus.CANCELLED,
            'rejected': OrderStatus.REJECTED,
            'expired': OrderStatus.EXPIRED,
            'pending': OrderStatus.PENDING,
            'partially_filled': OrderStatus.PARTIALLY_FILLED
        }
        
        return status_mapping.get(status.lower(), OrderStatus.PENDING)
    
    async def _set_stop_loss_take_profit(self, position: TradePosition) -> None:
        """Set stop loss and take profit orders for a position"""
        try:
            if not position.stop_loss_price and not position.take_profit_price:
                return
            
            # Set stop loss order
            if position.stop_loss_price:
                stop_loss_order = OrderRequest(
                    symbol=position.symbol,
                    side=OrderSide.SELL if position.side == "buy" else OrderSide.BUY,
                    order_type=OrderType.STOP_LOSS,
                    amount=position.amount,
                    stop_price=position.stop_loss_price,
                    reduce_only=True
                )
                
                try:
                    await self._execute_order(stop_loss_order)
                    logger.info(f"Stop loss set for {position.trade_id} at {position.stop_loss_price}")
                except Exception as e:
                    logger.error(f"Failed to set stop loss for {position.trade_id}: {e}")
            
            # Set take profit order
            if position.take_profit_price:
                take_profit_order = OrderRequest(
                    symbol=position.symbol,
                    side=OrderSide.SELL if position.side == "buy" else OrderSide.BUY,
                    order_type=OrderType.LIMIT,
                    amount=position.amount,
                    price=position.take_profit_price,
                    reduce_only=True
                )
                
                try:
                    await self._execute_order(take_profit_order)
                    logger.info(f"Take profit set for {position.trade_id} at {position.take_profit_price}")
                except Exception as e:
                    logger.error(f"Failed to set take profit for {position.trade_id}: {e}")
                    
        except Exception as e:
            logger.error(f"Error setting stop loss/take profit: {e}")


    async def get_active_positions(self, user_id: Optional[int] = None) -> List[TradePosition]:
        """Get all active trading positions"""
        try:
            # Update position data with current prices
            await self._update_positions()

            # Filter by user if specified
            positions = list(self.active_positions.values())

            # TODO: Add user filtering when user management is implemented
            # if user_id:
            #     positions = [p for p in positions if p.user_id == user_id]

            return positions

        except Exception as e:
            logger.error(f"Error getting active positions: {e}")
            return []

    async def _update_positions(self) -> None:
        """Update all active positions with current market prices"""
        try:
            if not self.active_positions:
                return

            # Get current prices for all symbols
            symbols = list(set(pos.symbol for pos in self.active_positions.values()))

            for symbol in symbols:
                try:
                    ticker = await self.exchange.fetch_ticker(symbol)
                    current_price = float(ticker['last'])

                    # Update positions for this symbol
                    for position in self.active_positions.values():
                        if position.symbol == symbol:
                            position.current_price = current_price

                            # Calculate unrealized PnL
                            if position.side == "buy":
                                pnl = (current_price - position.entry_price) * position.amount
                            else:
                                pnl = (position.entry_price - current_price) * position.amount

                            position.unrealized_pnl = pnl
                            position.unrealized_pnl_percentage = (pnl / (position.entry_price * position.amount)) * 100
                            position.last_update = datetime.now()

                except Exception as e:
                    logger.error(f"Error updating price for {symbol}: {e}")

        except Exception as e:
            logger.error(f"Error updating positions: {e}")

    async def close_position(
        self,
        trade_id: str,
        close_percentage: float = 100.0,
        order_type: OrderType = OrderType.MARKET
    ) -> Dict[str, Any]:
        """Close a trading position"""
        try:
            if trade_id not in self.active_positions:
                return {
                    "status": "error",
                    "message": "Position not found"
                }

            position = self.active_positions[trade_id]
            close_amount = position.amount * (close_percentage / 100.0)

            # Create close order
            close_order = OrderRequest(
                symbol=position.symbol,
                side=OrderSide.SELL if position.side == "buy" else OrderSide.BUY,
                order_type=order_type,
                amount=close_amount,
                reduce_only=True
            )

            # Execute close order
            order_result = await self._execute_order(close_order)

            if order_result.status in [OrderStatus.FILLED, OrderStatus.PARTIALLY_FILLED]:
                # Update position
                position.amount -= order_result.filled_amount

                # Remove position if fully closed
                if position.amount <= 0.001:  # Small threshold for rounding
                    del self.active_positions[trade_id]

                return {
                    "status": "closed",
                    "trade_id": trade_id,
                    "closed_amount": order_result.filled_amount,
                    "close_price": order_result.average_price,
                    "realized_pnl": self._calculate_realized_pnl(position, order_result),
                    "remaining_amount": position.amount if trade_id in self.active_positions else 0
                }
            else:
                return {
                    "status": "failed",
                    "message": f"Close order failed: {order_result.status.value}",
                    "order_id": order_result.order_id
                }

        except Exception as e:
            logger.error(f"Error closing position {trade_id}: {e}")
            return {
                "status": "error",
                "message": str(e)
            }

    def _calculate_realized_pnl(self, position: TradePosition, close_order: OrderResult) -> float:
        """Calculate realized PnL for a closed position"""
        try:
            if position.side == "buy":
                pnl = (close_order.average_price - position.entry_price) * close_order.filled_amount
            else:
                pnl = (position.entry_price - close_order.average_price) * close_order.filled_amount

            # Subtract fees
            total_fees = sum(close_order.fees.values()) if close_order.fees else 0
            pnl -= total_fees

            return pnl

        except Exception as e:
            logger.error(f"Error calculating realized PnL: {e}")
            return 0.0

    async def get_account_balance(self) -> Dict[str, Any]:
        """Get account balance from exchange"""
        try:
            if not self.exchange:
                await self.initialize()
                if not self.exchange:
                    return {"error": "Exchange not initialized"}

            balance = await self.exchange.fetch_balance()

            # Format balance data
            formatted_balance = {
                "total_value_usd": 0.0,
                "free_balance": {},
                "used_balance": {},
                "total_balance": {},
                "positions_value": 0.0
            }

            for currency, amounts in balance.items():
                if currency in ['free', 'used', 'total']:
                    continue

                if amounts['total'] > 0:
                    formatted_balance["free_balance"][currency] = amounts['free']
                    formatted_balance["used_balance"][currency] = amounts['used']
                    formatted_balance["total_balance"][currency] = amounts['total']

            # Calculate total USD value (simplified)
            if 'USDT' in formatted_balance["total_balance"]:
                formatted_balance["total_value_usd"] = formatted_balance["total_balance"]["USDT"]

            return formatted_balance

        except Exception as e:
            logger.error(f"Error getting account balance: {e}")
            return {"error": str(e)}

    async def get_order_history(
        self,
        symbol: Optional[str] = None,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """Get order history from exchange"""
        try:
            if not self.exchange:
                return []

            if symbol:
                orders = await self.exchange.fetch_orders(symbol, limit=limit)
            else:
                orders = await self.exchange.fetch_orders(limit=limit)

            # Format orders
            formatted_orders = []
            for order in orders:
                formatted_orders.append({
                    "order_id": order['id'],
                    "symbol": order['symbol'],
                    "side": order['side'],
                    "type": order['type'],
                    "amount": order['amount'],
                    "price": order.get('price'),
                    "average_price": order.get('average'),
                    "filled": order.get('filled', 0),
                    "remaining": order.get('remaining', 0),
                    "status": order['status'],
                    "timestamp": datetime.fromtimestamp(order['timestamp'] / 1000) if order['timestamp'] else None,
                    "fees": order.get('fees', {})
                })

            return formatted_orders

        except Exception as e:
            logger.error(f"Error getting order history: {e}")
            return []

    async def cancel_order(self, order_id: str, symbol: str) -> Dict[str, Any]:
        """Cancel an open order"""
        try:
            if not self.exchange:
                return {"status": "error", "message": "Exchange not initialized"}

            result = await self.exchange.cancel_order(order_id, symbol)

            return {
                "status": "cancelled",
                "order_id": order_id,
                "symbol": symbol,
                "exchange_response": result
            }

        except Exception as e:
            logger.error(f"Error cancelling order {order_id}: {e}")
            return {
                "status": "error",
                "message": str(e)
            }

    async def get_trading_fees(self, symbol: str) -> Dict[str, float]:
        """Get trading fees for a symbol"""
        try:
            if not self.exchange:
                return {"maker": 0.001, "taker": 0.001}  # Default Binance fees

            markets = await self.exchange.load_markets()

            if symbol in markets:
                market = markets[symbol]
                return {
                    "maker": market.get('maker', 0.001),
                    "taker": market.get('taker', 0.001)
                }

            return {"maker": 0.001, "taker": 0.001}

        except Exception as e:
            logger.error(f"Error getting trading fees for {symbol}: {e}")
            return {"maker": 0.001, "taker": 0.001}

    async def validate_trading_pair(self, symbol: str) -> bool:
        """Validate if a trading pair is available"""
        try:
            if not self.exchange:
                return False

            markets = await self.exchange.load_markets()
            return symbol in markets

        except Exception as e:
            logger.error(f"Error validating trading pair {symbol}: {e}")
            return False

    async def get_market_status(self) -> Dict[str, Any]:
        """Get exchange market status"""
        try:
            if not self.exchange:
                return {"status": "disconnected"}

            status = await self.exchange.fetch_status()

            return {
                "status": status.get('status', 'unknown'),
                "updated": status.get('updated'),
                "eta": status.get('eta'),
                "url": status.get('url')
            }

        except Exception as e:
            logger.error(f"Error getting market status: {e}")
            return {"status": "error", "message": str(e)}


# Create global instance
trading_engine_service = TradingEngineService()

"""
Configuration Validator for AI Crypto Trading System
Author: inkbytefo

This module provides comprehensive configuration validation and environment setup verification.
"""

import os
import re
import logging
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
from urllib.parse import urlparse

logger = logging.getLogger(__name__)


class ConfigValidationError(Exception):
    """Configuration validation error"""
    pass


class ConfigValidator:
    """Configuration validator class"""
    
    def __init__(self):
        self.errors: List[str] = []
        self.warnings: List[str] = []
        self.required_vars: Dict[str, str] = {
            'DATABASE_URL': 'PostgreSQL database connection URL',
            'SECRET_KEY': 'Secret key for JWT tokens',
            'TELEGRAM_BOT_TOKEN': 'Telegram bot token from @BotFather',
        }
        
        self.optional_vars: Dict[str, str] = {
            'REDIS_URL': 'Redis cache connection URL',
            'OPENAI_API_KEY': 'OpenAI API key for AI analysis',
            'BINANCE_API_KEY': 'Binance API key for trading',
            'BINANCE_SECRET_KEY': 'Binance secret key for trading',
        }
    
    def validate_all(self) -> Tuple[bool, List[str], List[str]]:
        """Validate all configuration settings"""
        self.errors.clear()
        self.warnings.clear()
        
        # Validate required environment variables
        self._validate_required_vars()
        
        # Validate database configuration
        self._validate_database_config()
        
        # Validate API keys
        self._validate_api_keys()
        
        # Validate trading configuration
        self._validate_trading_config()
        
        # Validate security settings
        self._validate_security_config()
        
        # Validate file paths
        self._validate_file_paths()
        
        # Validate network settings
        self._validate_network_config()
        
        is_valid = len(self.errors) == 0
        return is_valid, self.errors.copy(), self.warnings.copy()
    
    def _validate_required_vars(self) -> None:
        """Validate required environment variables"""
        for var_name, description in self.required_vars.items():
            value = os.getenv(var_name)
            if not value:
                self.errors.append(f"Missing required environment variable: {var_name} ({description})")
            elif len(value.strip()) == 0:
                self.errors.append(f"Empty required environment variable: {var_name}")
    
    def _validate_database_config(self) -> None:
        """Validate database configuration"""
        database_url = os.getenv('DATABASE_URL')
        if database_url:
            try:
                parsed = urlparse(database_url)
                
                # Check scheme
                if parsed.scheme not in ['postgresql', 'postgres']:
                    self.errors.append("DATABASE_URL must use postgresql:// scheme")
                
                # Check components
                if not parsed.hostname:
                    self.errors.append("DATABASE_URL missing hostname")
                
                if not parsed.username:
                    self.errors.append("DATABASE_URL missing username")
                
                if not parsed.password:
                    self.warnings.append("DATABASE_URL missing password (may be required)")
                
                if not parsed.path or parsed.path == '/':
                    self.errors.append("DATABASE_URL missing database name")
                
                # Check port
                if parsed.port and (parsed.port < 1 or parsed.port > 65535):
                    self.errors.append("DATABASE_URL has invalid port number")
                    
            except Exception as e:
                self.errors.append(f"Invalid DATABASE_URL format: {e}")
        
        # Validate Redis URL
        redis_url = os.getenv('REDIS_URL')
        if redis_url:
            try:
                parsed = urlparse(redis_url)
                if parsed.scheme not in ['redis', 'rediss']:
                    self.errors.append("REDIS_URL must use redis:// or rediss:// scheme")
            except Exception as e:
                self.warnings.append(f"Invalid REDIS_URL format: {e}")
    
    def _validate_api_keys(self) -> None:
        """Validate API keys format and presence"""
        # OpenAI API Key
        openai_key = os.getenv('OPENAI_API_KEY')
        if openai_key:
            if not openai_key.startswith('sk-'):
                self.warnings.append("OPENAI_API_KEY should start with 'sk-'")
            if len(openai_key) < 20:
                self.warnings.append("OPENAI_API_KEY seems too short")
        
        # Binance API Keys
        binance_api_key = os.getenv('BINANCE_API_KEY')
        binance_secret = os.getenv('BINANCE_SECRET_KEY')
        
        if binance_api_key and not binance_secret:
            self.errors.append("BINANCE_SECRET_KEY required when BINANCE_API_KEY is set")
        elif binance_secret and not binance_api_key:
            self.errors.append("BINANCE_API_KEY required when BINANCE_SECRET_KEY is set")
        
        if binance_api_key and len(binance_api_key) < 32:
            self.warnings.append("BINANCE_API_KEY seems too short")
        
        if binance_secret and len(binance_secret) < 32:
            self.warnings.append("BINANCE_SECRET_KEY seems too short")
        
        # Telegram Bot Token
        telegram_token = os.getenv('TELEGRAM_BOT_TOKEN')
        if telegram_token:
            # Telegram bot token format: 123456789:ABCdefGHIjklMNOpqrsTUVwxyz
            if not re.match(r'^\d+:[A-Za-z0-9_-]+$', telegram_token):
                self.errors.append("TELEGRAM_BOT_TOKEN has invalid format")
    
    def _validate_trading_config(self) -> None:
        """Validate trading configuration"""
        # Trading mode
        trading_mode = os.getenv('TRADING_MODE', 'testnet')
        if trading_mode not in ['testnet', 'live']:
            self.errors.append("TRADING_MODE must be 'testnet' or 'live'")
        
        # Enable trading
        enable_trading = os.getenv('ENABLE_TRADING', 'false').lower()
        if enable_trading not in ['true', 'false']:
            self.errors.append("ENABLE_TRADING must be 'true' or 'false'")
        
        # Risk settings
        try:
            max_position = float(os.getenv('MAX_POSITION_SIZE', '0.1'))
            if max_position <= 0 or max_position > 1:
                self.errors.append("MAX_POSITION_SIZE must be between 0 and 1")
        except ValueError:
            self.errors.append("MAX_POSITION_SIZE must be a valid number")
        
        try:
            max_drawdown = float(os.getenv('MAX_DRAWDOWN', '0.2'))
            if max_drawdown <= 0 or max_drawdown > 1:
                self.errors.append("MAX_DRAWDOWN must be between 0 and 1")
        except ValueError:
            self.errors.append("MAX_DRAWDOWN must be a valid number")
        
        # Risk level
        risk_level = os.getenv('RISK_LEVEL', 'low')
        if risk_level not in ['low', 'medium', 'high']:
            self.errors.append("RISK_LEVEL must be 'low', 'medium', or 'high'")
    
    def _validate_security_config(self) -> None:
        """Validate security configuration"""
        # Secret key
        secret_key = os.getenv('SECRET_KEY')
        if secret_key:
            if len(secret_key) < 32:
                self.warnings.append("SECRET_KEY should be at least 32 characters long")
            
            if secret_key in ['your_super_secret_key_here_change_this_in_production', 'changeme']:
                self.errors.append("SECRET_KEY must be changed from default value")
        
        # JWT settings
        try:
            token_expire = int(os.getenv('ACCESS_TOKEN_EXPIRE_MINUTES', '30'))
            if token_expire <= 0 or token_expire > 1440:  # Max 24 hours
                self.warnings.append("ACCESS_TOKEN_EXPIRE_MINUTES should be between 1 and 1440")
        except ValueError:
            self.errors.append("ACCESS_TOKEN_EXPIRE_MINUTES must be a valid number")
    
    def _validate_file_paths(self) -> None:
        """Validate file paths and directories"""
        # Log file path
        log_file = os.getenv('LOG_FILE', 'logs/trading.log')
        log_dir = Path(log_file).parent
        
        if not log_dir.exists():
            try:
                log_dir.mkdir(parents=True, exist_ok=True)
                self.warnings.append(f"Created log directory: {log_dir}")
            except Exception as e:
                self.errors.append(f"Cannot create log directory {log_dir}: {e}")
    
    def _validate_network_config(self) -> None:
        """Validate network configuration"""
        # API host and port
        api_host = os.getenv('API_HOST', '0.0.0.0')
        if api_host not in ['0.0.0.0', '127.0.0.1', 'localhost']:
            # Validate IP address format
            import ipaddress
            try:
                ipaddress.ip_address(api_host)
            except ValueError:
                self.errors.append(f"Invalid API_HOST: {api_host}")
        
        try:
            api_port = int(os.getenv('API_PORT', '8000'))
            if api_port < 1 or api_port > 65535:
                self.errors.append("API_PORT must be between 1 and 65535")
        except ValueError:
            self.errors.append("API_PORT must be a valid number")
    
    def generate_report(self) -> str:
        """Generate a configuration validation report"""
        report = []
        report.append("🔍 Configuration Validation Report")
        report.append("=" * 50)
        
        if not self.errors and not self.warnings:
            report.append("✅ All configuration settings are valid!")
            return "\n".join(report)
        
        if self.errors:
            report.append(f"\n❌ Errors ({len(self.errors)}):")
            for i, error in enumerate(self.errors, 1):
                report.append(f"  {i}. {error}")
        
        if self.warnings:
            report.append(f"\n⚠️  Warnings ({len(self.warnings)}):")
            for i, warning in enumerate(self.warnings, 1):
                report.append(f"  {i}. {warning}")
        
        if self.errors:
            report.append("\n💡 Please fix the errors above before starting the application.")
        
        return "\n".join(report)
    
    def check_environment_file(self) -> bool:
        """Check if .env file exists and is readable"""
        env_file = Path('.env')
        
        if not env_file.exists():
            self.warnings.append(".env file not found - using environment variables only")
            return False
        
        if not env_file.is_file():
            self.errors.append(".env exists but is not a file")
            return False
        
        try:
            with open(env_file, 'r') as f:
                content = f.read()
                if len(content.strip()) == 0:
                    self.warnings.append(".env file is empty")
        except Exception as e:
            self.errors.append(f"Cannot read .env file: {e}")
            return False
        
        return True


def validate_config() -> Tuple[bool, str]:
    """Validate configuration and return result"""
    validator = ConfigValidator()
    
    # Check .env file
    validator.check_environment_file()
    
    # Validate all settings
    is_valid, errors, warnings = validator.validate_all()
    
    # Generate report
    report = validator.generate_report()
    
    return is_valid, report


def main():
    """Main validation function for CLI usage"""
    print("🔍 AI Crypto Trading System - Configuration Validator")
    print("Author: inkbytefo")
    print("=" * 60)
    
    is_valid, report = validate_config()
    
    print(report)
    
    if is_valid:
        print("\n🎉 Configuration validation passed!")
        return 0
    else:
        print("\n💥 Configuration validation failed!")
        return 1


if __name__ == "__main__":
    import sys
    sys.exit(main())

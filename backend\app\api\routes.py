"""
API Routes for AI Crypto Trading System
Author: inkbytefo
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import List, Dict, Any
import logging
from datetime import datetime

from app.core.config import settings
# from app.api.v1.data_storage import router as data_storage_router  # Removed - using simple Telegram bot
from app.api.v1.auth import router as auth_router
from app.api.v1.users import router as users_router
from app.api.v1.technical_analysis import router as technical_analysis_router
from app.api.v1.pattern_detection import router as pattern_detection_router
from app.api.v1.signal_generation import router as signal_generation_router
from app.api.v1.ai_analysis import router as ai_analysis_router
from app.api.v1.telegram_bot import router as telegram_bot_router
from app.api.v1.backtesting import router as backtesting_router
from app.api.v1.system import router as system_router
from app.api.v1.optimization import router as optimization_router
from app.api.v1.walk_forward import router as walk_forward_router
from app.api.v1.market_regime import router as market_regime_router
from app.api.v1.circuit_breaker import router as circuit_breaker_router
from app.api.v1.metrics import router as metrics_router

# Create main API router
api_router = APIRouter()

# Data storage routes removed - using simple Telegram bot interface
# api_router.include_router(data_storage_router, prefix="/v1", tags=["Data Storage"])

# Include authentication routes
api_router.include_router(auth_router, prefix="/v1/auth", tags=["Authentication"])

# Include user management routes
api_router.include_router(users_router, prefix="/v1/users", tags=["User Management"])

# Include technical analysis routes
api_router.include_router(technical_analysis_router, prefix="/v1/technical", tags=["Technical Analysis"])

# Include pattern detection routes
api_router.include_router(pattern_detection_router, prefix="/v1/patterns", tags=["Pattern Detection"])

# Include signal generation routes
api_router.include_router(signal_generation_router, prefix="/v1/signals", tags=["Signal Generation"])

# Include AI analysis routes
api_router.include_router(ai_analysis_router, prefix="/v1/ai", tags=["AI Analysis"])

# Include Telegram bot routes
api_router.include_router(telegram_bot_router, prefix="/v1/telegram", tags=["Telegram Bot"])

# Include backtesting routes
api_router.include_router(backtesting_router, prefix="/v1/backtesting", tags=["Backtesting"])

# Include optimization routes
api_router.include_router(optimization_router, prefix="/v1/optimization", tags=["Optimization"])

# Include walk-forward analysis routes
api_router.include_router(walk_forward_router, prefix="/v1/walk-forward", tags=["Walk-Forward Analysis"])
api_router.include_router(market_regime_router, prefix="/v1/market-regime", tags=["Market Regime Detection"])
api_router.include_router(circuit_breaker_router, prefix="/v1/circuit-breaker", tags=["Circuit Breaker & Emergency Stop"])
api_router.include_router(metrics_router, prefix="/v1/metrics", tags=["Metrics & Monitoring"])

# Include system management routes
api_router.include_router(system_router, prefix="/v1/system", tags=["System Management"])

# Logger
logger = logging.getLogger("api")


@api_router.get("/status")
async def get_system_status():
    """Get system status and configuration"""
    return {
        "system": "AI Crypto Trading System",
        "version": settings.VERSION,
        "environment": settings.ENVIRONMENT,
        "trading_enabled": settings.ENABLE_TRADING,
        "trading_mode": settings.TRADING_MODE,
        "risk_level": settings.RISK_LEVEL,
        "max_daily_trades": settings.MAX_DAILY_TRADES,
        "default_symbols": settings.DEFAULT_SYMBOLS
    }


@api_router.get("/market/symbols")
async def get_supported_symbols(exchange: str = "binance"):
    """Get list of supported trading symbols"""
    try:
        from app.services.market_data_service import market_data_service

        logger.info(f"Supported symbols requested for {exchange}")

        # Get symbols from exchange
        symbols = await market_data_service.get_supported_symbols(exchange)

        return {
            "exchange": exchange,
            "symbols": symbols,
            "count": len(symbols),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error fetching supported symbols: {e}")
        # Fallback to default symbols
        return {
            "exchange": exchange,
            "symbols": settings.DEFAULT_SYMBOLS,
            "count": len(settings.DEFAULT_SYMBOLS),
            "status": "fallback"
        }


@api_router.get("/trading/config")
async def get_trading_config():
    """Get current trading configuration"""
    return {
        "enabled": settings.ENABLE_TRADING,
        "mode": settings.TRADING_MODE,
        "max_daily_trades": settings.MAX_DAILY_TRADES,
        "max_position_size": settings.MAX_POSITION_SIZE,
        "default_stop_loss": settings.DEFAULT_STOP_LOSS,
        "default_take_profit": settings.DEFAULT_TAKE_PROFIT,
        "risk_level": settings.RISK_LEVEL,
        "max_drawdown": settings.MAX_DRAWDOWN
    }


# Market Data Endpoints
@api_router.get("/market/data/{symbol}")
async def get_market_data(
    symbol: str,
    timeframe: str = "1h",
    limit: int = 100,
    exchange: str = "binance"
):
    """Get OHLCV market data for a symbol"""
    try:
        from app.services.market_data_service import market_data_service

        logger.info(f"Market data requested for {symbol} ({timeframe})")

        # Validate symbol format
        if '/' not in symbol:
            symbol = f"{symbol}/USDT"

        # Get OHLCV data
        data = await market_data_service.get_ohlcv_data(
            symbol=symbol,
            timeframe=timeframe,
            limit=limit,
            exchange=exchange
        )

        if not data:
            raise HTTPException(status_code=404, detail=f"No data found for {symbol}")

        return {
            "symbol": symbol,
            "exchange": exchange,
            "timeframe": timeframe,
            "data_points": len(data),
            "data": data,
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error fetching market data for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/market/ticker/{symbol}")
async def get_ticker_data(symbol: str, exchange: str = "binance"):
    """Get current ticker data for a symbol"""
    try:
        from app.services.market_data_service import market_data_service

        logger.info(f"Ticker data requested for {symbol}")

        # Validate symbol format
        if '/' not in symbol:
            symbol = f"{symbol}/USDT"

        # Get ticker data
        ticker = await market_data_service.get_ticker_data(symbol, exchange)

        if not ticker:
            raise HTTPException(status_code=404, detail=f"No ticker data found for {symbol}")

        return {
            "symbol": symbol,
            "ticker": ticker,
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error fetching ticker data for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/market/price/{symbol}")
async def get_current_price(symbol: str, exchange: str = "binance"):
    """Get current price for a symbol"""
    try:
        from app.services.market_data_service import market_data_service

        logger.info(f"Current price requested for {symbol}")

        # Validate symbol format
        if '/' not in symbol:
            symbol = f"{symbol}/USDT"

        # Get current price
        price = await market_data_service.get_current_price(symbol, exchange)

        if price is None:
            raise HTTPException(status_code=404, detail=f"No price data found for {symbol}")

        return {
            "symbol": symbol,
            "exchange": exchange,
            "price": price,
            "timestamp": datetime.now(),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error fetching current price for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/analysis/technical/{symbol}")
async def get_technical_analysis(symbol: str, timeframe: str = "1h"):
    """Get technical analysis for a symbol (legacy endpoint - redirects to new API)"""
    logger.info(f"Legacy technical analysis requested for {symbol}")

    try:
        from app.services.technical_analysis_service import technical_analysis_service

        # Validate symbol format
        if '/' not in symbol:
            symbol = f"{symbol}/USDT"

        # Get technical analysis
        signal = await technical_analysis_service.analyze_symbol(symbol, timeframe)

        if not signal:
            return {
                "symbol": symbol,
                "message": "Unable to perform technical analysis",
                "status": "error"
            }

        return {
            "symbol": symbol,
            "signal_type": signal.signal_type.value,
            "strength": round(signal.strength, 3),
            "confidence": round(signal.confidence, 3),
            "reasoning": signal.reasoning,
            "rsi": signal.indicators.rsi,
            "macd": signal.indicators.macd,
            "trend": signal.indicators.trend_direction.value if signal.indicators.trend_direction else "unknown",
            "message": "Use /api/v1/technical/analyze/{symbol} for detailed analysis",
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error in legacy technical analysis for {symbol}: {e}")
        return {
            "symbol": symbol,
            "message": f"Analysis failed: {str(e)}",
            "status": "error"
        }


@api_router.get("/analysis/ai/{symbol}")
async def get_ai_analysis(symbol: str):
    """Get AI analysis for a symbol (placeholder)"""
    logger.info(f"AI analysis requested for {symbol}")
    return {
        "symbol": symbol,
        "message": "AI analysis endpoint - to be implemented",
        "status": "placeholder"
    }


@api_router.get("/portfolio/status")
async def get_portfolio_status():
    """Get portfolio status (placeholder)"""
    logger.info("Portfolio status requested")
    return {
        "message": "Portfolio status endpoint - to be implemented",
        "status": "placeholder"
    }


@api_router.get("/trades/history")
async def get_trade_history():
    """Get trade history (placeholder)"""
    logger.info("Trade history requested")
    return {
        "message": "Trade history endpoint - to be implemented",
        "status": "placeholder"
    }

"""
Walk-Forward Analysis Service for Strategy Validation
Prevents overfitting by splitting data into in-sample and out-of-sample periods
Author: inkbytefo
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import pandas as pd
import numpy as np

from app.services.backtesting_engine import BacktestingEngine, BacktestPerformanceMetrics
from app.services.optimization_service import OptimizationService, ParameterRange
from app.models.backtesting import BacktestStrategy
from app.core.logging import performance_monitor

logger = logging.getLogger(__name__)

@dataclass
class WalkForwardPeriod:
    """Single walk-forward analysis period"""
    period_id: int
    in_sample_start: datetime
    in_sample_end: datetime
    out_sample_start: datetime
    out_sample_end: datetime
    optimized_parameters: Dict[str, Any]
    in_sample_metrics: Optional[BacktestPerformanceMetrics] = None
    out_sample_metrics: Optional[BacktestPerformanceMetrics] = None

@dataclass
class WalkForwardResult:
    """Complete walk-forward analysis results"""
    strategy_id: int
    symbol: str
    timeframe: str
    total_periods: int
    periods: List[WalkForwardPeriod]
    
    # Aggregated metrics
    avg_in_sample_return: float
    avg_out_sample_return: float
    in_sample_sharpe: float
    out_sample_sharpe: float
    
    # Robustness metrics
    consistency_score: float  # How consistent are out-of-sample results
    overfitting_score: float  # Difference between in-sample and out-of-sample performance
    stability_score: float    # Parameter stability across periods
    
    # Summary
    is_robust: bool
    robustness_rating: str  # "Excellent", "Good", "Fair", "Poor"
    recommendations: List[str]

class WalkForwardAnalysisService:
    """Service for conducting walk-forward analysis"""
    
    def __init__(self):
        self.backtesting_engine = BacktestingEngine()
        self.optimization_service = OptimizationService()
    
    @performance_monitor("walk_forward_analysis")
    async def run_walk_forward_analysis(
        self,
        strategy_id: int,
        symbol: str,
        timeframe: str,
        start_date: datetime,
        end_date: datetime,
        parameter_ranges: Dict[str, ParameterRange],
        optimization_metric: str = "sharpe_ratio",
        in_sample_ratio: float = 0.7,
        step_size_months: int = 3,
        min_out_sample_months: int = 1
    ) -> WalkForwardResult:
        """
        Run complete walk-forward analysis
        
        Args:
            strategy_id: Strategy to analyze
            symbol: Trading symbol
            timeframe: Data timeframe
            start_date: Analysis start date
            end_date: Analysis end date
            parameter_ranges: Parameters to optimize
            optimization_metric: Metric to optimize for
            in_sample_ratio: Ratio of data for in-sample (0.7 = 70%)
            step_size_months: How many months to step forward each period
            min_out_sample_months: Minimum months for out-of-sample testing
        """
        
        logger.info(f"Starting walk-forward analysis for strategy {strategy_id} on {symbol}")
        
        # Generate walk-forward periods
        periods = self._generate_walk_forward_periods(
            start_date, end_date, in_sample_ratio, step_size_months, min_out_sample_months
        )
        
        logger.info(f"Generated {len(periods)} walk-forward periods")
        
        # Process each period
        completed_periods = []
        for period in periods:
            try:
                logger.info(f"Processing period {period.period_id}")
                
                # Step 1: Optimize parameters on in-sample data
                optimization_result = await self.optimization_service.run_grid_search(
                    strategy_id=strategy_id,
                    symbol=symbol,
                    timeframe=timeframe,
                    start_date=period.in_sample_start,
                    end_date=period.in_sample_end,
                    parameter_ranges=parameter_ranges,
                    optimization_metric=optimization_metric,
                    max_combinations=500  # Limit for faster processing
                )
                
                period.optimized_parameters = optimization_result.best_parameters
                
                # Step 2: Test optimized parameters on in-sample data (for comparison)
                period.in_sample_metrics = await self._run_backtest_for_period(
                    strategy_id, symbol, timeframe,
                    period.in_sample_start, period.in_sample_end,
                    period.optimized_parameters
                )
                
                # Step 3: Test optimized parameters on out-of-sample data
                period.out_sample_metrics = await self._run_backtest_for_period(
                    strategy_id, symbol, timeframe,
                    period.out_sample_start, period.out_sample_end,
                    period.optimized_parameters
                )
                
                completed_periods.append(period)
                logger.info(f"Period {period.period_id} completed successfully")
                
            except Exception as e:
                logger.error(f"Error processing period {period.period_id}: {e}")
                continue
        
        # Calculate aggregated results
        result = self._calculate_walk_forward_results(
            strategy_id, symbol, timeframe, completed_periods
        )
        
        logger.info(f"Walk-forward analysis completed. Robustness rating: {result.robustness_rating}")
        
        return result
    
    def _generate_walk_forward_periods(
        self,
        start_date: datetime,
        end_date: datetime,
        in_sample_ratio: float,
        step_size_months: int,
        min_out_sample_months: int
    ) -> List[WalkForwardPeriod]:
        """Generate walk-forward periods"""
        
        periods = []
        period_id = 1
        current_start = start_date
        
        while current_start < end_date:
            # Calculate period end (ensure we have enough data)
            total_period_months = int(step_size_months / (1 - in_sample_ratio))
            period_end = current_start + timedelta(days=total_period_months * 30)
            
            if period_end > end_date:
                break
            
            # Calculate in-sample and out-of-sample splits
            total_days = (period_end - current_start).days
            in_sample_days = int(total_days * in_sample_ratio)
            
            in_sample_start = current_start
            in_sample_end = current_start + timedelta(days=in_sample_days)
            out_sample_start = in_sample_end + timedelta(days=1)
            out_sample_end = period_end
            
            # Ensure minimum out-of-sample period
            if (out_sample_end - out_sample_start).days < (min_out_sample_months * 30):
                break
            
            period = WalkForwardPeriod(
                period_id=period_id,
                in_sample_start=in_sample_start,
                in_sample_end=in_sample_end,
                out_sample_start=out_sample_start,
                out_sample_end=out_sample_end,
                optimized_parameters={}
            )
            
            periods.append(period)
            
            # Move to next period
            current_start += timedelta(days=step_size_months * 30)
            period_id += 1
        
        return periods
    
    async def _run_backtest_for_period(
        self,
        strategy_id: int,
        symbol: str,
        timeframe: str,
        start_date: datetime,
        end_date: datetime,
        parameters: Dict[str, Any]
    ) -> Optional[BacktestPerformanceMetrics]:
        """Run backtest for a specific period with given parameters"""
        
        try:
            # Run backtest with parameters
            backtest_run_id = await self.backtesting_engine.run_backtest(
                strategy_id=strategy_id,
                symbols=[symbol],
                timeframes=[timeframe],
                start_date=start_date,
                end_date=end_date,
                initial_capital=10000.0,
                user_id=1,  # System user for walk-forward analysis
                name=f"WF_Analysis_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}",
                description="Walk-forward analysis backtest",
                parameters=parameters
            )
            
            # Wait for completion and get results
            # Note: In a real implementation, you'd wait for the backtest to complete
            # For now, we'll return a placeholder
            return None
            
        except Exception as e:
            logger.error(f"Error running backtest for period: {e}")
            return None
    
    def _calculate_walk_forward_results(
        self,
        strategy_id: int,
        symbol: str,
        timeframe: str,
        periods: List[WalkForwardPeriod]
    ) -> WalkForwardResult:
        """Calculate aggregated walk-forward results"""
        
        if not periods:
            return self._empty_walk_forward_result(strategy_id, symbol, timeframe)
        
        # Filter periods with valid results
        valid_periods = [p for p in periods if p.in_sample_metrics and p.out_sample_metrics]
        
        if not valid_periods:
            return self._empty_walk_forward_result(strategy_id, symbol, timeframe)
        
        # Calculate average returns
        in_sample_returns = [p.in_sample_metrics.total_return_percentage for p in valid_periods]
        out_sample_returns = [p.out_sample_metrics.total_return_percentage for p in valid_periods]
        
        avg_in_sample_return = np.mean(in_sample_returns)
        avg_out_sample_return = np.mean(out_sample_returns)
        
        # Calculate Sharpe ratios
        in_sample_sharpes = [p.in_sample_metrics.sharpe_ratio for p in valid_periods]
        out_sample_sharpes = [p.out_sample_metrics.sharpe_ratio for p in valid_periods]
        
        in_sample_sharpe = np.mean(in_sample_sharpes)
        out_sample_sharpe = np.mean(out_sample_sharpes)
        
        # Calculate robustness metrics
        consistency_score = self._calculate_consistency_score(out_sample_returns)
        overfitting_score = self._calculate_overfitting_score(in_sample_returns, out_sample_returns)
        stability_score = self._calculate_parameter_stability(valid_periods)
        
        # Determine robustness
        is_robust, robustness_rating, recommendations = self._evaluate_robustness(
            consistency_score, overfitting_score, stability_score,
            avg_in_sample_return, avg_out_sample_return
        )
        
        return WalkForwardResult(
            strategy_id=strategy_id,
            symbol=symbol,
            timeframe=timeframe,
            total_periods=len(valid_periods),
            periods=valid_periods,
            avg_in_sample_return=avg_in_sample_return,
            avg_out_sample_return=avg_out_sample_return,
            in_sample_sharpe=in_sample_sharpe,
            out_sample_sharpe=out_sample_sharpe,
            consistency_score=consistency_score,
            overfitting_score=overfitting_score,
            stability_score=stability_score,
            is_robust=is_robust,
            robustness_rating=robustness_rating,
            recommendations=recommendations
        )
    
    def _calculate_consistency_score(self, returns: List[float]) -> float:
        """Calculate how consistent the out-of-sample returns are"""
        if len(returns) < 2:
            return 0.0
        
        # Lower standard deviation = higher consistency
        std_dev = np.std(returns)
        mean_return = np.mean(returns)
        
        # Normalize by mean return to get coefficient of variation
        if abs(mean_return) > 0.01:  # Avoid division by zero
            cv = std_dev / abs(mean_return)
            # Convert to 0-100 scale (lower CV = higher score)
            consistency_score = max(0, 100 - (cv * 50))
        else:
            consistency_score = 50  # Neutral score for near-zero returns
        
        return min(100, consistency_score)
    
    def _calculate_overfitting_score(self, in_sample: List[float], out_sample: List[float]) -> float:
        """Calculate overfitting score (lower is better)"""
        if not in_sample or not out_sample:
            return 100  # Maximum overfitting if no data
        
        avg_in = np.mean(in_sample)
        avg_out = np.mean(out_sample)
        
        # Calculate performance degradation
        if avg_in > 0:
            degradation = ((avg_in - avg_out) / avg_in) * 100
        else:
            degradation = 0
        
        # Convert to 0-100 scale (0 = no overfitting, 100 = severe overfitting)
        overfitting_score = max(0, min(100, degradation))
        
        return overfitting_score
    
    def _calculate_parameter_stability(self, periods: List[WalkForwardPeriod]) -> float:
        """Calculate how stable the optimized parameters are across periods"""
        if len(periods) < 2:
            return 100  # Perfect stability with only one period
        
        # For now, return a placeholder score
        # In a real implementation, you'd analyze parameter variance across periods
        return 75.0
    
    def _evaluate_robustness(
        self,
        consistency_score: float,
        overfitting_score: float,
        stability_score: float,
        avg_in_sample: float,
        avg_out_sample: float
    ) -> Tuple[bool, str, List[str]]:
        """Evaluate overall robustness and provide recommendations"""
        
        recommendations = []
        
        # Calculate overall robustness score
        robustness_score = (consistency_score + (100 - overfitting_score) + stability_score) / 3
        
        # Determine rating
        if robustness_score >= 80:
            rating = "Excellent"
            is_robust = True
        elif robustness_score >= 65:
            rating = "Good"
            is_robust = True
        elif robustness_score >= 50:
            rating = "Fair"
            is_robust = False
            recommendations.append("Consider reducing strategy complexity to improve robustness")
        else:
            rating = "Poor"
            is_robust = False
            recommendations.append("Strategy shows signs of overfitting - major revision needed")
        
        # Specific recommendations
        if overfitting_score > 30:
            recommendations.append("High overfitting detected - simplify strategy or increase regularization")
        
        if consistency_score < 60:
            recommendations.append("Inconsistent performance - consider adding market regime filters")
        
        if stability_score < 60:
            recommendations.append("Parameter instability detected - use more robust optimization methods")
        
        if avg_out_sample < 0:
            recommendations.append("Negative out-of-sample returns - strategy may not be profitable")
        
        return is_robust, rating, recommendations
    
    def _empty_walk_forward_result(self, strategy_id: int, symbol: str, timeframe: str) -> WalkForwardResult:
        """Return empty result for failed analysis"""
        return WalkForwardResult(
            strategy_id=strategy_id,
            symbol=symbol,
            timeframe=timeframe,
            total_periods=0,
            periods=[],
            avg_in_sample_return=0.0,
            avg_out_sample_return=0.0,
            in_sample_sharpe=0.0,
            out_sample_sharpe=0.0,
            consistency_score=0.0,
            overfitting_score=100.0,
            stability_score=0.0,
            is_robust=False,
            robustness_rating="Failed",
            recommendations=["Analysis failed - check data availability and parameters"]
        )

# Global instance
walk_forward_service = WalkForwardAnalysisService()

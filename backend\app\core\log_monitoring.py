"""
Log Monitoring and Alerting System
Author: inkbytefo

Advanced log monitoring with:
- Real-time error detection
- Performance threshold monitoring
- Alert aggregation and rate limiting
- Health check integration
- Automated incident response
"""

import asyncio
import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque
import time
from pathlib import Path

from app.core.config import settings


class AlertLevel(Enum):
    """Alert severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AlertType(Enum):
    """Types of alerts"""
    ERROR_RATE = "error_rate"
    PERFORMANCE = "performance"
    SYSTEM_HEALTH = "system_health"
    TRADING_ANOMALY = "trading_anomaly"
    SECURITY = "security"


@dataclass
class Alert:
    """Alert data structure"""
    alert_id: str
    alert_type: AlertType
    level: AlertLevel
    title: str
    message: str
    timestamp: datetime
    source: str
    metadata: Dict[str, Any] = field(default_factory=dict)
    resolved: bool = False
    resolved_at: Optional[datetime] = None


@dataclass
class PerformanceThreshold:
    """Performance monitoring threshold"""
    operation: str
    max_duration_ms: float
    max_memory_mb: float
    error_rate_threshold: float = 0.05  # 5%
    sample_window_minutes: int = 5


class LogMonitor:
    """Real-time log monitoring and alerting"""
    
    def __init__(self):
        self.alerts: List[Alert] = []
        self.alert_handlers: List[Callable] = []
        self.performance_data: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.error_counts: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        self.last_alert_times: Dict[str, datetime] = {}
        self.alert_rate_limits = {
            AlertLevel.LOW: timedelta(minutes=30),
            AlertLevel.MEDIUM: timedelta(minutes=15),
            AlertLevel.HIGH: timedelta(minutes=5),
            AlertLevel.CRITICAL: timedelta(minutes=1)
        }
        
        # Performance thresholds
        self.performance_thresholds = {
            "signal_generation": PerformanceThreshold(
                operation="signal_generation",
                max_duration_ms=5000,  # 5 seconds
                max_memory_mb=100,
                error_rate_threshold=0.02
            ),
            "technical_analysis": PerformanceThreshold(
                operation="technical_analysis",
                max_duration_ms=3000,  # 3 seconds
                max_memory_mb=50,
                error_rate_threshold=0.01
            ),
            "pattern_detection": PerformanceThreshold(
                operation="pattern_detection",
                max_duration_ms=2000,  # 2 seconds
                max_memory_mb=75,
                error_rate_threshold=0.02
            ),
            "backtesting": PerformanceThreshold(
                operation="backtesting",
                max_duration_ms=60000,  # 60 seconds
                max_memory_mb=500,
                error_rate_threshold=0.05
            )
        }
        
        self.is_monitoring = False
        self.monitor_task: Optional[asyncio.Task] = None
    
    def add_alert_handler(self, handler: Callable[[Alert], None]):
        """Add alert handler function"""
        self.alert_handlers.append(handler)
    
    def record_performance(self, operation: str, duration_ms: float, memory_mb: float = 0, status: str = "success"):
        """Record performance data"""
        timestamp = datetime.now()
        
        self.performance_data[operation].append({
            "timestamp": timestamp,
            "duration_ms": duration_ms,
            "memory_mb": memory_mb,
            "status": status
        })
        
        # Check thresholds
        self._check_performance_thresholds(operation)
    
    def record_error(self, source: str, error_type: str, message: str):
        """Record error occurrence"""
        timestamp = datetime.now()
        
        self.error_counts[source].append({
            "timestamp": timestamp,
            "error_type": error_type,
            "message": message
        })
        
        # Check error rate thresholds
        self._check_error_rate_thresholds(source)
    
    def _check_performance_thresholds(self, operation: str):
        """Check if performance thresholds are exceeded"""
        if operation not in self.performance_thresholds:
            return
        
        threshold = self.performance_thresholds[operation]
        recent_data = list(self.performance_data[operation])
        
        if not recent_data:
            return
        
        # Check recent performance
        cutoff_time = datetime.now() - timedelta(minutes=threshold.sample_window_minutes)
        recent_data = [d for d in recent_data if d["timestamp"] > cutoff_time]
        
        if len(recent_data) < 5:  # Need minimum samples
            return
        
        # Check duration threshold
        avg_duration = sum(d["duration_ms"] for d in recent_data) / len(recent_data)
        if avg_duration > threshold.max_duration_ms:
            self._create_alert(
                alert_type=AlertType.PERFORMANCE,
                level=AlertLevel.MEDIUM,
                title=f"High latency detected: {operation}",
                message=f"Average duration {avg_duration:.1f}ms exceeds threshold {threshold.max_duration_ms}ms",
                source=operation,
                metadata={"avg_duration_ms": avg_duration, "threshold_ms": threshold.max_duration_ms}
            )
        
        # Check memory threshold
        if recent_data and any(d.get("memory_mb", 0) > threshold.max_memory_mb for d in recent_data):
            max_memory = max(d.get("memory_mb", 0) for d in recent_data)
            self._create_alert(
                alert_type=AlertType.PERFORMANCE,
                level=AlertLevel.MEDIUM,
                title=f"High memory usage: {operation}",
                message=f"Memory usage {max_memory:.1f}MB exceeds threshold {threshold.max_memory_mb}MB",
                source=operation,
                metadata={"max_memory_mb": max_memory, "threshold_mb": threshold.max_memory_mb}
            )
        
        # Check error rate
        error_count = sum(1 for d in recent_data if d["status"] == "error")
        error_rate = error_count / len(recent_data) if recent_data else 0
        
        if error_rate > threshold.error_rate_threshold:
            self._create_alert(
                alert_type=AlertType.PERFORMANCE,
                level=AlertLevel.HIGH,
                title=f"High error rate: {operation}",
                message=f"Error rate {error_rate:.1%} exceeds threshold {threshold.error_rate_threshold:.1%}",
                source=operation,
                metadata={"error_rate": error_rate, "threshold": threshold.error_rate_threshold}
            )
    
    def _check_error_rate_thresholds(self, source: str):
        """Check error rate thresholds"""
        recent_errors = list(self.error_counts[source])
        
        if len(recent_errors) < 5:
            return
        
        # Check error frequency in last 5 minutes
        cutoff_time = datetime.now() - timedelta(minutes=5)
        recent_errors = [e for e in recent_errors if e["timestamp"] > cutoff_time]
        
        if len(recent_errors) >= 10:  # 10+ errors in 5 minutes
            self._create_alert(
                alert_type=AlertType.ERROR_RATE,
                level=AlertLevel.HIGH,
                title=f"High error rate: {source}",
                message=f"{len(recent_errors)} errors in last 5 minutes",
                source=source,
                metadata={"error_count": len(recent_errors), "time_window": "5_minutes"}
            )
    
    def _create_alert(self, alert_type: AlertType, level: AlertLevel, title: str, message: str, source: str, metadata: Dict = None):
        """Create and process new alert"""
        
        # Rate limiting
        alert_key = f"{alert_type.value}_{source}_{title}"
        now = datetime.now()
        
        if alert_key in self.last_alert_times:
            time_since_last = now - self.last_alert_times[alert_key]
            if time_since_last < self.alert_rate_limits[level]:
                return  # Skip due to rate limiting
        
        self.last_alert_times[alert_key] = now
        
        # Create alert
        alert = Alert(
            alert_id=f"{int(now.timestamp())}_{alert_type.value}_{source}",
            alert_type=alert_type,
            level=level,
            title=title,
            message=message,
            timestamp=now,
            source=source,
            metadata=metadata or {}
        )
        
        self.alerts.append(alert)
        
        # Trigger alert handlers
        for handler in self.alert_handlers:
            try:
                handler(alert)
            except Exception as e:
                logging.error(f"Error in alert handler: {e}")
        
        # Log the alert
        logging.warning(f"ALERT [{level.value.upper()}] {title}: {message}")
    
    async def start_monitoring(self):
        """Start background monitoring"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitor_task = asyncio.create_task(self._monitoring_loop())
        logging.info("Log monitoring started")
    
    async def stop_monitoring(self):
        """Stop background monitoring"""
        self.is_monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        logging.info("Log monitoring stopped")
    
    async def _monitoring_loop(self):
        """Background monitoring loop"""
        while self.is_monitoring:
            try:
                # Perform periodic health checks
                await self._perform_health_checks()
                
                # Clean old data
                self._cleanup_old_data()
                
                # Sleep for monitoring interval
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logging.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(60)  # Wait longer on error
    
    async def _perform_health_checks(self):
        """Perform system health checks"""
        try:
            # Check log file sizes
            log_dir = Path("logs")
            if log_dir.exists():
                for log_file in log_dir.glob("*.log"):
                    size_mb = log_file.stat().st_size / (1024 * 1024)
                    if size_mb > 100:  # 100MB threshold
                        self._create_alert(
                            alert_type=AlertType.SYSTEM_HEALTH,
                            level=AlertLevel.MEDIUM,
                            title="Large log file detected",
                            message=f"Log file {log_file.name} is {size_mb:.1f}MB",
                            source="log_monitoring",
                            metadata={"file": str(log_file), "size_mb": size_mb}
                        )
            
            # Check memory usage (simplified)
            import psutil
            memory_percent = psutil.virtual_memory().percent
            if memory_percent > 85:
                self._create_alert(
                    alert_type=AlertType.SYSTEM_HEALTH,
                    level=AlertLevel.HIGH,
                    title="High system memory usage",
                    message=f"System memory usage at {memory_percent:.1f}%",
                    source="system",
                    metadata={"memory_percent": memory_percent}
                )
                
        except Exception as e:
            logging.error(f"Error in health checks: {e}")
    
    def _cleanup_old_data(self):
        """Clean up old monitoring data"""
        cutoff_time = datetime.now() - timedelta(hours=24)
        
        # Clean performance data
        for operation in self.performance_data:
            data = self.performance_data[operation]
            while data and data[0]["timestamp"] < cutoff_time:
                data.popleft()
        
        # Clean error data
        for source in self.error_counts:
            errors = self.error_counts[source]
            while errors and errors[0]["timestamp"] < cutoff_time:
                errors.popleft()
        
        # Clean old alerts
        self.alerts = [a for a in self.alerts if a.timestamp > cutoff_time]
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get current system health status"""
        now = datetime.now()
        last_hour = now - timedelta(hours=1)
        
        # Count recent alerts by level
        recent_alerts = [a for a in self.alerts if a.timestamp > last_hour]
        alert_counts = defaultdict(int)
        for alert in recent_alerts:
            alert_counts[alert.level.value] += 1
        
        # Calculate average performance
        avg_performance = {}
        for operation, data in self.performance_data.items():
            recent_data = [d for d in data if d["timestamp"] > last_hour]
            if recent_data:
                avg_duration = sum(d["duration_ms"] for d in recent_data) / len(recent_data)
                error_rate = sum(1 for d in recent_data if d["status"] == "error") / len(recent_data)
                avg_performance[operation] = {
                    "avg_duration_ms": round(avg_duration, 2),
                    "error_rate": round(error_rate, 4),
                    "sample_count": len(recent_data)
                }
        
        # Overall health score (0-100)
        health_score = 100
        if alert_counts["critical"] > 0:
            health_score -= 30
        if alert_counts["high"] > 0:
            health_score -= 20
        if alert_counts["medium"] > 2:
            health_score -= 15
        if alert_counts["low"] > 5:
            health_score -= 10
        
        health_score = max(0, health_score)
        
        return {
            "health_score": health_score,
            "status": "healthy" if health_score > 80 else "degraded" if health_score > 50 else "unhealthy",
            "recent_alerts": alert_counts,
            "performance_metrics": avg_performance,
            "monitoring_active": self.is_monitoring,
            "last_check": now.isoformat()
        }


# Global monitor instance
log_monitor = LogMonitor()


# Alert handlers
def console_alert_handler(alert: Alert):
    """Console alert handler"""
    print(f"\n🚨 ALERT [{alert.level.value.upper()}] - {alert.title}")
    print(f"   Source: {alert.source}")
    print(f"   Message: {alert.message}")
    print(f"   Time: {alert.timestamp}")
    if alert.metadata:
        print(f"   Metadata: {alert.metadata}")
    print()


def file_alert_handler(alert: Alert):
    """File alert handler"""
    alert_data = {
        "alert_id": alert.alert_id,
        "type": alert.alert_type.value,
        "level": alert.level.value,
        "title": alert.title,
        "message": alert.message,
        "source": alert.source,
        "timestamp": alert.timestamp.isoformat(),
        "metadata": alert.metadata
    }
    
    # Write to alerts log file
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    with open(log_dir / "alerts.log", "a", encoding="utf-8") as f:
        f.write(json.dumps(alert_data, ensure_ascii=False) + "\n")


# Setup default alert handlers
log_monitor.add_alert_handler(console_alert_handler)
log_monitor.add_alert_handler(file_alert_handler)

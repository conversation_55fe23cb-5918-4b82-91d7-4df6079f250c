"""
Unit tests for AI Analysis Service
Author: inkbytefo
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch
from typing import List

from app.services.ai_analysis_service import (
    AIAnalysisService,
    NewsArticle,
    SentimentAnalysis,
    SentimentType,
    AIAnalysisResult
)


@pytest.fixture
def ai_service():
    """Create AI analysis service instance."""
    return AIAnalysisService()


@pytest.fixture
def sample_news_articles():
    """Sample news articles for testing."""
    return [
        NewsArticle(
            title="Bitcoin Reaches New High",
            content="Bitcoin has reached a new all-time high today...",
            url="https://example.com/news1",
            published_at=datetime.now(),
            source="CryptoNews",
            relevance_score=0.9
        ),
        NewsArticle(
            title="Ethereum Network Upgrade",
            content="Ethereum network has successfully upgraded...",
            url="https://example.com/news2",
            published_at=datetime.now() - timedelta(hours=2),
            source="EthNews",
            relevance_score=0.8
        ),
        NewsArticle(
            title="Market Volatility Concerns",
            content="Crypto market shows signs of volatility...",
            url="https://example.com/news3",
            published_at=datetime.now() - timedelta(hours=4),
            source="MarketWatch",
            relevance_score=0.7
        )
    ]


@pytest.fixture
def sample_sentiment_analysis():
    """Sample sentiment analysis for testing."""
    return SentimentAnalysis(
        symbol="BTC",
        overall_sentiment=SentimentType.BULLISH,
        sentiment_score=0.75,
        confidence=0.85,
        news_count=10,
        bullish_signals=["New ATH", "Institutional adoption"],
        bearish_signals=["Regulatory concerns"],
        key_themes=["Adoption", "Price action"],
        analysis_timestamp=datetime.now()
    )


class TestAIAnalysisService:
    """Test cases for AI Analysis Service."""

    @pytest.mark.asyncio
    async def test_get_crypto_news_cached(self, ai_service):
        """Test getting crypto news with caching."""
        with patch('app.services.ai_analysis_service.cache_manager') as mock_cache:
            # Mock cached data
            cached_news = [
                {
                    "title": "Cached News",
                    "content": "Cached content",
                    "url": "https://cached.com",
                    "published_at": datetime.now().isoformat(),
                    "source": "Cache",
                    "relevance_score": 0.8
                }
            ]
            mock_cache.get.return_value = str(cached_news).replace("'", '"')
            
            news = await ai_service.get_crypto_news("BTC", limit=5)
            
            assert isinstance(news, list)
            mock_cache.get.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_crypto_news_fresh(self, ai_service):
        """Test getting fresh crypto news."""
        with patch('app.services.ai_analysis_service.cache_manager') as mock_cache:
            mock_cache.get.return_value = None  # No cached data
            
            with patch.object(ai_service, '_fetch_coingecko_news') as mock_coingecko:
                with patch.object(ai_service, '_fetch_newsapi_crypto') as mock_newsapi:
                    with patch.object(ai_service, '_fetch_rss_crypto_news') as mock_rss:
                        # Mock news sources
                        mock_coingecko.return_value = []
                        mock_newsapi.return_value = []
                        mock_rss.return_value = []
                        
                        news = await ai_service.get_crypto_news("BTC", limit=5)
                        
                        assert isinstance(news, list)
                        mock_coingecko.assert_called_once()
                        mock_newsapi.assert_called_once()
                        mock_rss.assert_called_once()

    @pytest.mark.asyncio
    async def test_analyze_sentiment_with_news(self, ai_service, sample_news_articles):
        """Test sentiment analysis with provided news."""
        with patch.object(ai_service, '_analyze_sentiment_with_ai') as mock_ai_analysis:
            mock_ai_analysis.return_value = SentimentAnalysis(
                symbol="BTC",
                overall_sentiment=SentimentType.BULLISH,
                sentiment_score=0.8,
                confidence=0.9,
                news_count=3,
                bullish_signals=["Positive news"],
                bearish_signals=[],
                key_themes=["Growth"],
                analysis_timestamp=datetime.now()
            )
            
            sentiment = await ai_service.analyze_sentiment("BTC", sample_news_articles)
            
            assert isinstance(sentiment, SentimentAnalysis)
            assert sentiment.symbol == "BTC"
            assert sentiment.overall_sentiment == SentimentType.BULLISH
            assert sentiment.news_count == 3
            mock_ai_analysis.assert_called_once()

    @pytest.mark.asyncio
    async def test_analyze_sentiment_no_news(self, ai_service):
        """Test sentiment analysis with no news articles."""
        sentiment = await ai_service.analyze_sentiment("BTC", [])
        
        assert isinstance(sentiment, SentimentAnalysis)
        assert sentiment.symbol == "BTC"
        assert sentiment.overall_sentiment == SentimentType.NEUTRAL
        assert sentiment.sentiment_score == 0.0
        assert sentiment.confidence == 0.0
        assert sentiment.news_count == 0

    @pytest.mark.asyncio
    async def test_analyze_sentiment_cached(self, ai_service, sample_news_articles):
        """Test sentiment analysis with cached results."""
        with patch('app.services.ai_analysis_service.cache_manager') as mock_cache:
            # Mock cached sentiment
            cached_sentiment = {
                "symbol": "BTC",
                "overall_sentiment": "bullish",
                "sentiment_score": 0.8,
                "confidence": 0.9,
                "news_count": 3,
                "bullish_signals": ["Positive news"],
                "bearish_signals": [],
                "key_themes": ["Growth"],
                "analysis_timestamp": datetime.now().isoformat()
            }
            mock_cache.get.return_value = str(cached_sentiment).replace("'", '"')
            
            sentiment = await ai_service.analyze_sentiment("BTC", sample_news_articles)
            
            assert isinstance(sentiment, SentimentAnalysis)
            mock_cache.get.assert_called_once()

    @pytest.mark.asyncio
    async def test_fallback_sentiment_analysis(self, ai_service):
        """Test fallback sentiment analysis."""
        news_content = "Bitcoin price surges to new highs. Bullish momentum continues."
        
        sentiment = ai_service._fallback_sentiment_analysis("BTC", news_content)
        
        assert isinstance(sentiment, SentimentAnalysis)
        assert sentiment.symbol == "BTC"
        assert sentiment.confidence == 0.6  # Lower confidence for fallback

    @pytest.mark.asyncio
    async def test_generate_comprehensive_analysis(self, ai_service):
        """Test comprehensive AI analysis generation."""
        with patch.object(ai_service, 'get_crypto_news') as mock_news:
            with patch.object(ai_service, 'analyze_sentiment') as mock_sentiment:
                with patch.object(ai_service, '_generate_market_insights') as mock_insights:
                    with patch.object(ai_service, '_generate_ai_recommendation') as mock_recommendation:
                        # Mock dependencies
                        mock_news.return_value = []
                        mock_sentiment.return_value = SentimentAnalysis(
                            symbol="BTC",
                            overall_sentiment=SentimentType.BULLISH,
                            sentiment_score=0.8,
                            confidence=0.9,
                            news_count=0,
                            bullish_signals=[],
                            bearish_signals=[],
                            key_themes=[],
                            analysis_timestamp=datetime.now()
                        )
                        mock_insights.return_value = ["Market insight 1"]
                        mock_recommendation.return_value = ("BUY", 0.8, "High confidence", ["Factor 1"])
                        
                        analysis = await ai_service.generate_comprehensive_analysis("BTC")
                        
                        assert isinstance(analysis, AIAnalysisResult)
                        assert analysis.symbol == "BTC"
                        assert analysis.sentiment_analysis is not None
                        assert isinstance(analysis.market_insights, list)
                        assert analysis.ai_recommendation is not None

    @pytest.mark.asyncio
    async def test_generate_comprehensive_analysis_cached(self, ai_service):
        """Test comprehensive analysis with cached results."""
        with patch('app.services.ai_analysis_service.cache_manager') as mock_cache:
            # Mock cached analysis
            cached_analysis = {
                "symbol": "BTC",
                "timeframe": "1h",
                "sentiment_analysis": {
                    "symbol": "BTC",
                    "overall_sentiment": "bullish",
                    "sentiment_score": 0.8,
                    "confidence": 0.9,
                    "news_count": 5,
                    "bullish_signals": [],
                    "bearish_signals": [],
                    "key_themes": [],
                    "analysis_timestamp": datetime.now().isoformat()
                },
                "market_insights": ["Insight 1"],
                "ai_recommendation": "BUY",
                "confidence_score": 0.8,
                "risk_assessment": "Medium",
                "key_factors": ["Factor 1"],
                "generated_at": datetime.now().isoformat(),
                "valid_until": (datetime.now() + timedelta(hours=2)).isoformat()
            }
            mock_cache.get.return_value = str(cached_analysis).replace("'", '"')
            
            analysis = await ai_service.generate_comprehensive_analysis("BTC")
            
            assert isinstance(analysis, AIAnalysisResult)
            mock_cache.get.assert_called_once()

    def test_prepare_news_for_analysis(self, ai_service, sample_news_articles):
        """Test news preparation for AI analysis."""
        news_content = ai_service._prepare_news_for_analysis(sample_news_articles)
        
        assert isinstance(news_content, str)
        assert "Bitcoin Reaches New High" in news_content
        assert "Ethereum Network Upgrade" in news_content
        assert "Article 1:" in news_content

    def test_serialize_deserialize_news_article(self, ai_service):
        """Test news article serialization and deserialization."""
        article = NewsArticle(
            title="Test Article",
            content="Test content",
            url="https://test.com",
            published_at=datetime.now(),
            source="Test Source",
            relevance_score=0.8
        )
        
        # Serialize
        serialized = ai_service._serialize_news_article(article)
        assert isinstance(serialized, dict)
        assert serialized["title"] == "Test Article"
        
        # Deserialize
        deserialized = ai_service._deserialize_news_article(serialized)
        assert isinstance(deserialized, NewsArticle)
        assert deserialized.title == "Test Article"

    def test_serialize_deserialize_sentiment_analysis(self, ai_service, sample_sentiment_analysis):
        """Test sentiment analysis serialization and deserialization."""
        # Serialize
        serialized = ai_service._serialize_sentiment_analysis(sample_sentiment_analysis)
        assert isinstance(serialized, dict)
        assert serialized["symbol"] == "BTC"
        
        # Deserialize
        deserialized = ai_service._deserialize_sentiment_analysis(serialized)
        assert isinstance(deserialized, SentimentAnalysis)
        assert deserialized.symbol == "BTC"
        assert deserialized.overall_sentiment == SentimentType.BULLISH


if __name__ == "__main__":
    pytest.main([__file__])

"""
Market Data Service - Core data collection and management
Author: inkbytefo
"""

import ccxt
import asyncio
import json
import logging
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_

from app.core.config import settings
from app.core.database import get_db, cache_manager
from app.models.market_data import MarketData, TickerData, MarketStats
from app.models.market_data import MarketDataResponse, TickerResponse

logger = logging.getLogger("market_data")


class MarketDataService:
    """Market data collection and management service"""
    
    def __init__(self):
        self.exchanges = {}
        self.supported_timeframes = ['1m', '5m', '15m', '1h', '4h', '1d']
        self.default_symbols = settings.DEFAULT_SYMBOLS
        self.cache_ttl = {
            '1m': 60,      # 1 minute
            '5m': 300,     # 5 minutes
            '15m': 900,    # 15 minutes
            '1h': 3600,    # 1 hour
            '4h': 14400,   # 4 hours
            '1d': 86400    # 1 day
        }
        self._initialize_exchanges()
    
    def _initialize_exchanges(self):
        """Initialize exchange connections"""
        try:
            # Binance exchange
            self.exchanges['binance'] = ccxt.binance({
                'apiKey': settings.BINANCE_API_KEY,
                'secret': settings.BINANCE_SECRET_KEY,
                'sandbox': settings.TRADING_MODE == 'testnet',
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'spot'  # spot, future, delivery
                }
            })
            
            # Skip CoinGecko for now - focus on Binance
            # self.exchanges['coingecko'] = ccxt.coingecko({
            #     'enableRateLimit': True
            # })
            
            logger.info("Exchanges initialized successfully")

        except Exception as e:
            logger.error(f"Exchange initialization failed: {e}")
    
    async def get_current_price(self, symbol: str, exchange: str = 'binance') -> Optional[float]:
        """Get current price for a symbol"""
        try:
            # Check cache first (if available)
            cache_key = f"price:{exchange}:{symbol}"
            cached_price = None
            if cache_manager:
                try:
                    cached_price = await cache_manager.get(cache_key)
                except Exception:
                    pass

            if cached_price:
                return float(cached_price)
            
            # Fetch from exchange
            if exchange not in self.exchanges:
                logger.error(f"Exchange {exchange} not supported")
                return None
            
            ticker = self.exchanges[exchange].fetch_ticker(symbol)
            current_price = ticker['last']
            
            # Cache for 30 seconds (if available)
            if cache_manager:
                try:
                    await cache_manager.set(cache_key, str(current_price), 30)
                except Exception:
                    pass
            
            logger.info(f"Current price for {symbol}: ${current_price}")
            return current_price
            
        except Exception as e:
            logger.error(f"Error fetching current price for {symbol}: {e}")
            return None
    
    async def get_ohlcv_data(
        self, 
        symbol: str, 
        timeframe: str = '1h', 
        limit: int = 100,
        exchange: str = 'binance'
    ) -> List[Dict]:
        """Get OHLCV data for a symbol"""
        try:
            # Validate timeframe
            if timeframe not in self.supported_timeframes:
                raise ValueError(f"Unsupported timeframe: {timeframe}")
            
            # Check cache (if available)
            cache_key = f"ohlcv:{exchange}:{symbol}:{timeframe}:{limit}"
            cached_data = None
            if cache_manager:
                try:
                    cached_data = await cache_manager.get(cache_key)
                except Exception:
                    pass

            if cached_data:
                return json.loads(cached_data)
            
            # Fetch from exchange
            if exchange not in self.exchanges:
                raise ValueError(f"Exchange {exchange} not supported")
            
            ohlcv = self.exchanges[exchange].fetch_ohlcv(
                symbol, timeframe, limit=limit
            )
            
            # Format data
            formatted_data = []
            for candle in ohlcv:
                formatted_data.append({
                    'timestamp': datetime.fromtimestamp(candle[0] / 1000),
                    'open': candle[1],
                    'high': candle[2],
                    'low': candle[3],
                    'close': candle[4],
                    'volume': candle[5]
                })
            
            # Cache data (if available)
            if cache_manager:
                try:
                    cache_ttl = self.cache_ttl.get(timeframe, 300)
                    await cache_manager.set(
                        cache_key,
                        json.dumps(formatted_data, default=str),
                        cache_ttl
                    )
                except Exception:
                    pass
            
            logger.info(f"Fetched {len(formatted_data)} OHLCV records for {symbol}")
            return formatted_data
            
        except Exception as e:
            logger.error(f"Error fetching OHLCV data for {symbol}: {e}")
            return []
    
    async def get_ticker_data(self, symbol: str, exchange: str = 'binance') -> Optional[Dict]:
        """Get detailed ticker data"""
        try:
            # Check cache (if available)
            cache_key = f"ticker:{exchange}:{symbol}"
            cached_ticker = None
            if cache_manager:
                try:
                    cached_ticker = await cache_manager.get(cache_key)
                except Exception:
                    pass

            if cached_ticker:
                return json.loads(cached_ticker)
            
            # Fetch from exchange
            if exchange not in self.exchanges:
                raise ValueError(f"Exchange {exchange} not supported")
            
            ticker = self.exchanges[exchange].fetch_ticker(symbol)
            
            # Format ticker data
            ticker_data = {
                'symbol': symbol,
                'exchange': exchange,
                'last_price': ticker['last'],
                'bid_price': ticker['bid'],
                'ask_price': ticker['ask'],
                'price_change_24h': ticker['change'],
                'price_change_percent_24h': ticker['percentage'],
                'high_24h': ticker['high'],
                'low_24h': ticker['low'],
                'volume_24h': ticker['baseVolume'],
                'quote_volume_24h': ticker['quoteVolume'],
                'timestamp': datetime.now()
            }
            
            # Cache for 1 minute (if available)
            if cache_manager:
                try:
                    await cache_manager.set(
                        cache_key,
                        json.dumps(ticker_data, default=str),
                        60
                    )
                except Exception:
                    pass
            
            return ticker_data
            
        except Exception as e:
            logger.error(f"Error fetching ticker data for {symbol}: {e}")
            return None
    
    async def store_market_data(self, db: Session, data: List[Dict], symbol: str, timeframe: str):
        """Store market data in database"""
        try:
            for record in data:
                # Check if record already exists
                existing = db.query(MarketData).filter(
                    and_(
                        MarketData.symbol == symbol,
                        MarketData.timeframe == timeframe,
                        MarketData.timestamp == record['timestamp']
                    )
                ).first()
                
                if not existing:
                    market_data = MarketData(
                        symbol=symbol,
                        timeframe=timeframe,
                        timestamp=record['timestamp'],
                        open_price=record['open'],
                        high_price=record['high'],
                        low_price=record['low'],
                        close_price=record['close'],
                        volume=record['volume']
                    )
                    db.add(market_data)
            
            db.commit()
            logger.info(f"Stored {len(data)} market data records for {symbol}")
            
        except Exception as e:
            logger.error(f"Error storing market data: {e}")
            db.rollback()
    
    async def get_supported_symbols(self, exchange: str = 'binance') -> List[str]:
        """Get list of supported trading symbols"""
        try:
            if exchange not in self.exchanges:
                return self.default_symbols
            
            # Check cache (if available)
            cache_key = f"symbols:{exchange}"
            cached_symbols = None
            if cache_manager:
                try:
                    cached_symbols = await cache_manager.get(cache_key)
                except Exception:
                    pass

            if cached_symbols:
                return json.loads(cached_symbols)
            
            # Fetch from exchange
            markets = self.exchanges[exchange].load_markets()
            symbols = [symbol for symbol in markets.keys() if '/USDT' in symbol]
            
            # Cache for 1 hour (if available)
            if cache_manager:
                try:
                    await cache_manager.set(cache_key, json.dumps(symbols), 3600)
                except Exception:
                    pass
            
            return symbols[:50]  # Return top 50 symbols
            
        except Exception as e:
            logger.error(f"Error fetching supported symbols: {e}")
            return self.default_symbols

    async def get_historical_data(
        self,
        symbol: str,
        timeframe: str,
        start_date: datetime,
        end_date: datetime,
        exchange: str = "binance"
    ) -> Optional[pd.DataFrame]:
        """Get historical OHLCV data for backtesting"""
        try:
            if exchange not in self.exchanges:
                logger.error(f"Exchange {exchange} not supported")
                return None

            exchange_obj = self.exchanges[exchange]

            # Convert dates to timestamps
            since = int(start_date.timestamp() * 1000)
            until = int(end_date.timestamp() * 1000)

            # Fetch historical data
            all_data = []
            current_since = since

            while current_since < until:
                try:
                    # Fetch data in chunks
                    ohlcv = await exchange_obj.fetch_ohlcv(
                        symbol, timeframe, since=current_since, limit=1000
                    )

                    if not ohlcv:
                        break

                    all_data.extend(ohlcv)

                    # Update since to last timestamp + 1
                    current_since = ohlcv[-1][0] + 1

                    # Rate limiting
                    await asyncio.sleep(0.1)

                except Exception as e:
                    logger.error(f"Error fetching historical data chunk: {e}")
                    break

            if not all_data:
                logger.warning(f"No historical data found for {symbol}")
                return None

            # Convert to DataFrame
            df = pd.DataFrame(all_data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)

            # Filter by date range
            df = df[(df.index >= start_date) & (df.index <= end_date)]

            logger.info(f"Retrieved {len(df)} historical data points for {symbol}")
            return df

        except Exception as e:
            logger.error(f"Error fetching historical data for {symbol}: {e}")
            return None


# Global service instance
market_data_service = MarketDataService()

"""
Configuration settings for AI Crypto Trading System
Author: inkbytefo
"""

from pydantic_settings import BaseSettings
from typing import List, Optional
import os
from pathlib import Path


class Settings(BaseSettings):
    """Application settings"""
    
    # Basic Configuration
    PROJECT_NAME: str = "AI Crypto Trading System"
    VERSION: str = "1.0.0"
    DEBUG: bool = True
    ENVIRONMENT: str = "development"
    
    # Security
    SECRET_KEY: str = "your-secret-key-change-this-in-production"
    ENCRYPTION_KEY: Optional[str] = None  # For encrypting sensitive data like API keys
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # Database
    DATABASE_URL: str = "postgresql://username:password@localhost:5432/crypto_trading"
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # API Keys
    BINANCE_API_KEY: Optional[str] = None
    BINANCE_SECRET_KEY: Optional[str] = None
    COINGECKO_API_KEY: Optional[str] = None
    OPENAI_API_KEY: Optional[str] = None
    NEWSAPI_KEY: Optional[str] = None

    # AI Configuration
    OPENAI_BASE_URL: str = "https://api.openai.com/v1"  # Can be changed to local LLM endpoint
    OPENAI_MODEL: str = "gpt-3.5-turbo"
    AI_ANALYSIS_CACHE_TTL: int = 3600  # 1 hour
    NEWS_CACHE_TTL: int = 1800  # 30 minutes
    SENTIMENT_CACHE_TTL: int = 1800  # 30 minutes
    
    # Telegram Bot
    TELEGRAM_BOT_TOKEN: Optional[str] = None
    TELEGRAM_CHAT_ID: Optional[str] = None
    TELEGRAM_AUTHORIZED_USERS: Optional[str] = None  # Comma-separated user IDs
    TELEGRAM_ADMIN_USERS: Optional[str] = None  # Comma-separated admin user IDs
    TELEGRAM_WEBHOOK_URL: Optional[str] = None
    TELEGRAM_WEBHOOK_SECRET: Optional[str] = None
    
    # Trading Configuration
    TRADING_MODE: str = "testnet"  # testnet or live
    ENABLE_TRADING: bool = False
    MAX_DAILY_TRADES: int = 10
    MAX_POSITION_SIZE: float = 0.1  # 10% of portfolio
    DEFAULT_STOP_LOSS: float = 0.05  # 5%
    DEFAULT_TAKE_PROFIT: float = 0.15  # 15%
    
    # Risk Management
    RISK_LEVEL: str = "low"  # low, medium, high
    MAX_DRAWDOWN: float = 0.20  # 20%
    
    # CORS
    ALLOWED_HOSTS: List[str] = ["http://localhost:3000", "http://127.0.0.1:3000"]
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/trading.log"
    
    # Rate Limiting
    RATE_LIMIT_PER_MINUTE: int = 60
    
    # Data Sources
    DEFAULT_SYMBOLS: List[str] = ["BTC/USDT", "ETH/USDT", "BNB/USDT", "ADA/USDT", "SOL/USDT"]

    # Market Data Configuration
    MARKET_DATA_CACHE_TTL: int = 300  # 5 minutes
    PRICE_CACHE_TTL: int = 30  # 30 seconds
    TICKER_CACHE_TTL: int = 60  # 1 minute
    MAX_OHLCV_LIMIT: int = 1000
    DEFAULT_TIMEFRAME: str = "1h"
    SUPPORTED_TIMEFRAMES: List[str] = ["1m", "5m", "15m", "1h", "4h", "1d"]

    # Exchange Configuration
    DEFAULT_EXCHANGE: str = "binance"
    SUPPORTED_EXCHANGES: List[str] = ["binance", "coingecko"]
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Create settings instance
settings = Settings()


def get_project_root() -> Path:
    """Get project root directory"""
    return Path(__file__).parent.parent.parent


def ensure_log_directory():
    """Ensure log directory exists"""
    log_dir = get_project_root() / "logs"
    log_dir.mkdir(exist_ok=True)
    return log_dir

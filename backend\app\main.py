"""
AI Crypto Trading System - Main Application
Author: inkbytefo
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import logging
from contextlib import asynccontextmanager

from app.core.config import settings
from app.core.logging import setup_logging
from app.core.logging_config import setup_logging as setup_comprehensive_logging
from app.core.shutdown_manager import shutdown_manager
from app.core.log_monitoring import log_monitor
from app.api.routes import api_router


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    setup_logging()
    setup_comprehensive_logging()  # Setup comprehensive logging system

    # Start log monitoring
    await log_monitor.start_monitoring()

    logging.info("AI Crypto Trading System starting up with comprehensive logging and monitoring...")

    # Initialize services here
    from app.core.database import init_database, check_database_connection, check_redis_connection

    # Initialize database
    db_success = await init_database()
    if db_success:
        logging.info("Database initialized successfully")
    else:
        logging.error("Database initialization failed")

    # Check connections
    await check_database_connection()
    await check_redis_connection()

    # Initialize market data service
    try:
        from app.services.market_data_service import market_data_service
        logging.info("Market Data Service initialized")
    except Exception as e:
        logging.error(f"Market Data Service initialization failed: {e}")

    # Scheduler service removed - using simple Telegram bot interface
    logging.info("Simplified system - no scheduler needed")

    logging.info("System startup completed")

    yield

    # Shutdown
    logging.info("🛑 AI Crypto Trading System shutting down...")

    # Initiate graceful shutdown
    await shutdown_manager.initiate_shutdown(
        reason=shutdown_manager.ShutdownReason.USER_REQUEST,
        details="Application shutdown requested"
    )

    # Stop log monitoring
    await log_monitor.stop_monitoring()

    logging.info("👋 Graceful shutdown completed")


# Create FastAPI application
app = FastAPI(
    title="AI Crypto Trading System",
    description="AI-powered cryptocurrency trading system with technical analysis and risk management",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(api_router, prefix="/api/v1")


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "AI Crypto Trading System API",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "trading_enabled": settings.ENABLE_TRADING,
        "environment": settings.ENVIRONMENT
    }


@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Global HTTP exception handler"""
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail, "status_code": exc.status_code}
    )


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )

"""Initial migration

Revision ID: e91c68e5a846
Revises: 
Create Date: 2025-07-26 15:24:45.539661

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e91c68e5a846'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('ai_models',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('model_uuid', sa.String(length=36), nullable=True),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('model_type', sa.String(length=50), nullable=False),
    sa.Column('version', sa.String(length=20), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('parameters', sa.JSON(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_production', sa.Boolean(), nullable=True),
    sa.Column('accuracy', sa.Float(), nullable=True),
    sa.Column('precision', sa.Float(), nullable=True),
    sa.Column('recall', sa.Float(), nullable=True),
    sa.Column('f1_score', sa.Float(), nullable=True),
    sa.Column('total_predictions', sa.Integer(), nullable=True),
    sa.Column('correct_predictions', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('last_training', sa.DateTime(), nullable=True),
    sa.Column('last_evaluation', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ai_models_id'), 'ai_models', ['id'], unique=False)
    op.create_index(op.f('ix_ai_models_model_uuid'), 'ai_models', ['model_uuid'], unique=True)
    op.create_table('analysis_sessions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('session_uuid', sa.String(length=36), nullable=True),
    sa.Column('symbols_analyzed', sa.Text(), nullable=True),
    sa.Column('timeframes', sa.Text(), nullable=True),
    sa.Column('exchange', sa.String(length=20), nullable=False),
    sa.Column('total_symbols', sa.Integer(), nullable=True),
    sa.Column('successful_analysis', sa.Integer(), nullable=True),
    sa.Column('signals_generated', sa.Integer(), nullable=True),
    sa.Column('patterns_detected', sa.Integer(), nullable=True),
    sa.Column('execution_time_ms', sa.Integer(), nullable=True),
    sa.Column('cache_hit_rate', sa.Float(), nullable=True),
    sa.Column('started_at', sa.DateTime(), nullable=True),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_started_at', 'analysis_sessions', ['started_at'], unique=False)
    op.create_index(op.f('ix_analysis_sessions_id'), 'analysis_sessions', ['id'], unique=False)
    op.create_index(op.f('ix_analysis_sessions_session_uuid'), 'analysis_sessions', ['session_uuid'], unique=True)
    op.create_table('exchanges',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False),
    sa.Column('display_name', sa.String(length=100), nullable=False),
    sa.Column('api_url', sa.String(length=200), nullable=True),
    sa.Column('testnet_url', sa.String(length=200), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_trading_enabled', sa.Boolean(), nullable=True),
    sa.Column('supports_websocket', sa.Boolean(), nullable=True),
    sa.Column('supports_margin', sa.Boolean(), nullable=True),
    sa.Column('min_trade_amount', sa.Float(), nullable=True),
    sa.Column('max_trade_amount', sa.Float(), nullable=True),
    sa.Column('trading_fee', sa.Float(), nullable=True),
    sa.Column('rate_limit_per_minute', sa.Integer(), nullable=True),
    sa.Column('rate_limit_per_second', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_exchanges_id'), 'exchanges', ['id'], unique=False)
    op.create_index(op.f('ix_exchanges_name'), 'exchanges', ['name'], unique=True)
    op.create_table('market_data',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('symbol', sa.String(length=20), nullable=False),
    sa.Column('exchange', sa.String(length=20), nullable=False),
    sa.Column('timeframe', sa.String(length=10), nullable=False),
    sa.Column('timestamp', sa.DateTime(), nullable=False),
    sa.Column('open_price', sa.Float(), nullable=False),
    sa.Column('high_price', sa.Float(), nullable=False),
    sa.Column('low_price', sa.Float(), nullable=False),
    sa.Column('close_price', sa.Float(), nullable=False),
    sa.Column('volume', sa.Float(), nullable=False),
    sa.Column('quote_volume', sa.Float(), nullable=True),
    sa.Column('trades_count', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_exchange_symbol_timestamp', 'market_data', ['exchange', 'symbol', 'timestamp'], unique=False)
    op.create_index('idx_symbol_timeframe_timestamp', 'market_data', ['symbol', 'timeframe', 'timestamp'], unique=False)
    op.create_index(op.f('ix_market_data_id'), 'market_data', ['id'], unique=False)
    op.create_index(op.f('ix_market_data_symbol'), 'market_data', ['symbol'], unique=False)
    op.create_index(op.f('ix_market_data_timeframe'), 'market_data', ['timeframe'], unique=False)
    op.create_index(op.f('ix_market_data_timestamp'), 'market_data', ['timestamp'], unique=False)
    op.create_table('market_stats',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('symbol', sa.String(length=20), nullable=False),
    sa.Column('exchange', sa.String(length=20), nullable=False),
    sa.Column('market_cap', sa.Float(), nullable=True),
    sa.Column('circulating_supply', sa.Float(), nullable=True),
    sa.Column('total_supply', sa.Float(), nullable=True),
    sa.Column('max_supply', sa.Float(), nullable=True),
    sa.Column('market_cap_rank', sa.Integer(), nullable=True),
    sa.Column('volume_rank', sa.Integer(), nullable=True),
    sa.Column('ath_price', sa.Float(), nullable=True),
    sa.Column('ath_date', sa.DateTime(), nullable=True),
    sa.Column('atl_price', sa.Float(), nullable=True),
    sa.Column('atl_date', sa.DateTime(), nullable=True),
    sa.Column('timestamp', sa.DateTime(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_market_stats_id'), 'market_stats', ['id'], unique=False)
    op.create_index(op.f('ix_market_stats_symbol'), 'market_stats', ['symbol'], unique=False)
    op.create_index(op.f('ix_market_stats_timestamp'), 'market_stats', ['timestamp'], unique=False)
    op.create_table('system_config',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('config_key', sa.String(length=100), nullable=False),
    sa.Column('config_value', sa.Text(), nullable=False),
    sa.Column('config_type', sa.String(length=20), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('category', sa.String(length=50), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_user_configurable', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('updated_by', sa.String(length=50), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_system_config_category'), 'system_config', ['category'], unique=False)
    op.create_index(op.f('ix_system_config_config_key'), 'system_config', ['config_key'], unique=True)
    op.create_index(op.f('ix_system_config_id'), 'system_config', ['id'], unique=False)
    op.create_table('system_logs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('log_level', sa.String(length=20), nullable=False),
    sa.Column('module', sa.String(length=50), nullable=False),
    sa.Column('message', sa.Text(), nullable=False),
    sa.Column('details', sa.JSON(), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('trade_id', sa.Integer(), nullable=True),
    sa.Column('symbol', sa.String(length=20), nullable=True),
    sa.Column('exchange', sa.String(length=20), nullable=True),
    sa.Column('error_code', sa.String(length=50), nullable=True),
    sa.Column('stack_trace', sa.Text(), nullable=True),
    sa.Column('timestamp', sa.DateTime(), nullable=True),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('user_agent', sa.Text(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_system_logs_id'), 'system_logs', ['id'], unique=False)
    op.create_index(op.f('ix_system_logs_log_level'), 'system_logs', ['log_level'], unique=False)
    op.create_index(op.f('ix_system_logs_module'), 'system_logs', ['module'], unique=False)
    op.create_index(op.f('ix_system_logs_timestamp'), 'system_logs', ['timestamp'], unique=False)
    op.create_table('system_metrics',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('metric_name', sa.String(length=100), nullable=False),
    sa.Column('metric_value', sa.Float(), nullable=False),
    sa.Column('metric_unit', sa.String(length=20), nullable=True),
    sa.Column('component', sa.String(length=50), nullable=False),
    sa.Column('environment', sa.String(length=20), nullable=True),
    sa.Column('timestamp', sa.DateTime(), nullable=True),
    sa.Column('collected_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_system_metrics_id'), 'system_metrics', ['id'], unique=False)
    op.create_index(op.f('ix_system_metrics_metric_name'), 'system_metrics', ['metric_name'], unique=False)
    op.create_index(op.f('ix_system_metrics_timestamp'), 'system_metrics', ['timestamp'], unique=False)
    op.create_table('technical_indicators',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('symbol', sa.String(length=20), nullable=False),
    sa.Column('exchange', sa.String(length=20), nullable=False),
    sa.Column('timeframe', sa.String(length=10), nullable=False),
    sa.Column('sma_20', sa.Float(), nullable=True),
    sa.Column('sma_50', sa.Float(), nullable=True),
    sa.Column('ema_12', sa.Float(), nullable=True),
    sa.Column('ema_26', sa.Float(), nullable=True),
    sa.Column('rsi', sa.Float(), nullable=True),
    sa.Column('macd', sa.Float(), nullable=True),
    sa.Column('macd_signal', sa.Float(), nullable=True),
    sa.Column('macd_histogram', sa.Float(), nullable=True),
    sa.Column('bb_upper', sa.Float(), nullable=True),
    sa.Column('bb_middle', sa.Float(), nullable=True),
    sa.Column('bb_lower', sa.Float(), nullable=True),
    sa.Column('bb_width', sa.Float(), nullable=True),
    sa.Column('volume_sma', sa.Float(), nullable=True),
    sa.Column('volume_spike', sa.Boolean(), nullable=True),
    sa.Column('support_level', sa.Float(), nullable=True),
    sa.Column('resistance_level', sa.Float(), nullable=True),
    sa.Column('trend_direction', sa.String(length=20), nullable=True),
    sa.Column('trend_strength', sa.Float(), nullable=True),
    sa.Column('timestamp', sa.DateTime(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_symbol_exchange_timeframe_timestamp', 'technical_indicators', ['symbol', 'exchange', 'timeframe', 'timestamp'], unique=False)
    op.create_index(op.f('ix_technical_indicators_id'), 'technical_indicators', ['id'], unique=False)
    op.create_index(op.f('ix_technical_indicators_symbol'), 'technical_indicators', ['symbol'], unique=False)
    op.create_index(op.f('ix_technical_indicators_timeframe'), 'technical_indicators', ['timeframe'], unique=False)
    op.create_index(op.f('ix_technical_indicators_timestamp'), 'technical_indicators', ['timestamp'], unique=False)
    op.create_table('ticker_data',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('symbol', sa.String(length=20), nullable=False),
    sa.Column('exchange', sa.String(length=20), nullable=False),
    sa.Column('last_price', sa.Float(), nullable=False),
    sa.Column('bid_price', sa.Float(), nullable=True),
    sa.Column('ask_price', sa.Float(), nullable=True),
    sa.Column('price_change_24h', sa.Float(), nullable=True),
    sa.Column('price_change_percent_24h', sa.Float(), nullable=True),
    sa.Column('high_24h', sa.Float(), nullable=True),
    sa.Column('low_24h', sa.Float(), nullable=True),
    sa.Column('volume_24h', sa.Float(), nullable=True),
    sa.Column('quote_volume_24h', sa.Float(), nullable=True),
    sa.Column('timestamp', sa.DateTime(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_symbol_exchange_timestamp', 'ticker_data', ['symbol', 'exchange', 'timestamp'], unique=False)
    op.create_index(op.f('ix_ticker_data_id'), 'ticker_data', ['id'], unique=False)
    op.create_index(op.f('ix_ticker_data_symbol'), 'ticker_data', ['symbol'], unique=False)
    op.create_index(op.f('ix_ticker_data_timestamp'), 'ticker_data', ['timestamp'], unique=False)
    op.create_table('trading_pairs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('symbol', sa.String(length=20), nullable=False),
    sa.Column('base_asset', sa.String(length=10), nullable=False),
    sa.Column('quote_asset', sa.String(length=10), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_trading_enabled', sa.Boolean(), nullable=True),
    sa.Column('min_quantity', sa.Float(), nullable=True),
    sa.Column('max_quantity', sa.Float(), nullable=True),
    sa.Column('quantity_precision', sa.Integer(), nullable=True),
    sa.Column('price_precision', sa.Integer(), nullable=True),
    sa.Column('current_price', sa.Float(), nullable=True),
    sa.Column('volume_24h', sa.Float(), nullable=True),
    sa.Column('price_change_24h', sa.Float(), nullable=True),
    sa.Column('market_cap', sa.Float(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('last_price_update', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_trading_pairs_id'), 'trading_pairs', ['id'], unique=False)
    op.create_index(op.f('ix_trading_pairs_symbol'), 'trading_pairs', ['symbol'], unique=True)
    op.create_table('trading_signals',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('symbol', sa.String(length=20), nullable=False),
    sa.Column('timeframe', sa.String(length=10), nullable=False),
    sa.Column('exchange', sa.String(length=20), nullable=False),
    sa.Column('signal_type', sa.String(length=20), nullable=False),
    sa.Column('signal_strength', sa.String(length=20), nullable=False),
    sa.Column('overall_score', sa.Float(), nullable=False),
    sa.Column('confidence', sa.Float(), nullable=False),
    sa.Column('reasoning', sa.Text(), nullable=True),
    sa.Column('entry_price', sa.Float(), nullable=False),
    sa.Column('entry_confidence', sa.Float(), nullable=False),
    sa.Column('stop_loss', sa.Float(), nullable=True),
    sa.Column('take_profit', sa.Float(), nullable=True),
    sa.Column('risk_reward_ratio', sa.Float(), nullable=True),
    sa.Column('position_size_percentage', sa.Float(), nullable=False),
    sa.Column('position_size_usd', sa.Float(), nullable=True),
    sa.Column('max_loss_percentage', sa.Float(), nullable=False),
    sa.Column('risk_level', sa.String(length=20), nullable=False),
    sa.Column('leverage', sa.Float(), nullable=True),
    sa.Column('max_drawdown_risk', sa.Float(), nullable=True),
    sa.Column('volatility_risk', sa.Float(), nullable=True),
    sa.Column('market_correlation_risk', sa.Float(), nullable=True),
    sa.Column('generated_at', sa.DateTime(), nullable=False),
    sa.Column('valid_until', sa.DateTime(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_trading_signals_id'), 'trading_signals', ['id'], unique=False)
    op.create_index(op.f('ix_trading_signals_symbol'), 'trading_signals', ['symbol'], unique=False)
    op.create_table('users',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_uuid', sa.String(length=36), nullable=True),
    sa.Column('username', sa.String(length=50), nullable=False),
    sa.Column('email', sa.String(length=100), nullable=False),
    sa.Column('full_name', sa.String(length=100), nullable=True),
    sa.Column('hashed_password', sa.String(length=255), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_verified', sa.Boolean(), nullable=True),
    sa.Column('telegram_user_id', sa.String(length=50), nullable=True),
    sa.Column('telegram_username', sa.String(length=50), nullable=True),
    sa.Column('telegram_chat_id', sa.String(length=50), nullable=True),
    sa.Column('trading_enabled', sa.Boolean(), nullable=True),
    sa.Column('risk_level', sa.String(length=20), nullable=True),
    sa.Column('max_daily_trades', sa.Integer(), nullable=True),
    sa.Column('max_position_size', sa.Float(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('last_login', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_telegram_user_id'), 'users', ['telegram_user_id'], unique=True)
    op.create_index(op.f('ix_users_user_uuid'), 'users', ['user_uuid'], unique=True)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.create_table('exit_points',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('trading_signal_id', sa.Integer(), nullable=False),
    sa.Column('exit_price', sa.Float(), nullable=False),
    sa.Column('exit_confidence', sa.Float(), nullable=False),
    sa.Column('exit_reasoning', sa.Text(), nullable=True),
    sa.Column('risk_reward_ratio', sa.Float(), nullable=True),
    sa.Column('position_percentage', sa.Float(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['trading_signal_id'], ['trading_signals.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_exit_points_id'), 'exit_points', ['id'], unique=False)
    op.create_table('portfolios',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('exchange', sa.String(length=20), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('total_value_usd', sa.Float(), nullable=True),
    sa.Column('total_invested', sa.Float(), nullable=True),
    sa.Column('total_profit_loss', sa.Float(), nullable=True),
    sa.Column('profit_loss_percentage', sa.Float(), nullable=True),
    sa.Column('max_drawdown', sa.Float(), nullable=True),
    sa.Column('sharpe_ratio', sa.Float(), nullable=True),
    sa.Column('win_rate', sa.Float(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('last_sync', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_portfolios_id'), 'portfolios', ['id'], unique=False)
    op.create_table('signal_factors',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('trading_signal_id', sa.Integer(), nullable=False),
    sa.Column('factor_category', sa.String(length=20), nullable=False),
    sa.Column('factor_name', sa.String(length=50), nullable=False),
    sa.Column('factor_value', sa.Float(), nullable=False),
    sa.Column('factor_weight', sa.Float(), nullable=False),
    sa.Column('factor_confidence', sa.Float(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['trading_signal_id'], ['trading_signals.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_signal_factors_id'), 'signal_factors', ['id'], unique=False)
    op.create_table('signal_performance',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('trading_signal_id', sa.Integer(), nullable=False),
    sa.Column('actual_entry_price', sa.Float(), nullable=True),
    sa.Column('actual_exit_price', sa.Float(), nullable=True),
    sa.Column('actual_profit_loss', sa.Float(), nullable=True),
    sa.Column('actual_profit_loss_percentage', sa.Float(), nullable=True),
    sa.Column('executed_at', sa.DateTime(), nullable=True),
    sa.Column('closed_at', sa.DateTime(), nullable=True),
    sa.Column('execution_status', sa.String(length=20), nullable=True),
    sa.Column('signal_accuracy', sa.Float(), nullable=True),
    sa.Column('timing_accuracy', sa.Float(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['trading_signal_id'], ['trading_signals.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_signal_performance_id'), 'signal_performance', ['id'], unique=False)
    op.create_table('technical_signals',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('signal_uuid', sa.String(length=36), nullable=True),
    sa.Column('symbol', sa.String(length=20), nullable=False),
    sa.Column('exchange', sa.String(length=20), nullable=False),
    sa.Column('timeframe', sa.String(length=10), nullable=False),
    sa.Column('signal_type', sa.String(length=20), nullable=False),
    sa.Column('strength', sa.Float(), nullable=False),
    sa.Column('confidence', sa.Float(), nullable=False),
    sa.Column('entry_price', sa.Float(), nullable=True),
    sa.Column('target_price', sa.Float(), nullable=True),
    sa.Column('stop_loss_price', sa.Float(), nullable=True),
    sa.Column('reasoning', sa.Text(), nullable=True),
    sa.Column('rsi_value', sa.Float(), nullable=True),
    sa.Column('macd_value', sa.Float(), nullable=True),
    sa.Column('trend_direction', sa.String(length=20), nullable=True),
    sa.Column('volume_spike', sa.Boolean(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_executed', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('expires_at', sa.DateTime(), nullable=True),
    sa.Column('executed_at', sa.DateTime(), nullable=True),
    sa.Column('indicator_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['indicator_id'], ['technical_indicators.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_created_at_active', 'technical_signals', ['created_at', 'is_active'], unique=False)
    op.create_index('idx_symbol_signal_type_active', 'technical_signals', ['symbol', 'signal_type', 'is_active'], unique=False)
    op.create_index(op.f('ix_technical_signals_created_at'), 'technical_signals', ['created_at'], unique=False)
    op.create_index(op.f('ix_technical_signals_id'), 'technical_signals', ['id'], unique=False)
    op.create_index(op.f('ix_technical_signals_signal_uuid'), 'technical_signals', ['signal_uuid'], unique=True)
    op.create_index(op.f('ix_technical_signals_symbol'), 'technical_signals', ['symbol'], unique=False)
    op.create_index(op.f('ix_technical_signals_timeframe'), 'technical_signals', ['timeframe'], unique=False)
    op.create_table('trades',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('trade_uuid', sa.String(length=36), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('symbol', sa.String(length=20), nullable=False),
    sa.Column('exchange', sa.String(length=20), nullable=False),
    sa.Column('trade_type', sa.Enum('BUY', 'SELL', name='tradetype'), nullable=False),
    sa.Column('order_type', sa.Enum('MARKET', 'LIMIT', 'STOP_LOSS', 'TAKE_PROFIT', name='ordertype'), nullable=False),
    sa.Column('quantity', sa.Float(), nullable=False),
    sa.Column('price', sa.Float(), nullable=True),
    sa.Column('executed_price', sa.Float(), nullable=True),
    sa.Column('executed_quantity', sa.Float(), nullable=True),
    sa.Column('exchange_order_id', sa.String(length=100), nullable=True),
    sa.Column('status', sa.Enum('PENDING', 'OPEN', 'CLOSED', 'CANCELLED', 'FAILED', name='tradestatus'), nullable=True),
    sa.Column('total_value', sa.Float(), nullable=True),
    sa.Column('fees', sa.Float(), nullable=True),
    sa.Column('net_value', sa.Float(), nullable=True),
    sa.Column('stop_loss_price', sa.Float(), nullable=True),
    sa.Column('take_profit_price', sa.Float(), nullable=True),
    sa.Column('ai_confidence', sa.Float(), nullable=True),
    sa.Column('ai_reasoning', sa.Text(), nullable=True),
    sa.Column('technical_indicators', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('executed_at', sa.DateTime(), nullable=True),
    sa.Column('closed_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_trades_created_at'), 'trades', ['created_at'], unique=False)
    op.create_index(op.f('ix_trades_exchange_order_id'), 'trades', ['exchange_order_id'], unique=False)
    op.create_index(op.f('ix_trades_id'), 'trades', ['id'], unique=False)
    op.create_index(op.f('ix_trades_status'), 'trades', ['status'], unique=False)
    op.create_index(op.f('ix_trades_symbol'), 'trades', ['symbol'], unique=False)
    op.create_index(op.f('ix_trades_trade_uuid'), 'trades', ['trade_uuid'], unique=True)
    op.create_table('user_api_keys',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('exchange', sa.String(length=20), nullable=False),
    sa.Column('api_key', sa.Text(), nullable=False),
    sa.Column('secret_key', sa.Text(), nullable=False),
    sa.Column('passphrase', sa.Text(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_testnet', sa.Boolean(), nullable=True),
    sa.Column('permissions', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('last_used', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_api_keys_id'), 'user_api_keys', ['id'], unique=False)
    op.create_table('user_notifications',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(length=200), nullable=False),
    sa.Column('message', sa.Text(), nullable=False),
    sa.Column('notification_type', sa.String(length=50), nullable=False),
    sa.Column('priority', sa.String(length=20), nullable=True),
    sa.Column('is_read', sa.Boolean(), nullable=True),
    sa.Column('is_sent', sa.Boolean(), nullable=True),
    sa.Column('sent_via', sa.String(length=50), nullable=True),
    sa.Column('related_symbol', sa.String(length=20), nullable=True),
    sa.Column('related_trade_id', sa.Integer(), nullable=True),
    sa.Column('extra_data', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('read_at', sa.DateTime(), nullable=True),
    sa.Column('sent_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_notifications_id'), 'user_notifications', ['id'], unique=False)
    op.create_table('user_sessions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('session_token', sa.String(length=255), nullable=False),
    sa.Column('refresh_token', sa.String(length=255), nullable=False),
    sa.Column('device_info', sa.Text(), nullable=True),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('user_agent', sa.Text(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('expires_at', sa.DateTime(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('last_activity', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('refresh_token')
    )
    op.create_index(op.f('ix_user_sessions_id'), 'user_sessions', ['id'], unique=False)
    op.create_index(op.f('ix_user_sessions_session_token'), 'user_sessions', ['session_token'], unique=True)
    op.create_table('pattern_detections',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('pattern_uuid', sa.String(length=36), nullable=True),
    sa.Column('symbol', sa.String(length=20), nullable=False),
    sa.Column('exchange', sa.String(length=20), nullable=False),
    sa.Column('timeframe', sa.String(length=10), nullable=False),
    sa.Column('pattern_type', sa.String(length=50), nullable=False),
    sa.Column('pattern_name', sa.String(length=100), nullable=False),
    sa.Column('confidence', sa.Float(), nullable=False),
    sa.Column('breakout_direction', sa.String(length=20), nullable=True),
    sa.Column('target_price', sa.Float(), nullable=True),
    sa.Column('stop_loss', sa.Float(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('pattern_data', sa.Text(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_confirmed', sa.Boolean(), nullable=True),
    sa.Column('detected_at', sa.DateTime(), nullable=True),
    sa.Column('confirmed_at', sa.DateTime(), nullable=True),
    sa.Column('signal_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['signal_id'], ['technical_signals.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_symbol_pattern_type_active', 'pattern_detections', ['symbol', 'pattern_type', 'is_active'], unique=False)
    op.create_index(op.f('ix_pattern_detections_detected_at'), 'pattern_detections', ['detected_at'], unique=False)
    op.create_index(op.f('ix_pattern_detections_id'), 'pattern_detections', ['id'], unique=False)
    op.create_index(op.f('ix_pattern_detections_pattern_uuid'), 'pattern_detections', ['pattern_uuid'], unique=True)
    op.create_index(op.f('ix_pattern_detections_symbol'), 'pattern_detections', ['symbol'], unique=False)
    op.create_index(op.f('ix_pattern_detections_timeframe'), 'pattern_detections', ['timeframe'], unique=False)
    op.create_table('portfolio_holdings',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('portfolio_id', sa.Integer(), nullable=False),
    sa.Column('symbol', sa.String(length=20), nullable=False),
    sa.Column('asset_name', sa.String(length=100), nullable=True),
    sa.Column('quantity', sa.Float(), nullable=False),
    sa.Column('average_buy_price', sa.Float(), nullable=False),
    sa.Column('current_price', sa.Float(), nullable=True),
    sa.Column('total_value_usd', sa.Float(), nullable=True),
    sa.Column('unrealized_pnl', sa.Float(), nullable=True),
    sa.Column('unrealized_pnl_percentage', sa.Float(), nullable=True),
    sa.Column('realized_pnl', sa.Float(), nullable=True),
    sa.Column('first_purchase_date', sa.DateTime(), nullable=True),
    sa.Column('last_update', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['portfolio_id'], ['portfolios.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_portfolio_holdings_id'), 'portfolio_holdings', ['id'], unique=False)
    op.create_index(op.f('ix_portfolio_holdings_symbol'), 'portfolio_holdings', ['symbol'], unique=False)
    op.create_table('trade_signals',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('signal_uuid', sa.String(length=36), nullable=True),
    sa.Column('trade_id', sa.Integer(), nullable=True),
    sa.Column('symbol', sa.String(length=20), nullable=False),
    sa.Column('signal_type', sa.String(length=20), nullable=False),
    sa.Column('strength', sa.Float(), nullable=False),
    sa.Column('confidence', sa.Float(), nullable=False),
    sa.Column('entry_price', sa.Float(), nullable=True),
    sa.Column('target_price', sa.Float(), nullable=True),
    sa.Column('stop_loss_price', sa.Float(), nullable=True),
    sa.Column('rsi_value', sa.Float(), nullable=True),
    sa.Column('macd_value', sa.Float(), nullable=True),
    sa.Column('volume_spike', sa.Boolean(), nullable=True),
    sa.Column('pattern_detected', sa.String(length=50), nullable=True),
    sa.Column('ai_reasoning', sa.Text(), nullable=True),
    sa.Column('market_sentiment', sa.String(length=20), nullable=True),
    sa.Column('news_sentiment', sa.Float(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_executed', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('expires_at', sa.DateTime(), nullable=True),
    sa.Column('executed_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['trade_id'], ['trades.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_trade_signals_created_at'), 'trade_signals', ['created_at'], unique=False)
    op.create_index(op.f('ix_trade_signals_id'), 'trade_signals', ['id'], unique=False)
    op.create_index(op.f('ix_trade_signals_signal_uuid'), 'trade_signals', ['signal_uuid'], unique=True)
    op.create_index(op.f('ix_trade_signals_symbol'), 'trade_signals', ['symbol'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_trade_signals_symbol'), table_name='trade_signals')
    op.drop_index(op.f('ix_trade_signals_signal_uuid'), table_name='trade_signals')
    op.drop_index(op.f('ix_trade_signals_id'), table_name='trade_signals')
    op.drop_index(op.f('ix_trade_signals_created_at'), table_name='trade_signals')
    op.drop_table('trade_signals')
    op.drop_index(op.f('ix_portfolio_holdings_symbol'), table_name='portfolio_holdings')
    op.drop_index(op.f('ix_portfolio_holdings_id'), table_name='portfolio_holdings')
    op.drop_table('portfolio_holdings')
    op.drop_index(op.f('ix_pattern_detections_timeframe'), table_name='pattern_detections')
    op.drop_index(op.f('ix_pattern_detections_symbol'), table_name='pattern_detections')
    op.drop_index(op.f('ix_pattern_detections_pattern_uuid'), table_name='pattern_detections')
    op.drop_index(op.f('ix_pattern_detections_id'), table_name='pattern_detections')
    op.drop_index(op.f('ix_pattern_detections_detected_at'), table_name='pattern_detections')
    op.drop_index('idx_symbol_pattern_type_active', table_name='pattern_detections')
    op.drop_table('pattern_detections')
    op.drop_index(op.f('ix_user_sessions_session_token'), table_name='user_sessions')
    op.drop_index(op.f('ix_user_sessions_id'), table_name='user_sessions')
    op.drop_table('user_sessions')
    op.drop_index(op.f('ix_user_notifications_id'), table_name='user_notifications')
    op.drop_table('user_notifications')
    op.drop_index(op.f('ix_user_api_keys_id'), table_name='user_api_keys')
    op.drop_table('user_api_keys')
    op.drop_index(op.f('ix_trades_trade_uuid'), table_name='trades')
    op.drop_index(op.f('ix_trades_symbol'), table_name='trades')
    op.drop_index(op.f('ix_trades_status'), table_name='trades')
    op.drop_index(op.f('ix_trades_id'), table_name='trades')
    op.drop_index(op.f('ix_trades_exchange_order_id'), table_name='trades')
    op.drop_index(op.f('ix_trades_created_at'), table_name='trades')
    op.drop_table('trades')
    op.drop_index(op.f('ix_technical_signals_timeframe'), table_name='technical_signals')
    op.drop_index(op.f('ix_technical_signals_symbol'), table_name='technical_signals')
    op.drop_index(op.f('ix_technical_signals_signal_uuid'), table_name='technical_signals')
    op.drop_index(op.f('ix_technical_signals_id'), table_name='technical_signals')
    op.drop_index(op.f('ix_technical_signals_created_at'), table_name='technical_signals')
    op.drop_index('idx_symbol_signal_type_active', table_name='technical_signals')
    op.drop_index('idx_created_at_active', table_name='technical_signals')
    op.drop_table('technical_signals')
    op.drop_index(op.f('ix_signal_performance_id'), table_name='signal_performance')
    op.drop_table('signal_performance')
    op.drop_index(op.f('ix_signal_factors_id'), table_name='signal_factors')
    op.drop_table('signal_factors')
    op.drop_index(op.f('ix_portfolios_id'), table_name='portfolios')
    op.drop_table('portfolios')
    op.drop_index(op.f('ix_exit_points_id'), table_name='exit_points')
    op.drop_table('exit_points')
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('ix_users_user_uuid'), table_name='users')
    op.drop_index(op.f('ix_users_telegram_user_id'), table_name='users')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_table('users')
    op.drop_index(op.f('ix_trading_signals_symbol'), table_name='trading_signals')
    op.drop_index(op.f('ix_trading_signals_id'), table_name='trading_signals')
    op.drop_table('trading_signals')
    op.drop_index(op.f('ix_trading_pairs_symbol'), table_name='trading_pairs')
    op.drop_index(op.f('ix_trading_pairs_id'), table_name='trading_pairs')
    op.drop_table('trading_pairs')
    op.drop_index(op.f('ix_ticker_data_timestamp'), table_name='ticker_data')
    op.drop_index(op.f('ix_ticker_data_symbol'), table_name='ticker_data')
    op.drop_index(op.f('ix_ticker_data_id'), table_name='ticker_data')
    op.drop_index('idx_symbol_exchange_timestamp', table_name='ticker_data')
    op.drop_table('ticker_data')
    op.drop_index(op.f('ix_technical_indicators_timestamp'), table_name='technical_indicators')
    op.drop_index(op.f('ix_technical_indicators_timeframe'), table_name='technical_indicators')
    op.drop_index(op.f('ix_technical_indicators_symbol'), table_name='technical_indicators')
    op.drop_index(op.f('ix_technical_indicators_id'), table_name='technical_indicators')
    op.drop_index('idx_symbol_exchange_timeframe_timestamp', table_name='technical_indicators')
    op.drop_table('technical_indicators')
    op.drop_index(op.f('ix_system_metrics_timestamp'), table_name='system_metrics')
    op.drop_index(op.f('ix_system_metrics_metric_name'), table_name='system_metrics')
    op.drop_index(op.f('ix_system_metrics_id'), table_name='system_metrics')
    op.drop_table('system_metrics')
    op.drop_index(op.f('ix_system_logs_timestamp'), table_name='system_logs')
    op.drop_index(op.f('ix_system_logs_module'), table_name='system_logs')
    op.drop_index(op.f('ix_system_logs_log_level'), table_name='system_logs')
    op.drop_index(op.f('ix_system_logs_id'), table_name='system_logs')
    op.drop_table('system_logs')
    op.drop_index(op.f('ix_system_config_id'), table_name='system_config')
    op.drop_index(op.f('ix_system_config_config_key'), table_name='system_config')
    op.drop_index(op.f('ix_system_config_category'), table_name='system_config')
    op.drop_table('system_config')
    op.drop_index(op.f('ix_market_stats_timestamp'), table_name='market_stats')
    op.drop_index(op.f('ix_market_stats_symbol'), table_name='market_stats')
    op.drop_index(op.f('ix_market_stats_id'), table_name='market_stats')
    op.drop_table('market_stats')
    op.drop_index(op.f('ix_market_data_timestamp'), table_name='market_data')
    op.drop_index(op.f('ix_market_data_timeframe'), table_name='market_data')
    op.drop_index(op.f('ix_market_data_symbol'), table_name='market_data')
    op.drop_index(op.f('ix_market_data_id'), table_name='market_data')
    op.drop_index('idx_symbol_timeframe_timestamp', table_name='market_data')
    op.drop_index('idx_exchange_symbol_timestamp', table_name='market_data')
    op.drop_table('market_data')
    op.drop_index(op.f('ix_exchanges_name'), table_name='exchanges')
    op.drop_index(op.f('ix_exchanges_id'), table_name='exchanges')
    op.drop_table('exchanges')
    op.drop_index(op.f('ix_analysis_sessions_session_uuid'), table_name='analysis_sessions')
    op.drop_index(op.f('ix_analysis_sessions_id'), table_name='analysis_sessions')
    op.drop_index('idx_started_at', table_name='analysis_sessions')
    op.drop_table('analysis_sessions')
    op.drop_index(op.f('ix_ai_models_model_uuid'), table_name='ai_models')
    op.drop_index(op.f('ix_ai_models_id'), table_name='ai_models')
    op.drop_table('ai_models')
    # ### end Alembic commands ###

"""
System Configuration Models for AI Crypto Trading System
Author: inkbytefo
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Text, JSON
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
import uuid

from app.core.database import Base


class SystemConfig(Base):
    """System configuration settings"""
    __tablename__ = "system_config"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Configuration Key-Value
    config_key = Column(String(100), unique=True, nullable=False, index=True)
    config_value = Column(Text, nullable=False)
    config_type = Column(String(20), nullable=False)  # string, int, float, bool, json
    description = Column(Text)
    
    # Configuration Category
    category = Column(String(50), nullable=False, index=True)  # trading, risk, ai, system
    is_active = Column(Boolean, default=True)
    is_user_configurable = Column(Boolean, default=True)
    
    # Metadata
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    updated_by = Column(String(50))  # Username who updated


class Exchange(Base):
    """Supported exchanges configuration"""
    __tablename__ = "exchanges"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Exchange Information
    name = Column(String(50), unique=True, nullable=False, index=True)
    display_name = Column(String(100), nullable=False)
    api_url = Column(String(200))
    testnet_url = Column(String(200))
    
    # Exchange Status
    is_active = Column(Boolean, default=True)
    is_trading_enabled = Column(Boolean, default=False)
    supports_websocket = Column(Boolean, default=False)
    supports_margin = Column(Boolean, default=False)
    
    # Trading Configuration
    min_trade_amount = Column(Float, default=10.0)  # Minimum trade amount in USD
    max_trade_amount = Column(Float, default=10000.0)  # Maximum trade amount in USD
    trading_fee = Column(Float, default=0.001)  # 0.1% default fee
    
    # Rate Limiting
    rate_limit_per_minute = Column(Integer, default=60)
    rate_limit_per_second = Column(Integer, default=10)
    
    # Metadata
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())


class TradingPair(Base):
    """Supported trading pairs"""
    __tablename__ = "trading_pairs"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Pair Information
    symbol = Column(String(20), unique=True, nullable=False, index=True)
    base_asset = Column(String(10), nullable=False)  # BTC, ETH, etc.
    quote_asset = Column(String(10), nullable=False)  # USDT, USD, etc.
    
    # Trading Configuration
    is_active = Column(Boolean, default=True)
    is_trading_enabled = Column(Boolean, default=False)
    min_quantity = Column(Float)
    max_quantity = Column(Float)
    quantity_precision = Column(Integer, default=8)
    price_precision = Column(Integer, default=8)
    
    # Market Data
    current_price = Column(Float)
    volume_24h = Column(Float)
    price_change_24h = Column(Float)
    market_cap = Column(Float)
    
    # Metadata
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    last_price_update = Column(DateTime)


class AIModel(Base):
    """AI model configurations and performance tracking"""
    __tablename__ = "ai_models"
    
    id = Column(Integer, primary_key=True, index=True)
    model_uuid = Column(String(36), unique=True, index=True, default=lambda: str(uuid.uuid4()))
    
    # Model Information
    name = Column(String(100), nullable=False)
    model_type = Column(String(50), nullable=False)  # technical_analysis, sentiment, price_prediction
    version = Column(String(20), nullable=False)
    description = Column(Text)
    
    # Model Configuration
    parameters = Column(JSON)  # Model parameters as JSON
    is_active = Column(Boolean, default=True)
    is_production = Column(Boolean, default=False)
    
    # Performance Metrics
    accuracy = Column(Float)
    precision = Column(Float)
    recall = Column(Float)
    f1_score = Column(Float)
    total_predictions = Column(Integer, default=0)
    correct_predictions = Column(Integer, default=0)
    
    # Metadata
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    last_training = Column(DateTime)
    last_evaluation = Column(DateTime)


class SystemLog(Base):
    """System activity and error logs"""
    __tablename__ = "system_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Log Information
    log_level = Column(String(20), nullable=False, index=True)  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    module = Column(String(50), nullable=False, index=True)  # trading, ai, market_data, etc.
    message = Column(Text, nullable=False)
    details = Column(JSON)  # Additional details as JSON
    
    # Context Information
    user_id = Column(Integer)
    trade_id = Column(Integer)
    symbol = Column(String(20))
    exchange = Column(String(20))
    
    # Error Information
    error_code = Column(String(50))
    stack_trace = Column(Text)
    
    # Metadata
    timestamp = Column(DateTime, default=func.now(), index=True)
    ip_address = Column(String(45))
    user_agent = Column(Text)


class SystemMetrics(Base):
    """System performance and health metrics"""
    __tablename__ = "system_metrics"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Metric Information
    metric_name = Column(String(100), nullable=False, index=True)
    metric_value = Column(Float, nullable=False)
    metric_unit = Column(String(20))  # seconds, percentage, count, etc.
    
    # Context
    component = Column(String(50), nullable=False)  # api, database, trading, ai
    environment = Column(String(20), default="production")  # development, staging, production
    
    # Metadata
    timestamp = Column(DateTime, default=func.now(), index=True)
    collected_at = Column(DateTime, default=func.now())


# Pydantic models for API
class SystemConfigCreate(BaseModel):
    """System configuration creation model"""
    config_key: str
    config_value: str
    config_type: str = Field(..., pattern="^(string|int|float|bool|json)$")
    description: Optional[str] = None
    category: str
    is_user_configurable: bool = True


class SystemConfigUpdate(BaseModel):
    """System configuration update model"""
    config_value: str
    description: Optional[str] = None
    is_active: Optional[bool] = None


class SystemConfigResponse(BaseModel):
    """System configuration response model"""
    id: int
    config_key: str
    config_value: str
    config_type: str
    description: Optional[str] = None
    category: str
    is_active: bool
    is_user_configurable: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class ExchangeCreate(BaseModel):
    """Exchange creation model"""
    name: str
    display_name: str
    api_url: Optional[str] = None
    testnet_url: Optional[str] = None
    min_trade_amount: float = 10.0
    max_trade_amount: float = 10000.0
    trading_fee: float = 0.001


class ExchangeResponse(BaseModel):
    """Exchange response model"""
    id: int
    name: str
    display_name: str
    is_active: bool
    is_trading_enabled: bool
    supports_websocket: bool
    min_trade_amount: float
    max_trade_amount: float
    trading_fee: float
    created_at: datetime
    
    class Config:
        from_attributes = True


class TradingPairCreate(BaseModel):
    """Trading pair creation model"""
    symbol: str
    base_asset: str
    quote_asset: str
    min_quantity: Optional[float] = None
    max_quantity: Optional[float] = None
    quantity_precision: int = 8
    price_precision: int = 8


class TradingPairResponse(BaseModel):
    """Trading pair response model"""
    id: int
    symbol: str
    base_asset: str
    quote_asset: str
    is_active: bool
    is_trading_enabled: bool
    current_price: Optional[float] = None
    volume_24h: Optional[float] = None
    price_change_24h: Optional[float] = None
    created_at: datetime
    last_price_update: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class AIModelResponse(BaseModel):
    """AI model response model"""
    id: int
    model_uuid: str
    name: str
    model_type: str
    version: str
    is_active: bool
    is_production: bool
    accuracy: Optional[float] = None
    total_predictions: int
    correct_predictions: int
    created_at: datetime
    last_training: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class SystemLogResponse(BaseModel):
    """System log response model"""
    id: int
    log_level: str
    module: str
    message: str
    user_id: Optional[int] = None
    symbol: Optional[str] = None
    exchange: Optional[str] = None
    timestamp: datetime
    
    class Config:
        from_attributes = True


class SystemMetricsResponse(BaseModel):
    """System metrics response model"""
    id: int
    metric_name: str
    metric_value: float
    metric_unit: Optional[str] = None
    component: str
    environment: str
    timestamp: datetime
    
    class Config:
        from_attributes = True

#!/usr/bin/env python3
"""
Development Server Startup Script
Author: inkbytefo
"""

import os
import sys
import subprocess
import time
from pathlib import Path


def check_env_file():
    """Check if .env file exists"""
    env_file = Path("backend/.env")
    env_example = Path("backend/.env.example")
    
    if not env_file.exists():
        if env_example.exists():
            print("📝 Creating .env file from .env.example...")
            import shutil
            shutil.copy(env_example, env_file)
            print("⚠️  Please edit backend/.env with your API keys!")
            return False
        else:
            print("❌ No .env.example file found!")
            return False
    return True


def start_database():
    """Start database with Docker Compose"""
    print("🐘 Starting PostgreSQL and Redis...")
    try:
        subprocess.run([
            "docker-compose", "up", "-d", "postgres", "redis"
        ], check=True)
        print("✅ Database services started")
        time.sleep(5)  # Wait for services to be ready
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start database: {e}")
        return False


def start_backend():
    """Start FastAPI backend"""
    print("🐍 Starting FastAPI backend...")
    try:
        os.chdir("backend")
        
        # Check if virtual environment exists
        venv_path = Path("venv")
        if not venv_path.exists():
            print("📦 Creating virtual environment...")
            subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
        
        # Determine activation script
        if sys.platform == "win32":
            activate_script = "venv\\Scripts\\activate"
            python_exe = "venv\\Scripts\\python"
            pip_exe = "venv\\Scripts\\pip"
        else:
            activate_script = "venv/bin/activate"
            python_exe = "venv/bin/python"
            pip_exe = "venv/bin/pip"
        
        # Install dependencies
        print("📦 Installing dependencies...")
        subprocess.run([pip_exe, "install", "-r", "requirements.txt"], check=True)
        
        # Start the server
        print("🚀 Starting FastAPI server...")
        subprocess.run([
            python_exe, "-m", "uvicorn", 
            "app.main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000", 
            "--reload"
        ])
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start backend: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⏹️ Backend stopped by user")
        return True


def main():
    """Main startup function"""
    print("🚀 AI Crypto Trading System - Development Startup")
    print("Author: inkbytefo")
    print("=" * 60)
    
    # Check environment file
    if not check_env_file():
        print("\n❌ Please configure your .env file first!")
        print("1. Edit backend/.env")
        print("2. Add your API keys (Binance, OpenAI, etc.)")
        print("3. Run this script again")
        return
    
    # Start database services
    if not start_database():
        print("❌ Failed to start database services")
        return
    
    print("\n📋 Services Status:")
    print("✅ PostgreSQL: Running on port 5432")
    print("✅ Redis: Running on port 6379")
    print("🔄 FastAPI: Starting on port 8000...")
    
    print("\n🔗 Access Points:")
    print("📊 API Documentation: http://localhost:8000/docs")
    print("🔍 API Alternative Docs: http://localhost:8000/redoc")
    print("💓 Health Check: http://localhost:8000/health")
    print("📈 Market Data: http://localhost:8000/api/v1/market/symbols")
    
    print("\n⚠️  Important Notes:")
    print("- Make sure Docker is running")
    print("- Configure your API keys in backend/.env")
    print("- Use Ctrl+C to stop the server")
    print("- Check logs for any errors")
    
    print("\n🎯 Starting backend server...")
    print("=" * 60)
    
    # Start backend
    start_backend()


if __name__ == "__main__":
    main()

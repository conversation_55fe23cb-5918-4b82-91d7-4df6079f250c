"""
Telegram Bot API Routes
Author: inkbytefo

API endpoints for Telegram bot management and webhook handling.
"""

import logging
from typing import Dict, Any, List
from fastapi import APIRouter, HTTPException, Depends, Request, BackgroundTasks
from pydantic import BaseModel

from app.core.config import settings
from app.services.telegram_bot import TelegramBotManager

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/telegram", tags=["telegram"])

# Global bot manager instance
bot_manager: TelegramBotManager = None


class WebhookUpdate(BaseModel):
    """Telegram webhook update model."""
    update_id: int
    message: Dict[str, Any] = None
    edited_message: Dict[str, Any] = None
    channel_post: Dict[str, Any] = None
    edited_channel_post: Dict[str, Any] = None
    inline_query: Dict[str, Any] = None
    chosen_inline_result: Dict[str, Any] = None
    callback_query: Dict[str, Any] = None


class NotificationRequest(BaseModel):
    """Notification request model."""
    user_id: int
    message: str
    parse_mode: str = "Markdown"


class AlertRequest(BaseModel):
    """Price alert request model."""
    user_id: int
    symbol: str
    price: float
    condition: str = "above"


async def get_bot_manager() -> TelegramBotManager:
    """Get bot manager instance."""
    global bot_manager
    if bot_manager is None:
        bot_manager = TelegramBotManager()
        await bot_manager.initialize()
    return bot_manager


@router.post("/webhook")
async def telegram_webhook(
    update: WebhookUpdate,
    request: Request,
    background_tasks: BackgroundTasks
):
    """
    Handle Telegram webhook updates.
    This endpoint receives updates from Telegram when using webhook mode.
    """
    try:
        # Verify webhook secret if configured
        if settings.TELEGRAM_WEBHOOK_SECRET:
            secret_header = request.headers.get("X-Telegram-Bot-Api-Secret-Token")
            if secret_header != settings.TELEGRAM_WEBHOOK_SECRET:
                raise HTTPException(status_code=403, detail="Invalid webhook secret")
        
        # Process update in background
        background_tasks.add_task(process_webhook_update, update.dict())
        
        return {"status": "ok"}
        
    except Exception as e:
        logger.error(f"Error processing webhook: {e}")
        raise HTTPException(status_code=500, detail="Webhook processing failed")


async def process_webhook_update(update_data: Dict[str, Any]):
    """Process webhook update in background."""
    try:
        bot_manager = await get_bot_manager()
        # TODO: Process the update with bot manager
        logger.info(f"Processing webhook update: {update_data.get('update_id')}")
        
    except Exception as e:
        logger.error(f"Error processing webhook update: {e}")


@router.post("/start")
async def start_bot():
    """Start the Telegram bot."""
    try:
        bot_manager = await get_bot_manager()
        
        # Start bot in background task
        import asyncio
        asyncio.create_task(bot_manager.start())
        
        return {"status": "Bot started successfully"}
        
    except Exception as e:
        logger.error(f"Error starting bot: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start bot: {str(e)}")


@router.post("/stop")
async def stop_bot():
    """Stop the Telegram bot."""
    try:
        global bot_manager
        if bot_manager:
            await bot_manager.stop()
            bot_manager = None
        
        return {"status": "Bot stopped successfully"}
        
    except Exception as e:
        logger.error(f"Error stopping bot: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to stop bot: {str(e)}")


@router.get("/status")
async def get_bot_status():
    """Get Telegram bot status."""
    try:
        global bot_manager
        
        if bot_manager is None:
            return {
                "status": "stopped",
                "is_running": False,
                "message": "Bot is not initialized"
            }
        
        return {
            "status": "running" if bot_manager._is_running else "stopped",
            "is_running": bot_manager._is_running,
            "message": "Bot is operational" if bot_manager._is_running else "Bot is stopped"
        }
        
    except Exception as e:
        logger.error(f"Error getting bot status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get status: {str(e)}")


@router.post("/send-notification")
async def send_notification(notification: NotificationRequest):
    """Send notification to specific user."""
    try:
        bot_manager = await get_bot_manager()
        
        success = await bot_manager.send_notification(
            user_id=notification.user_id,
            message=notification.message,
            parse_mode=notification.parse_mode
        )
        
        if success:
            return {"status": "Notification sent successfully"}
        else:
            raise HTTPException(status_code=400, detail="Failed to send notification")
            
    except Exception as e:
        logger.error(f"Error sending notification: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to send notification: {str(e)}")


@router.post("/set-alert")
async def set_price_alert(alert: AlertRequest):
    """Set price alert for user."""
    try:
        bot_manager = await get_bot_manager()
        
        success = await bot_manager.notification_engine.set_alert(
            user_id=alert.user_id,
            alert_config={
                "symbol": alert.symbol,
                "price": alert.price,
                "condition": alert.condition
            }
        )
        
        if success:
            return {"status": f"Price alert set for {alert.symbol} at ${alert.price}"}
        else:
            raise HTTPException(status_code=400, detail="Failed to set price alert")
            
    except Exception as e:
        logger.error(f"Error setting price alert: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to set alert: {str(e)}")


@router.get("/alerts/{user_id}")
async def get_user_alerts(user_id: int):
    """Get all alerts for specific user."""
    try:
        bot_manager = await get_bot_manager()
        
        alerts = await bot_manager.notification_engine.get_user_alerts(user_id)
        
        return {
            "user_id": user_id,
            "alerts": alerts,
            "count": len(alerts)
        }
        
    except Exception as e:
        logger.error(f"Error getting user alerts: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get alerts: {str(e)}")


@router.delete("/alerts/{user_id}")
async def remove_user_alert(user_id: int, symbol: str, price: float):
    """Remove specific alert for user."""
    try:
        bot_manager = await get_bot_manager()
        
        success = await bot_manager.notification_engine.remove_alert(
            user_id=user_id,
            symbol=symbol,
            price=price
        )
        
        if success:
            return {"status": f"Alert removed for {symbol} at ${price}"}
        else:
            raise HTTPException(status_code=404, detail="Alert not found")
            
    except Exception as e:
        logger.error(f"Error removing alert: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to remove alert: {str(e)}")


@router.get("/users")
async def get_bot_users():
    """Get bot user statistics."""
    try:
        bot_manager = await get_bot_manager()
        
        user_stats = await bot_manager.user_manager.get_user_stats()
        notification_stats = await bot_manager.notification_engine.get_statistics()
        
        return {
            "user_statistics": user_stats,
            "notification_statistics": notification_stats
        }
        
    except Exception as e:
        logger.error(f"Error getting user stats: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get user stats: {str(e)}")


@router.post("/broadcast")
async def broadcast_message(message: str, user_type: str = "all"):
    """Broadcast message to users."""
    try:
        bot_manager = await get_bot_manager()
        
        # Get user list based on type
        if user_type == "admins":
            users = [uid for uid in bot_manager.user_manager.admin_users]
        else:
            users = list(bot_manager.user_manager.authorized_users)
        
        # Send to all users
        success_count = 0
        for user_id in users:
            success = await bot_manager.send_notification(user_id, message)
            if success:
                success_count += 1
        
        return {
            "status": f"Broadcast sent to {success_count}/{len(users)} users",
            "success_count": success_count,
            "total_users": len(users)
        }
        
    except Exception as e:
        logger.error(f"Error broadcasting message: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to broadcast: {str(e)}")


@router.get("/health")
async def bot_health_check():
    """Health check for Telegram bot service."""
    try:
        global bot_manager
        
        health_status = {
            "service": "telegram_bot",
            "status": "healthy",
            "timestamp": "2025-07-26T12:00:00Z",
            "components": {
                "bot_manager": "unknown",
                "ai_assistant": "unknown",
                "notification_engine": "unknown",
                "user_manager": "unknown"
            }
        }
        
        if bot_manager:
            health_status["components"]["bot_manager"] = "healthy" if bot_manager._is_running else "stopped"
            health_status["components"]["ai_assistant"] = "healthy"
            health_status["components"]["notification_engine"] = "healthy"
            health_status["components"]["user_manager"] = "healthy"
        else:
            health_status["status"] = "stopped"
            health_status["components"] = {k: "stopped" for k in health_status["components"]}
        
        return health_status
        
    except Exception as e:
        logger.error(f"Error in health check: {e}")
        return {
            "service": "telegram_bot",
            "status": "unhealthy",
            "error": str(e),
            "timestamp": "2025-07-26T12:00:00Z"
        }

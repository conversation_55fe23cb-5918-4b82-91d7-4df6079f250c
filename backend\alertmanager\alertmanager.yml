# Alertmanager Configuration for Crypto Trading Bot
# Author: inkbytefo

global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'password'

# The directory from which notification templates are read.
templates:
  - '/etc/alertmanager/templates/*.tmpl'

# The root route on which each incoming alert enters.
route:
  # The labels by which incoming alerts are grouped together.
  group_by: ['alertname', 'service', 'category']

  # When a new group of alerts is created by an incoming alert, wait at
  # least 'group_wait' to send the initial notification.
  group_wait: 10s

  # When the first notification was sent, wait 'group_interval' to send a batch
  # of new alerts that started firing for that group.
  group_interval: 10s

  # If an alert has successfully been sent, wait 'repeat_interval' to
  # resend them.
  repeat_interval: 1h

  # A default receiver
  receiver: 'web.hook'

  # All the above attributes are inherited by all child routes and can
  # overwritten on each.
  routes:
    # Critical alerts - immediate notification
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 5s
      group_interval: 5s
      repeat_interval: 30m

    # Trading alerts - specialized handling
    - match:
        category: trading
      receiver: 'trading-alerts'
      group_wait: 30s
      group_interval: 1m
      repeat_interval: 2h

    # System alerts
    - match:
        category: system
      receiver: 'system-alerts'
      group_wait: 1m
      group_interval: 5m
      repeat_interval: 4h

    # Risk management alerts - high priority
    - match:
        category: risk_management
      receiver: 'risk-alerts'
      group_wait: 10s
      group_interval: 30s
      repeat_interval: 1h

# Inhibition rules allow to mute a set of alerts given that another alert is firing.
inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'service', 'instance']

  - source_match:
      alertname: 'ServiceDown'
    target_match_re:
      service: '.*'
    equal: ['service']

receivers:
  - name: 'web.hook'
    webhook_configs:
      - url: 'http://127.0.0.1:5001/'

  - name: 'critical-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '🚨 CRITICAL ALERT: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Labels: {{ range .Labels.SortedPairs }}{{ .Name }}={{ .Value }} {{ end }}
          Started: {{ .StartsAt }}
          {{ end }}
        headers:
          Priority: 'high'
    
    webhook_configs:
      - url: 'http://localhost:8000/api/v1/webhooks/alerts'
        send_resolved: true
        http_config:
          basic_auth:
            username: 'alertmanager'
            password: 'webhook_password'

    # Slack notifications for critical alerts
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#trading-alerts'
        title: '🚨 Critical Trading Bot Alert'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Severity:* {{ .Labels.severity }}
          *Service:* {{ .Labels.service }}
          *Started:* {{ .StartsAt }}
          {{ end }}
        color: 'danger'

  - name: 'trading-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '📈 Trading Alert: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Started: {{ .StartsAt }}
          {{ end }}

    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#trading-alerts'
        title: '📈 Trading Alert'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          {{ end }}
        color: 'warning'

  - name: 'system-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '🖥️ System Alert: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Started: {{ .StartsAt }}
          {{ end }}

  - name: 'risk-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '⚠️ Risk Management Alert: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Started: {{ .StartsAt }}
          {{ end }}
        headers:
          Priority: 'high'

    webhook_configs:
      - url: 'http://localhost:8000/api/v1/webhooks/risk-alerts'
        send_resolved: true

# Notification templates
templates:
  - '/etc/alertmanager/templates/default.tmpl'

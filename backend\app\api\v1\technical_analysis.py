"""
Technical Analysis API Endpoints
Author: inkbytefo
"""

from fastapi import APIRouter, HTTPException, Query, BackgroundTasks
from typing import List, Dict, Optional, Any
import logging
from datetime import datetime

from app.services.technical_analysis_service import technical_analysis_service, SignalType, TrendDirection
from app.core.config import settings

router = APIRouter()
logger = logging.getLogger("technical_analysis_api")


@router.get("/analyze/{symbol}")
async def analyze_symbol(
    symbol: str,
    timeframe: str = Query("1h", description="Timeframe (1m, 5m, 15m, 1h, 4h, 1d)"),
    exchange: str = Query("binance", description="Exchange name"),
    limit: int = Query(200, description="Number of data points to analyze")
):
    """Get comprehensive technical analysis for a symbol"""
    try:
        logger.info(f"Technical analysis requested for {symbol} ({timeframe})")
        
        # Validate symbol format
        if '/' not in symbol:
            symbol = f"{symbol}/USDT"
        
        # Perform technical analysis
        signal = await technical_analysis_service.analyze_symbol(
            symbol=symbol,
            timeframe=timeframe,
            limit=limit,
            exchange=exchange
        )
        
        if not signal:
            raise HTTPException(
                status_code=404, 
                detail=f"Unable to perform technical analysis for {symbol}"
            )
        
        # Format response
        response = {
            "symbol": signal.symbol,
            "timeframe": timeframe,
            "exchange": exchange,
            "signal": {
                "type": signal.signal_type.value,
                "strength": round(signal.strength, 3),
                "confidence": round(signal.confidence, 3),
                "entry_price": signal.entry_price,
                "target_price": signal.target_price,
                "stop_loss_price": signal.stop_loss_price,
                "reasoning": signal.reasoning,
                "timestamp": signal.timestamp.isoformat()
            },
            "indicators": {
                "rsi": signal.indicators.rsi,
                "macd": signal.indicators.macd,
                "macd_signal": signal.indicators.macd_signal,
                "macd_histogram": signal.indicators.macd_histogram,
                "sma_20": signal.indicators.sma_20,
                "sma_50": signal.indicators.sma_50,
                "ema_12": signal.indicators.ema_12,
                "ema_26": signal.indicators.ema_26,
                "bb_upper": signal.indicators.bb_upper,
                "bb_middle": signal.indicators.bb_middle,
                "bb_lower": signal.indicators.bb_lower,
                "bb_width": signal.indicators.bb_width,
                "volume_spike": signal.indicators.volume_spike,
                "support_level": signal.indicators.support_level,
                "resistance_level": signal.indicators.resistance_level,
                "trend_direction": signal.indicators.trend_direction.value if signal.indicators.trend_direction else None,
                "trend_strength": signal.indicators.trend_strength
            },
            "status": "success"
        }
        
        return response
        
    except Exception as e:
        logger.error(f"Error in technical analysis for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/multi-timeframe/{symbol}")
async def multi_timeframe_analysis(
    symbol: str,
    timeframes: str = Query("15m,1h,4h,1d", description="Comma-separated timeframes"),
    exchange: str = Query("binance", description="Exchange name")
):
    """Get technical analysis for multiple timeframes"""
    try:
        logger.info(f"Multi-timeframe analysis requested for {symbol}")
        
        # Validate symbol format
        if '/' not in symbol:
            symbol = f"{symbol}/USDT"
        
        # Parse timeframes
        timeframe_list = [tf.strip() for tf in timeframes.split(",")]
        
        # Get analysis for each timeframe
        results = await technical_analysis_service.get_multiple_timeframe_analysis(
            symbol=symbol,
            timeframes=timeframe_list,
            exchange=exchange
        )
        
        # Format response
        formatted_results = {}
        overall_sentiment = {"bullish": 0, "bearish": 0, "neutral": 0}
        
        for timeframe, signal in results.items():
            if signal:
                formatted_results[timeframe] = {
                    "signal_type": signal.signal_type.value,
                    "strength": round(signal.strength, 3),
                    "confidence": round(signal.confidence, 3),
                    "reasoning": signal.reasoning,
                    "entry_price": signal.entry_price,
                    "target_price": signal.target_price,
                    "stop_loss_price": signal.stop_loss_price
                }
                
                # Count sentiment
                if signal.signal_type == SignalType.BUY:
                    overall_sentiment["bullish"] += signal.strength
                elif signal.signal_type == SignalType.SELL:
                    overall_sentiment["bearish"] += signal.strength
                else:
                    overall_sentiment["neutral"] += 1
            else:
                formatted_results[timeframe] = {
                    "signal_type": "no_data",
                    "strength": 0,
                    "confidence": 0,
                    "reasoning": "Insufficient data for analysis"
                }
        
        # Calculate overall sentiment
        total_signals = len([r for r in formatted_results.values() if r["signal_type"] != "no_data"])
        if total_signals > 0:
            bullish_ratio = overall_sentiment["bullish"] / total_signals
            bearish_ratio = overall_sentiment["bearish"] / total_signals
            
            if bullish_ratio > bearish_ratio and bullish_ratio > 0.3:
                overall_signal = "bullish"
            elif bearish_ratio > bullish_ratio and bearish_ratio > 0.3:
                overall_signal = "bearish"
            else:
                overall_signal = "neutral"
        else:
            overall_signal = "no_data"
        
        return {
            "symbol": symbol,
            "exchange": exchange,
            "timeframes": formatted_results,
            "overall_sentiment": {
                "signal": overall_signal,
                "bullish_strength": round(overall_sentiment["bullish"], 3),
                "bearish_strength": round(overall_sentiment["bearish"], 3),
                "neutral_count": overall_sentiment["neutral"]
            },
            "analysis_timestamp": datetime.now().isoformat(),
            "status": "success"
        }
        
    except Exception as e:
        logger.error(f"Error in multi-timeframe analysis for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/market-overview")
async def market_overview(
    symbols: str = Query(None, description="Comma-separated symbols (default: all configured symbols)"),
    timeframe: str = Query("1h", description="Timeframe for analysis"),
    exchange: str = Query("binance", description="Exchange name")
):
    """Get technical analysis overview for multiple symbols"""
    try:
        logger.info(f"Market overview requested for timeframe {timeframe}")
        
        # Parse symbols
        if symbols:
            symbol_list = [s.strip() for s in symbols.split(",")]
            # Ensure proper format
            symbol_list = [s if '/' in s else f"{s}/USDT" for s in symbol_list]
        else:
            symbol_list = settings.DEFAULT_SYMBOLS
        
        # Get analysis for each symbol
        results = await technical_analysis_service.get_market_overview(
            symbols=symbol_list,
            timeframe=timeframe,
            exchange=exchange
        )
        
        # Format response
        formatted_results = {}
        market_sentiment = {"bullish": 0, "bearish": 0, "neutral": 0}
        
        for symbol, signal in results.items():
            if signal:
                formatted_results[symbol] = {
                    "signal_type": signal.signal_type.value,
                    "strength": round(signal.strength, 3),
                    "confidence": round(signal.confidence, 3),
                    "entry_price": signal.entry_price,
                    "reasoning": signal.reasoning[:100] + "..." if len(signal.reasoning) > 100 else signal.reasoning,
                    "rsi": signal.indicators.rsi,
                    "trend_direction": signal.indicators.trend_direction.value if signal.indicators.trend_direction else None
                }
                
                # Count market sentiment
                if signal.signal_type == SignalType.BUY:
                    market_sentiment["bullish"] += 1
                elif signal.signal_type == SignalType.SELL:
                    market_sentiment["bearish"] += 1
                else:
                    market_sentiment["neutral"] += 1
            else:
                formatted_results[symbol] = {
                    "signal_type": "no_data",
                    "strength": 0,
                    "confidence": 0,
                    "reasoning": "Analysis failed"
                }
        
        # Calculate market sentiment percentages
        total_analyzed = len([r for r in formatted_results.values() if r["signal_type"] != "no_data"])
        sentiment_percentages = {}
        
        if total_analyzed > 0:
            sentiment_percentages = {
                "bullish": round((market_sentiment["bullish"] / total_analyzed) * 100, 1),
                "bearish": round((market_sentiment["bearish"] / total_analyzed) * 100, 1),
                "neutral": round((market_sentiment["neutral"] / total_analyzed) * 100, 1)
            }
        
        return {
            "timeframe": timeframe,
            "exchange": exchange,
            "symbols_analyzed": len(symbol_list),
            "successful_analysis": total_analyzed,
            "market_sentiment": {
                "counts": market_sentiment,
                "percentages": sentiment_percentages
            },
            "symbol_analysis": formatted_results,
            "analysis_timestamp": datetime.now().isoformat(),
            "status": "success"
        }
        
    except Exception as e:
        logger.error(f"Error in market overview: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/indicators/{symbol}")
async def get_technical_indicators(
    symbol: str,
    timeframe: str = Query("1h", description="Timeframe"),
    exchange: str = Query("binance", description="Exchange name")
):
    """Get detailed technical indicators for a symbol"""
    try:
        logger.info(f"Technical indicators requested for {symbol}")
        
        # Validate symbol format
        if '/' not in symbol:
            symbol = f"{symbol}/USDT"
        
        # Get technical analysis
        signal = await technical_analysis_service.analyze_symbol(
            symbol=symbol,
            timeframe=timeframe,
            exchange=exchange
        )
        
        if not signal or not signal.indicators:
            raise HTTPException(
                status_code=404,
                detail=f"Unable to calculate indicators for {symbol}"
            )
        
        indicators = signal.indicators
        
        return {
            "symbol": symbol,
            "timeframe": timeframe,
            "exchange": exchange,
            "indicators": {
                "moving_averages": {
                    "sma_20": indicators.sma_20,
                    "sma_50": indicators.sma_50,
                    "ema_12": indicators.ema_12,
                    "ema_26": indicators.ema_26
                },
                "momentum": {
                    "rsi": indicators.rsi,
                    "rsi_signal": "oversold" if indicators.rsi and indicators.rsi < 30 else "overbought" if indicators.rsi and indicators.rsi > 70 else "neutral"
                },
                "macd": {
                    "macd": indicators.macd,
                    "signal": indicators.macd_signal,
                    "histogram": indicators.macd_histogram,
                    "trend": "bullish" if indicators.macd and indicators.macd_signal and indicators.macd > indicators.macd_signal else "bearish"
                },
                "bollinger_bands": {
                    "upper": indicators.bb_upper,
                    "middle": indicators.bb_middle,
                    "lower": indicators.bb_lower,
                    "width": indicators.bb_width,
                    "squeeze": indicators.bb_width and indicators.bb_width < (indicators.bb_middle * 0.02) if indicators.bb_middle else False
                },
                "volume": {
                    "sma": indicators.volume_sma,
                    "spike_detected": indicators.volume_spike
                },
                "support_resistance": {
                    "support": indicators.support_level,
                    "resistance": indicators.resistance_level
                },
                "trend": {
                    "direction": indicators.trend_direction.value if indicators.trend_direction else None,
                    "strength": indicators.trend_strength
                }
            },
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }
        
    except Exception as e:
        logger.error(f"Error getting indicators for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/position-size")
async def calculate_position_size(
    symbol: str,
    account_balance: float,
    risk_percentage: float = Query(0.02, description="Risk percentage (0.01 = 1%)"),
    timeframe: str = Query("1h", description="Timeframe for analysis"),
    exchange: str = Query("binance", description="Exchange name")
):
    """Calculate optimal position size based on technical analysis and risk management"""
    try:
        logger.info(f"Position size calculation requested for {symbol}")
        
        # Validate inputs
        if account_balance <= 0:
            raise HTTPException(status_code=400, detail="Account balance must be positive")
        
        if risk_percentage <= 0 or risk_percentage > 0.1:
            raise HTTPException(status_code=400, detail="Risk percentage must be between 0.001 and 0.1 (0.1% to 10%)")
        
        # Validate symbol format
        if '/' not in symbol:
            symbol = f"{symbol}/USDT"
        
        # Get technical analysis
        signal = await technical_analysis_service.analyze_symbol(
            symbol=symbol,
            timeframe=timeframe,
            exchange=exchange
        )
        
        if not signal:
            raise HTTPException(
                status_code=404,
                detail=f"Unable to perform technical analysis for {symbol}"
            )
        
        # Calculate position size
        position_data = await technical_analysis_service.calculate_position_size(
            signal=signal,
            account_balance=account_balance,
            risk_percentage=risk_percentage
        )
        
        return {
            "symbol": symbol,
            "signal": {
                "type": signal.signal_type.value,
                "strength": round(signal.strength, 3),
                "confidence": round(signal.confidence, 3),
                "entry_price": signal.entry_price,
                "target_price": signal.target_price,
                "stop_loss_price": signal.stop_loss_price
            },
            "position_sizing": {
                "account_balance": account_balance,
                "risk_percentage": risk_percentage * 100,
                "position_size": position_data.get("position_size", 0),
                "risk_amount": position_data.get("risk_amount", 0),
                "risk_per_unit": position_data.get("risk_per_unit", 0),
                "max_position_value": position_data.get("max_position_value", 0)
            },
            "risk_reward": {
                "risk_amount": position_data.get("risk_amount", 0),
                "potential_profit": round((signal.target_price - signal.entry_price) * position_data.get("position_size", 0), 2) if signal.target_price and signal.entry_price else 0,
                "risk_reward_ratio": round((signal.target_price - signal.entry_price) / (signal.entry_price - signal.stop_loss_price), 2) if signal.target_price and signal.stop_loss_price and signal.entry_price else 0
            },
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }
        
    except Exception as e:
        logger.error(f"Error calculating position size for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

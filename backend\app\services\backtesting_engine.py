"""
Backtesting Engine for AI Crypto Trading System
Author: inkbytefo

This service provides:
- Historical data backtesting
- Strategy performance evaluation
- Risk metrics calculation
- Trade simulation and analysis
"""

import asyncio
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any, <PERSON><PERSON>
from dataclasses import dataclass
import json
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.models.backtesting import (
    BacktestStrategy, BacktestRun, BacktestTrade, BacktestStatus,
    BacktestPerformanceMetrics
)
from app.services.signal_generation_service import signal_generation_service
from app.services.market_data_service import market_data_service
from app.services.risk_management_service import risk_management_service

logger = logging.getLogger(__name__)


@dataclass
class BacktestPosition:
    """Active position during backtesting"""
    symbol: str
    entry_date: datetime
    entry_price: float
    quantity: float
    stop_loss_price: Optional[float]
    take_profit_price: Optional[float]
    entry_signal_strength: float
    entry_confidence: float
    entry_reasoning: str
    max_favorable_excursion: float = 0.0
    max_adverse_excursion: float = 0.0


@dataclass
class BacktestState:
    """Current state of backtest execution"""
    current_capital: float
    positions: Dict[str, BacktestPosition]
    trade_history: List[Dict[str, Any]]
    equity_curve: List[Dict[str, Any]]
    drawdown_periods: List[Dict[str, Any]]
    current_drawdown: float = 0.0
    peak_capital: float = 0.0


class BacktestingEngine:
    """Backtesting Engine for strategy evaluation"""
    
    def __init__(self):
        self.db: Optional[Session] = None
        
    async def run_backtest(
        self,
        strategy_id: int,
        symbols: List[str],
        timeframes: List[str],
        start_date: datetime,
        end_date: datetime,
        initial_capital: float,
        user_id: int,
        name: str,
        description: Optional[str] = None,
        parameters: Optional[Dict[str, Any]] = None
    ) -> int:
        """Run a complete backtest"""
        
        # Create backtest run record
        db = next(get_db())
        try:
            strategy = db.query(BacktestStrategy).filter(
                BacktestStrategy.id == strategy_id
            ).first()
            
            if not strategy:
                raise ValueError(f"Strategy with ID {strategy_id} not found")
            
            backtest_run = BacktestRun(
                strategy_id=strategy_id,
                user_id=user_id,
                name=name,
                description=description,
                symbols=symbols,
                timeframes=timeframes,
                start_date=start_date,
                end_date=end_date,
                initial_capital=initial_capital,
                status=BacktestStatus.RUNNING.value,
                started_at=datetime.now(),
                parameters=parameters  # Store custom parameters for this backtest run
            )
            
            db.add(backtest_run)
            db.commit()
            db.refresh(backtest_run)
            
            logger.info(f"Starting backtest {backtest_run.run_uuid} for strategy {strategy.name}")
            
            # Run backtest asynchronously
            asyncio.create_task(self._execute_backtest(backtest_run.id, strategy, db))
            
            return backtest_run.id
            
        except Exception as e:
            logger.error(f"Error starting backtest: {e}")
            if 'backtest_run' in locals():
                backtest_run.status = BacktestStatus.FAILED.value
                backtest_run.error_message = str(e)
                db.commit()
            raise
        finally:
            db.close()
    
    async def _execute_backtest(
        self,
        backtest_run_id: int,
        strategy: BacktestStrategy,
        db: Session
    ):
        """Execute the actual backtest"""
        try:
            backtest_run = db.query(BacktestRun).filter(
                BacktestRun.id == backtest_run_id
            ).first()
            
            if not backtest_run:
                logger.error(f"Backtest run {backtest_run_id} not found")
                return
            
            # Initialize backtest state
            state = BacktestState(
                current_capital=backtest_run.initial_capital,
                positions={},
                trade_history=[],
                equity_curve=[],
                drawdown_periods=[],
                peak_capital=backtest_run.initial_capital
            )
            
            total_days = (backtest_run.end_date - backtest_run.start_date).days
            processed_days = 0
            
            # Process each symbol and timeframe combination
            for symbol in backtest_run.symbols:
                for timeframe in backtest_run.timeframes:
                    logger.info(f"Processing {symbol} on {timeframe} timeframe")
                    
                    # Update progress
                    backtest_run.current_symbol = symbol
                    backtest_run.progress_percentage = (processed_days / total_days) * 100
                    db.commit()
                    
                    # Get historical data
                    historical_data = await market_data_service.get_historical_data(
                        symbol=symbol,
                        timeframe=timeframe,
                        start_date=backtest_run.start_date,
                        end_date=backtest_run.end_date,
                        exchange="binance"
                    )
                    
                    if historical_data is None or len(historical_data) < 50:
                        logger.warning(f"Insufficient data for {symbol} {timeframe}")
                        continue
                    
                    # Process each time period with custom parameters
                    await self._process_timeframe_data(
                        symbol, timeframe, historical_data, strategy, state, backtest_run, db,
                        custom_parameters=backtest_run.parameters
                    )
                    
                    processed_days += total_days // (len(backtest_run.symbols) * len(backtest_run.timeframes))
            
            # Close any remaining positions
            await self._close_all_positions(state, backtest_run.end_date)
            
            # Calculate final metrics
            performance_metrics = self._calculate_performance_metrics(
                state, backtest_run.initial_capital
            )
            
            # Update backtest run with results
            self._update_backtest_results(backtest_run, performance_metrics, state, db)
            
            # Save individual trades
            await self._save_backtest_trades(backtest_run.id, state.trade_history, db)
            
            backtest_run.status = BacktestStatus.COMPLETED.value
            backtest_run.completed_at = datetime.now()
            backtest_run.progress_percentage = 100.0
            
            db.commit()
            
            logger.info(f"Backtest {backtest_run.run_uuid} completed successfully")
            
        except Exception as e:
            logger.error(f"Error executing backtest {backtest_run_id}: {e}")
            backtest_run.status = BacktestStatus.FAILED.value
            backtest_run.error_message = str(e)
            backtest_run.completed_at = datetime.now()
            db.commit()
    
    async def _process_timeframe_data(
        self,
        symbol: str,
        timeframe: str,
        data: pd.DataFrame,
        strategy: BacktestStrategy,
        state: BacktestState,
        backtest_run: BacktestRun,
        db: Session,
        custom_parameters: Optional[Dict[str, Any]] = None
    ):
        """Process historical data for a specific symbol and timeframe"""
        
        for i in range(len(data)):
            current_time = data.index[i]
            current_price = data.iloc[i]['close']
            
            # Update current date in backtest run
            backtest_run.current_date = current_time
            
            # Update existing positions
            await self._update_positions(state, current_time, current_price, symbol)
            
            # Check for exit signals on existing positions
            await self._check_exit_conditions(
                state, symbol, current_time, current_price, data.iloc[:i+1], strategy
            )
            
            # Generate new signals if no position exists for this symbol
            if symbol not in state.positions:
                signal = await self._generate_signal_for_backtest(
                    symbol, timeframe, data.iloc[:i+1], strategy, custom_parameters
                )
                
                if signal and self._should_enter_trade(signal, strategy):
                    await self._enter_position(
                        state, symbol, current_time, current_price, signal, strategy
                    )
            
            # Update equity curve
            total_equity = self._calculate_total_equity(state, current_price, symbol)
            state.equity_curve.append({
                'date': current_time,
                'equity': total_equity,
                'symbol': symbol
            })
            
            # Update drawdown tracking
            self._update_drawdown_tracking(state, total_equity)
            
            # Commit periodically to avoid memory issues
            if i % 1000 == 0:
                db.commit()
    
    async def _generate_signal_for_backtest(
        self,
        symbol: str,
        timeframe: str,
        historical_data: pd.DataFrame,
        strategy: BacktestStrategy,
        custom_parameters: Optional[Dict[str, Any]] = None
    ) -> Optional[Any]:
        """Generate trading signal using historical data"""
        try:
            # Merge strategy parameters with custom parameters
            parameters = {}
            if strategy.parameters:
                parameters.update(strategy.parameters)
            if custom_parameters:
                parameters.update(custom_parameters)

            # Use the signal generation service with historical data and custom parameters
            signal = await signal_generation_service.generate_signal(
                symbol=symbol,
                timeframe=timeframe,
                exchange="binance",
                parameters=parameters if parameters else None
            )
            
            return signal
            
        except Exception as e:
            logger.error(f"Error generating signal for backtest: {e}")
            return None
    
    def _should_enter_trade(self, signal: Any, strategy: BacktestStrategy) -> bool:
        """Determine if we should enter a trade based on signal and strategy"""
        if not signal:
            return False
        
        # Check minimum confidence
        if signal.confidence < strategy.min_confidence:
            return False
        
        # Check signal strength
        if abs(signal.overall_score) < 0.3:  # Minimum signal strength
            return False
        
        # Additional strategy-specific conditions
        entry_conditions = strategy.entry_conditions or {}
        
        # Check technical factors if specified
        if 'min_technical_score' in entry_conditions:
            technical_score = sum(f.weight * f.value for f in signal.technical_factors)
            if technical_score < entry_conditions['min_technical_score']:
                return False
        
        return True
    
    async def _enter_position(
        self,
        state: BacktestState,
        symbol: str,
        entry_time: datetime,
        entry_price: float,
        signal: Any,
        strategy: BacktestStrategy
    ):
        """Enter a new position"""
        
        # Calculate position size
        position_size_result = await risk_management_service.calculate_position_size(
            symbol=symbol,
            entry_price=entry_price,
            portfolio_value=state.current_capital,
            signal_confidence=signal.confidence
        )
        
        # Limit position size based on strategy
        max_position_value = state.current_capital * strategy.max_position_size
        position_value = min(position_size_result.recommended_size * entry_price, max_position_value)
        quantity = position_value / entry_price
        
        # Calculate stop loss and take profit
        stop_loss_price = entry_price * (1 - strategy.stop_loss_percentage)
        take_profit_price = entry_price * (1 + strategy.take_profit_percentage)
        
        # Create position
        position = BacktestPosition(
            symbol=symbol,
            entry_date=entry_time,
            entry_price=entry_price,
            quantity=quantity,
            stop_loss_price=stop_loss_price,
            take_profit_price=take_profit_price,
            entry_signal_strength=signal.overall_score,
            entry_confidence=signal.confidence,
            entry_reasoning=signal.reasoning or "Signal-based entry"
        )
        
        state.positions[symbol] = position
        state.current_capital -= position_value  # Reduce available capital
        
        logger.debug(f"Entered position: {symbol} at {entry_price}, quantity: {quantity}")
    
    async def _update_positions(
        self,
        state: BacktestState,
        current_time: datetime,
        current_price: float,
        symbol: str
    ):
        """Update existing positions with current price"""
        if symbol in state.positions:
            position = state.positions[symbol]
            
            # Update max favorable/adverse excursion
            if current_price > position.entry_price:
                position.max_favorable_excursion = max(
                    position.max_favorable_excursion,
                    current_price - position.entry_price
                )
            else:
                position.max_adverse_excursion = max(
                    position.max_adverse_excursion,
                    position.entry_price - current_price
                )
    
    def _calculate_total_equity(self, state: BacktestState, current_price: float, symbol: str) -> float:
        """Calculate total portfolio equity"""
        total_equity = state.current_capital
        
        # Add value of open positions
        if symbol in state.positions:
            position = state.positions[symbol]
            position_value = position.quantity * current_price
            total_equity += position_value
        
        return total_equity
    
    def _update_drawdown_tracking(self, state: BacktestState, current_equity: float):
        """Update drawdown tracking"""
        if current_equity > state.peak_capital:
            # New peak, end any current drawdown period
            if state.current_drawdown > 0:
                state.drawdown_periods.append({
                    'start_equity': state.peak_capital,
                    'end_equity': current_equity,
                    'max_drawdown': state.current_drawdown,
                    'recovery_time': len(state.equity_curve)
                })
                state.current_drawdown = 0.0
            
            state.peak_capital = current_equity
        else:
            # Calculate current drawdown
            drawdown = (state.peak_capital - current_equity) / state.peak_capital
            state.current_drawdown = max(state.current_drawdown, drawdown)

    async def _check_exit_conditions(
        self,
        state: BacktestState,
        symbol: str,
        current_time: datetime,
        current_price: float,
        historical_data: pd.DataFrame,
        strategy: BacktestStrategy
    ):
        """Check if we should exit existing positions"""
        if symbol not in state.positions:
            return

        position = state.positions[symbol]
        exit_reason = None

        # Check stop loss
        if current_price <= position.stop_loss_price:
            exit_reason = "stop_loss"

        # Check take profit
        elif current_price >= position.take_profit_price:
            exit_reason = "take_profit"

        # Check time-based exit (optional)
        elif (current_time - position.entry_date).days > 30:  # Max 30 days
            exit_reason = "timeout"

        # Check signal-based exit
        else:
            # Generate exit signal
            exit_signal = await self._generate_signal_for_backtest(
                symbol, "1h", historical_data, strategy
            )

            if exit_signal and exit_signal.signal_type.value == "sell":
                if exit_signal.confidence > 0.7:  # High confidence exit
                    exit_reason = "signal_exit"

        # Execute exit if reason found
        if exit_reason:
            await self._exit_position(state, symbol, current_time, current_price, exit_reason)

    async def _exit_position(
        self,
        state: BacktestState,
        symbol: str,
        exit_time: datetime,
        exit_price: float,
        exit_reason: str
    ):
        """Exit an existing position"""
        if symbol not in state.positions:
            return

        position = state.positions[symbol]

        # Calculate trade results
        position_value = position.quantity * exit_price
        pnl = position_value - (position.quantity * position.entry_price)
        pnl_percentage = pnl / (position.quantity * position.entry_price)

        # Add fees (0.1% for both entry and exit)
        fees = (position.quantity * position.entry_price * 0.001) + (position_value * 0.001)
        net_pnl = pnl - fees

        # Calculate trade duration
        duration_hours = (exit_time - position.entry_date).total_seconds() / 3600

        # Create trade record
        trade_record = {
            'symbol': symbol,
            'entry_date': position.entry_date,
            'entry_price': position.entry_price,
            'exit_date': exit_time,
            'exit_price': exit_price,
            'quantity': position.quantity,
            'pnl': pnl,
            'pnl_percentage': pnl_percentage,
            'fees': fees,
            'net_pnl': net_pnl,
            'exit_reason': exit_reason,
            'duration_hours': duration_hours,
            'entry_signal_strength': position.entry_signal_strength,
            'entry_confidence': position.entry_confidence,
            'entry_reasoning': position.entry_reasoning,
            'max_favorable_excursion': position.max_favorable_excursion,
            'max_adverse_excursion': position.max_adverse_excursion,
            'is_winning_trade': net_pnl > 0
        }

        state.trade_history.append(trade_record)
        state.current_capital += position_value  # Add back to available capital

        # Remove position
        del state.positions[symbol]

        logger.debug(f"Exited position: {symbol} at {exit_price}, PnL: {net_pnl:.2f} ({exit_reason})")

    async def _close_all_positions(self, state: BacktestState, end_date: datetime):
        """Close all remaining positions at the end of backtest"""
        symbols_to_close = list(state.positions.keys())

        for symbol in symbols_to_close:
            position = state.positions[symbol]
            # Use last known price (would need to fetch from data)
            await self._exit_position(state, symbol, end_date, position.entry_price, "backtest_end")

    def _calculate_performance_metrics(
        self,
        state: BacktestState,
        initial_capital: float
    ) -> BacktestPerformanceMetrics:
        """Calculate comprehensive performance metrics"""

        trades = state.trade_history
        if not trades:
            return self._empty_performance_metrics(initial_capital)

        # Basic metrics
        total_trades = len(trades)
        winning_trades = len([t for t in trades if t['is_winning_trade']])
        losing_trades = total_trades - winning_trades

        total_pnl = sum(t['net_pnl'] for t in trades)
        final_capital = initial_capital + total_pnl
        total_return_percentage = (total_pnl / initial_capital) * 100

        # Trade statistics
        win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0

        winning_pnls = [t['net_pnl'] for t in trades if t['is_winning_trade']]
        losing_pnls = [t['net_pnl'] for t in trades if not t['is_winning_trade']]

        avg_win = np.mean(winning_pnls) if winning_pnls else 0
        avg_loss = np.mean(losing_pnls) if losing_pnls else 0

        gross_profit = sum(winning_pnls) if winning_pnls else 0
        gross_loss = abs(sum(losing_pnls)) if losing_pnls else 0
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')

        # Risk metrics
        equity_values = [point['equity'] for point in state.equity_curve]
        returns = np.diff(equity_values) / equity_values[:-1] if len(equity_values) > 1 else [0]

        max_drawdown = max([dp['max_drawdown'] for dp in state.drawdown_periods]) if state.drawdown_periods else 0
        max_drawdown_percentage = max_drawdown * 100

        volatility = np.std(returns) * np.sqrt(252) if len(returns) > 1 else 0  # Annualized

        # Sharpe ratio (assuming 0% risk-free rate)
        mean_return = np.mean(returns) if returns else 0
        sharpe_ratio = (mean_return / volatility) * np.sqrt(252) if volatility > 0 else 0

        # Sortino ratio (downside deviation)
        downside_returns = [r for r in returns if r < 0]
        downside_deviation = np.std(downside_returns) if downside_returns else 0
        sortino_ratio = (mean_return / downside_deviation) * np.sqrt(252) if downside_deviation > 0 else 0

        # Calmar ratio
        calmar_ratio = (total_return_percentage / 100) / max_drawdown if max_drawdown > 0 else 0

        # Additional metrics
        best_trade = max([t['net_pnl'] for t in trades]) if trades else 0
        worst_trade = min([t['net_pnl'] for t in trades]) if trades else 0
        avg_trade_duration = np.mean([t['duration_hours'] for t in trades]) if trades else 0

        return BacktestPerformanceMetrics(
            total_return=total_pnl,
            total_return_percentage=total_return_percentage,
            final_capital=final_capital,
            initial_capital=initial_capital,
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            win_rate=win_rate,
            max_drawdown=max_drawdown * initial_capital,
            max_drawdown_percentage=max_drawdown_percentage,
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=sortino_ratio,
            calmar_ratio=calmar_ratio,
            volatility=volatility,
            avg_win=avg_win,
            avg_loss=avg_loss,
            profit_factor=profit_factor,
            avg_trade_duration=avg_trade_duration,
            best_trade=best_trade,
            worst_trade=worst_trade,
            consecutive_wins=self._calculate_consecutive_wins(trades),
            consecutive_losses=self._calculate_consecutive_losses(trades),
            recovery_factor=total_return_percentage / max_drawdown_percentage if max_drawdown_percentage > 0 else 0,
            monthly_returns=self._calculate_monthly_returns(state.equity_curve),
            drawdown_periods=[],  # Simplified for now
            equity_curve=state.equity_curve
        )

    def _empty_performance_metrics(self, initial_capital: float) -> BacktestPerformanceMetrics:
        """Return empty performance metrics for failed backtests"""
        return BacktestPerformanceMetrics(
            total_return=0, total_return_percentage=0, final_capital=initial_capital,
            initial_capital=initial_capital, total_trades=0, winning_trades=0,
            losing_trades=0, win_rate=0, max_drawdown=0, max_drawdown_percentage=0,
            sharpe_ratio=0, sortino_ratio=0, calmar_ratio=0, volatility=0,
            avg_win=0, avg_loss=0, profit_factor=0, avg_trade_duration=0,
            best_trade=0, worst_trade=0, consecutive_wins=0, consecutive_losses=0,
            recovery_factor=0, monthly_returns=[], drawdown_periods=[], equity_curve=[]
        )

    def _calculate_consecutive_wins(self, trades: List[Dict]) -> int:
        """Calculate maximum consecutive winning trades"""
        max_consecutive = 0
        current_consecutive = 0

        for trade in trades:
            if trade['is_winning_trade']:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0

        return max_consecutive

    def _calculate_consecutive_losses(self, trades: List[Dict]) -> int:
        """Calculate maximum consecutive losing trades"""
        max_consecutive = 0
        current_consecutive = 0

        for trade in trades:
            if not trade['is_winning_trade']:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0

        return max_consecutive

    def _calculate_monthly_returns(self, equity_curve: List[Dict]) -> List[float]:
        """Calculate monthly returns from equity curve"""
        if not equity_curve:
            return []

        # Group by month and calculate returns
        monthly_returns = []
        # Simplified implementation - would need proper monthly grouping
        return monthly_returns

    def _update_backtest_results(
        self,
        backtest_run: BacktestRun,
        metrics: BacktestPerformanceMetrics,
        state: BacktestState,
        db: Session
    ):
        """Update backtest run with final results"""
        backtest_run.total_trades = metrics.total_trades
        backtest_run.winning_trades = metrics.winning_trades
        backtest_run.losing_trades = metrics.losing_trades
        backtest_run.final_capital = metrics.final_capital
        backtest_run.total_return = metrics.total_return
        backtest_run.total_return_percentage = metrics.total_return_percentage
        backtest_run.max_drawdown = metrics.max_drawdown
        backtest_run.max_drawdown_percentage = metrics.max_drawdown_percentage
        backtest_run.sharpe_ratio = metrics.sharpe_ratio
        backtest_run.sortino_ratio = metrics.sortino_ratio
        backtest_run.calmar_ratio = metrics.calmar_ratio
        backtest_run.volatility = metrics.volatility
        backtest_run.win_rate = metrics.win_rate
        backtest_run.avg_win = metrics.avg_win
        backtest_run.avg_loss = metrics.avg_loss
        backtest_run.profit_factor = metrics.profit_factor
        backtest_run.avg_trade_duration = metrics.avg_trade_duration

        # Store detailed results as JSON
        backtest_run.detailed_results = {
            'equity_curve': state.equity_curve,
            'drawdown_periods': state.drawdown_periods,
            'trade_summary': {
                'best_trade': metrics.best_trade,
                'worst_trade': metrics.worst_trade,
                'consecutive_wins': metrics.consecutive_wins,
                'consecutive_losses': metrics.consecutive_losses
            }
        }

    async def _save_backtest_trades(
        self,
        backtest_run_id: int,
        trade_history: List[Dict],
        db: Session
    ):
        """Save individual trades to database"""
        for trade_data in trade_history:
            backtest_trade = BacktestTrade(
                backtest_run_id=backtest_run_id,
                symbol=trade_data['symbol'],
                timeframe="1h",  # Default timeframe
                trade_type="buy",  # Simplified
                entry_date=trade_data['entry_date'],
                entry_price=trade_data['entry_price'],
                entry_signal_strength=trade_data['entry_signal_strength'],
                entry_confidence=trade_data['entry_confidence'],
                entry_reasoning=trade_data['entry_reasoning'],
                exit_date=trade_data['exit_date'],
                exit_price=trade_data['exit_price'],
                exit_reason=trade_data['exit_reason'],
                quantity=trade_data['quantity'],
                pnl=trade_data['pnl'],
                pnl_percentage=trade_data['pnl_percentage'],
                fees=trade_data['fees'],
                net_pnl=trade_data['net_pnl'],
                duration_hours=trade_data['duration_hours'],
                max_favorable_excursion=trade_data['max_favorable_excursion'],
                max_adverse_excursion=trade_data['max_adverse_excursion'],
                is_winning_trade=trade_data['is_winning_trade']
            )

            db.add(backtest_trade)

        db.commit()


# Create global instance
backtesting_engine = BacktestingEngine()

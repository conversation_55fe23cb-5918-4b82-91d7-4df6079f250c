# Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Database
sqlalchemy==2.0.23
alembic==1.13.1
psycopg2-binary==2.9.9
redis==5.0.1

# Crypto Trading
ccxt==4.1.64
python-binance==1.0.19

# Technical Analysis
# TA-Lib==0.4.28  # Not used - using pandas-based implementations
pandas==2.1.4
numpy==1.26.4
scipy==1.11.4

# AI & ML
openai==1.54.4
requests==2.31.0
aiohttp==3.9.1
feedparser==6.0.10
# litellm==1.17.9  # Not used currently

# Telegram Bot
python-telegram-bot==22.2

# Utilities
python-dotenv==1.0.0
python-multipart==0.0.6
httpx>=0.27.0,<0.29.0
aiofiles==23.2.1
# celery==5.3.4  # Not used currently
email-validator==2.1.0

# Security (Not used currently - Telegram bot handles auth)
# python-jose[cryptography]==3.3.0
# passlib[bcrypt]==1.7.4
cryptography>=41.0.0

# Development (Optional - for testing and code quality)
# pytest==7.4.3
# pytest-asyncio==0.21.1
# black==23.11.0
# flake8==6.1.0
# mypy==1.7.1

# Scheduling (Not used currently)
# APScheduler==3.10.4

# System Monitoring
psutil==5.9.6

# Cloud Storage (Not used currently)
# boto3==1.34.0
# botocore==1.34.0

# Monitoring (Not used currently)
# prometheus-client==0.19.0
# structlog==23.2.0

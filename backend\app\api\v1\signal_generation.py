"""
Signal Generation API Endpoints
Author: inkbytefo
"""

from fastapi import APIRouter, HTTPException, Query, BackgroundTasks
from typing import List, Dict, Optional, Any
import logging
from datetime import datetime

from app.services.signal_generation_service import signal_generation_service
from app.core.config import settings

router = APIRouter()
logger = logging.getLogger("signal_generation_api")


@router.get("/generate/{symbol}")
async def generate_signal(
    symbol: str,
    timeframe: str = Query("1h", description="Timeframe (1m, 5m, 15m, 1h, 4h, 1d)"),
    exchange: str = Query("binance", description="Exchange name"),
    portfolio_size: float = Query(10000.0, description="Portfolio size in USD for position sizing")
):
    """Generate comprehensive trading signal for a symbol"""
    try:
        logger.info(f"Signal generation requested for {symbol} ({timeframe})")
        
        # Validate symbol format
        if '/' not in symbol:
            symbol = f"{symbol}/USDT"
        
        # Generate trading signal
        signal = await signal_generation_service.generate_signal(
            symbol=symbol,
            timeframe=timeframe,
            exchange=exchange,
            portfolio_size=portfolio_size
        )
        
        if not signal:
            raise HTTPException(
                status_code=404,
                detail=f"Unable to generate signal for {symbol}"
            )
        
        # Format response
        response = {
            "symbol": signal.symbol,
            "timeframe": signal.timeframe,
            "exchange": signal.exchange,
            "signal": {
                "type": signal.signal_type.value,
                "strength": signal.signal_strength.value,
                "overall_score": round(signal.overall_score, 3),
                "confidence": round(signal.confidence, 3),
                "reasoning": signal.reasoning
            },
            "entry_point": {
                "price": signal.entry_point.price,
                "confidence": round(signal.entry_point.confidence, 3),
                "reasoning": signal.entry_point.reasoning,
                "stop_loss": signal.entry_point.stop_loss,
                "take_profit": signal.entry_point.take_profit,
                "risk_reward_ratio": signal.entry_point.risk_reward_ratio
            },
            "exit_points": [
                {
                    "price": exit_point.price,
                    "confidence": round(exit_point.confidence, 3),
                    "reasoning": exit_point.reasoning,
                    "risk_reward_ratio": exit_point.risk_reward_ratio
                }
                for exit_point in signal.exit_points
            ],
            "position_sizing": {
                "position_size_percentage": round(signal.position_sizing.position_size_percentage, 2),
                "position_size_usd": round(signal.position_sizing.position_size_usd, 2) if signal.position_sizing.position_size_usd else None,
                "max_loss_percentage": round(signal.position_sizing.max_loss_percentage, 2),
                "risk_level": signal.position_sizing.risk_level.value,
                "leverage": signal.position_sizing.leverage,
                "reasoning": signal.position_sizing.reasoning
            },
            "signal_factors": {
                "technical": [
                    {
                        "name": factor.name,
                        "value": round(factor.value, 3),
                        "weight": round(factor.weight, 3),
                        "confidence": round(factor.confidence, 3),
                        "description": factor.description
                    }
                    for factor in signal.technical_factors
                ],
                "pattern": [
                    {
                        "name": factor.name,
                        "value": round(factor.value, 3),
                        "weight": round(factor.weight, 3),
                        "confidence": round(factor.confidence, 3),
                        "description": factor.description
                    }
                    for factor in signal.pattern_factors
                ],
                "market": [
                    {
                        "name": factor.name,
                        "value": round(factor.value, 3),
                        "weight": round(factor.weight, 3),
                        "confidence": round(factor.confidence, 3),
                        "description": factor.description
                    }
                    for factor in signal.market_factors
                ]
            },
            "risk_metrics": {
                "max_drawdown_risk": round(signal.max_drawdown_risk, 3),
                "volatility_risk": round(signal.volatility_risk, 3),
                "market_correlation_risk": round(signal.market_correlation_risk, 3)
            },
            "validity": {
                "generated_at": signal.generated_at.isoformat(),
                "valid_until": signal.valid_until.isoformat(),
                "is_valid": signal.valid_until > datetime.now()
            },
            "status": "success"
        }
        
        return response
        
    except Exception as e:
        logger.error(f"Error generating signal for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/multi-symbol")
async def generate_multi_symbol_signals(
    symbols: str = Query(..., description="Comma-separated list of symbols"),
    timeframe: str = Query("1h", description="Timeframe"),
    exchange: str = Query("binance", description="Exchange name"),
    portfolio_size: float = Query(10000.0, description="Portfolio size in USD")
):
    """Generate signals for multiple symbols"""
    try:
        # Parse symbols
        symbol_list = [s.strip() for s in symbols.split(",")]
        
        # Validate and format symbols
        formatted_symbols = []
        for symbol in symbol_list:
            if '/' not in symbol:
                symbol = f"{symbol}/USDT"
            formatted_symbols.append(symbol)
        
        logger.info(f"Multi-symbol signal generation for {len(formatted_symbols)} symbols")
        
        # Generate signals
        signals = await signal_generation_service.generate_multi_symbol_signals(
            symbols=formatted_symbols,
            timeframe=timeframe,
            exchange=exchange,
            portfolio_size=portfolio_size
        )
        
        # Format response
        formatted_signals = {}
        successful_signals = 0
        
        for symbol, signal in signals.items():
            if signal:
                formatted_signals[symbol] = {
                    "signal_type": signal.signal_type.value,
                    "signal_strength": signal.signal_strength.value,
                    "overall_score": round(signal.overall_score, 3),
                    "confidence": round(signal.confidence, 3),
                    "entry_price": signal.entry_point.price,
                    "stop_loss": signal.entry_point.stop_loss,
                    "take_profit": signal.entry_point.take_profit,
                    "position_size_percentage": round(signal.position_sizing.position_size_percentage, 2),
                    "risk_level": signal.position_sizing.risk_level.value,
                    "reasoning": signal.reasoning[:100] + "..." if len(signal.reasoning) > 100 else signal.reasoning,
                    "generated_at": signal.generated_at.isoformat(),
                    "valid_until": signal.valid_until.isoformat()
                }
                successful_signals += 1
            else:
                formatted_signals[symbol] = {
                    "error": "Failed to generate signal"
                }
        
        return {
            "signals": formatted_signals,
            "summary": {
                "total_symbols": len(formatted_symbols),
                "successful_signals": successful_signals,
                "failed_signals": len(formatted_symbols) - successful_signals,
                "timeframe": timeframe,
                "exchange": exchange,
                "portfolio_size": portfolio_size
            },
            "status": "success"
        }
        
    except Exception as e:
        logger.error(f"Error in multi-symbol signal generation: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/market-summary")
async def get_market_signal_summary(
    symbols: str = Query(None, description="Comma-separated symbols (optional, uses defaults if not provided)"),
    timeframe: str = Query("1h", description="Timeframe"),
    exchange: str = Query("binance", description="Exchange name")
):
    """Get market-wide signal summary"""
    try:
        # Parse symbols or use defaults
        if symbols:
            symbol_list = [s.strip() for s in symbols.split(",")]
            # Format symbols
            formatted_symbols = []
            for symbol in symbol_list:
                if '/' not in symbol:
                    symbol = f"{symbol}/USDT"
                formatted_symbols.append(symbol)
        else:
            formatted_symbols = None  # Use defaults
        
        logger.info(f"Market signal summary requested")
        
        # Get signal summary
        summary = await signal_generation_service.get_signal_summary(
            symbols=formatted_symbols,
            timeframe=timeframe,
            exchange=exchange
        )
        
        if "error" in summary:
            raise HTTPException(status_code=500, detail=summary["error"])
        
        return {
            "market_analysis": {
                "sentiment": summary["market_sentiment"],
                "confidence": summary["average_confidence"],
                "timeframe": timeframe,
                "exchange": exchange
            },
            "signal_distribution": summary["signal_distribution"],
            "statistics": {
                "total_symbols": summary["total_symbols_analyzed"],
                "valid_signals": summary["valid_signals"],
                "success_rate": round((summary["valid_signals"] / summary["total_symbols_analyzed"]) * 100, 1) if summary["total_symbols_analyzed"] > 0 else 0
            },
            "analysis_timestamp": summary["analysis_timestamp"],
            "status": "success"
        }
        
    except Exception as e:
        logger.error(f"Error getting market signal summary: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/top-signals")
async def get_top_signals(
    limit: int = Query(10, description="Number of top signals to return"),
    min_confidence: float = Query(0.7, description="Minimum confidence threshold"),
    timeframe: str = Query("1h", description="Timeframe"),
    exchange: str = Query("binance", description="Exchange name"),
    signal_types: str = Query("buy,strong_buy", description="Comma-separated signal types to include")
):
    """Get top trading signals based on confidence and strength"""
    try:
        # Parse signal types
        allowed_types = [s.strip().lower() for s in signal_types.split(",")]
        
        # Use default symbols for screening
        symbols = settings.DEFAULT_SYMBOLS
        
        logger.info(f"Top signals screening for {len(symbols)} symbols")
        
        # Generate signals for all symbols
        all_signals = await signal_generation_service.generate_multi_symbol_signals(
            symbols=symbols,
            timeframe=timeframe,
            exchange=exchange
        )
        
        # Filter and rank signals
        qualified_signals = []
        
        for symbol, signal in all_signals.items():
            if signal and signal.confidence >= min_confidence:
                if signal.signal_type.value in allowed_types:
                    # Calculate ranking score
                    score = (
                        signal.confidence * 0.4 +
                        (abs(signal.overall_score) * 0.3) +
                        (1.0 if signal.signal_strength.value == "very_strong" else
                         0.8 if signal.signal_strength.value == "strong" else
                         0.6 if signal.signal_strength.value == "moderate" else 0.4) * 0.3
                    )
                    
                    qualified_signals.append({
                        "symbol": symbol,
                        "signal": signal,
                        "ranking_score": score
                    })
        
        # Sort by ranking score
        qualified_signals.sort(key=lambda x: x["ranking_score"], reverse=True)
        
        # Format top signals
        top_signals = []
        for item in qualified_signals[:limit]:
            signal = item["signal"]
            
            # Calculate potential profit
            entry_price = signal.entry_point.price
            take_profit = signal.entry_point.take_profit
            
            if take_profit and entry_price:
                if signal.signal_type.value in ["buy", "strong_buy"]:
                    potential_profit_pct = ((take_profit - entry_price) / entry_price) * 100
                else:
                    potential_profit_pct = ((entry_price - take_profit) / entry_price) * 100
            else:
                potential_profit_pct = 0
            
            top_signals.append({
                "rank": len(top_signals) + 1,
                "symbol": item["symbol"],
                "signal_type": signal.signal_type.value,
                "signal_strength": signal.signal_strength.value,
                "confidence": round(signal.confidence, 3),
                "overall_score": round(signal.overall_score, 3),
                "ranking_score": round(item["ranking_score"], 3),
                "entry_price": entry_price,
                "stop_loss": signal.entry_point.stop_loss,
                "take_profit": take_profit,
                "potential_profit_percentage": round(potential_profit_pct, 2),
                "position_size_percentage": round(signal.position_sizing.position_size_percentage, 2),
                "risk_level": signal.position_sizing.risk_level.value,
                "risk_reward_ratio": signal.entry_point.risk_reward_ratio,
                "reasoning": signal.reasoning[:150] + "..." if len(signal.reasoning) > 150 else signal.reasoning,
                "generated_at": signal.generated_at.isoformat(),
                "valid_until": signal.valid_until.isoformat()
            })
        
        return {
            "top_signals": top_signals,
            "screening_summary": {
                "total_symbols_screened": len(symbols),
                "qualified_signals": len(qualified_signals),
                "min_confidence_threshold": min_confidence,
                "signal_types_included": allowed_types,
                "top_signals_returned": len(top_signals)
            },
            "timeframe": timeframe,
            "exchange": exchange,
            "analysis_timestamp": datetime.now().isoformat(),
            "status": "success"
        }
        
    except Exception as e:
        logger.error(f"Error getting top signals: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/signal-factors/{symbol}")
async def get_signal_factors(
    symbol: str,
    timeframe: str = Query("1h", description="Timeframe"),
    exchange: str = Query("binance", description="Exchange name")
):
    """Get detailed signal factors breakdown for a symbol"""
    try:
        # Validate symbol format
        if '/' not in symbol:
            symbol = f"{symbol}/USDT"
        
        logger.info(f"Signal factors analysis for {symbol}")
        
        # Generate signal
        signal = await signal_generation_service.generate_signal(
            symbol=symbol,
            timeframe=timeframe,
            exchange=exchange
        )
        
        if not signal:
            raise HTTPException(
                status_code=404,
                detail=f"Unable to analyze signal factors for {symbol}"
            )
        
        # Calculate factor contributions
        tech_contribution = sum(f.value * f.weight for f in signal.technical_factors)
        pattern_contribution = sum(f.value * f.weight for f in signal.pattern_factors)
        market_contribution = sum(f.value * f.weight for f in signal.market_factors)
        ai_contribution = sum(f.value * f.weight for f in signal.ai_factors)
        
        # Identify strongest factors
        all_factors = signal.technical_factors + signal.pattern_factors + signal.market_factors + signal.ai_factors
        strongest_factors = sorted(all_factors, key=lambda f: abs(f.value * f.weight), reverse=True)[:5]
        
        return {
            "symbol": symbol,
            "timeframe": timeframe,
            "overall_signal": {
                "type": signal.signal_type.value,
                "strength": signal.signal_strength.value,
                "score": round(signal.overall_score, 3),
                "confidence": round(signal.confidence, 3)
            },
            "factor_contributions": {
                "technical": {
                    "contribution": round(tech_contribution, 3),
                    "weight": signal_generation_service.signal_weights["technical"],
                    "factors": [
                        {
                            "name": f.name,
                            "value": round(f.value, 3),
                            "weight": round(f.weight, 3),
                            "contribution": round(f.value * f.weight, 3),
                            "confidence": round(f.confidence, 3),
                            "description": f.description
                        }
                        for f in signal.technical_factors
                    ]
                },
                "pattern": {
                    "contribution": round(pattern_contribution, 3),
                    "weight": signal_generation_service.signal_weights["pattern"],
                    "factors": [
                        {
                            "name": f.name,
                            "value": round(f.value, 3),
                            "weight": round(f.weight, 3),
                            "contribution": round(f.value * f.weight, 3),
                            "confidence": round(f.confidence, 3),
                            "description": f.description
                        }
                        for f in signal.pattern_factors
                    ]
                },
                "market": {
                    "contribution": round(market_contribution, 3),
                    "weight": signal_generation_service.signal_weights["market"],
                    "factors": [
                        {
                            "name": f.name,
                            "value": round(f.value, 3),
                            "weight": round(f.weight, 3),
                            "contribution": round(f.value * f.weight, 3),
                            "confidence": round(f.confidence, 3),
                            "description": f.description
                        }
                        for f in signal.market_factors
                    ]
                },
                "ai": {
                    "contribution": round(ai_contribution, 3),
                    "weight": signal_generation_service.signal_weights["ai"],
                    "factors": [
                        {
                            "name": f.name,
                            "value": round(f.value, 3),
                            "weight": round(f.weight, 3),
                            "contribution": round(f.value * f.weight, 3),
                            "confidence": round(f.confidence, 3),
                            "description": f.description
                        }
                        for f in signal.ai_factors
                    ]
                }
            },
            "strongest_factors": [
                {
                    "name": f.name,
                    "value": round(f.value, 3),
                    "weight": round(f.weight, 3),
                    "contribution": round(f.value * f.weight, 3),
                    "description": f.description
                }
                for f in strongest_factors
            ],
            "analysis_timestamp": signal.generated_at.isoformat(),
            "status": "success"
        }
        
    except Exception as e:
        logger.error(f"Error getting signal factors for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/position-sizing/{symbol}")
async def get_position_sizing_recommendation(
    symbol: str,
    portfolio_size: float = Query(..., description="Portfolio size in USD"),
    risk_tolerance: str = Query("medium", description="Risk tolerance: low, medium, high"),
    timeframe: str = Query("1h", description="Timeframe"),
    exchange: str = Query("binance", description="Exchange name")
):
    """Get position sizing recommendation for a specific symbol"""
    try:
        # Validate symbol format
        if '/' not in symbol:
            symbol = f"{symbol}/USDT"
        
        logger.info(f"Position sizing recommendation for {symbol}")
        
        # Generate signal
        signal = await signal_generation_service.generate_signal(
            symbol=symbol,
            timeframe=timeframe,
            exchange=exchange,
            portfolio_size=portfolio_size
        )
        
        if not signal:
            raise HTTPException(
                status_code=404,
                detail=f"Unable to generate position sizing for {symbol}"
            )
        
        # Adjust for risk tolerance
        base_position_size = signal.position_sizing.position_size_percentage
        
        risk_multipliers = {
            "low": 0.7,
            "medium": 1.0,
            "high": 1.3
        }
        
        risk_multiplier = risk_multipliers.get(risk_tolerance.lower(), 1.0)
        adjusted_position_size = base_position_size * risk_multiplier
        
        # Calculate position details
        position_size_usd = portfolio_size * (adjusted_position_size / 100)
        entry_price = signal.entry_point.price
        stop_loss = signal.entry_point.stop_loss
        
        # Calculate shares/units
        shares = position_size_usd / entry_price if entry_price > 0 else 0
        
        # Calculate risk amounts
        if stop_loss and entry_price:
            risk_per_share = abs(entry_price - stop_loss)
            total_risk_usd = shares * risk_per_share
            risk_percentage = (total_risk_usd / portfolio_size) * 100
        else:
            total_risk_usd = position_size_usd * 0.02  # Default 2% risk
            risk_percentage = 2.0
        
        return {
            "symbol": symbol,
            "portfolio_size": portfolio_size,
            "risk_tolerance": risk_tolerance,
            "signal_assessment": {
                "signal_type": signal.signal_type.value,
                "signal_strength": signal.signal_strength.value,
                "confidence": round(signal.confidence, 3)
            },
            "position_sizing": {
                "recommended_position_size_percentage": round(adjusted_position_size, 2),
                "recommended_position_size_usd": round(position_size_usd, 2),
                "shares_or_units": round(shares, 6),
                "entry_price": entry_price,
                "stop_loss": stop_loss,
                "take_profit": signal.entry_point.take_profit
            },
            "risk_analysis": {
                "total_risk_usd": round(total_risk_usd, 2),
                "risk_percentage_of_portfolio": round(risk_percentage, 2),
                "risk_per_share": round(risk_per_share, 6) if 'risk_per_share' in locals() else None,
                "risk_reward_ratio": signal.entry_point.risk_reward_ratio,
                "max_loss_percentage": round(signal.position_sizing.max_loss_percentage, 2)
            },
            "recommendations": {
                "leverage": signal.position_sizing.leverage,
                "risk_level": signal.position_sizing.risk_level.value,
                "reasoning": signal.position_sizing.reasoning
            },
            "analysis_timestamp": signal.generated_at.isoformat(),
            "status": "success"
        }
        
    except Exception as e:
        logger.error(f"Error getting position sizing for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/combined-analysis/{symbol}")
async def get_combined_analysis(
    symbol: str,
    timeframe: str = Query("1h", description="Timeframe (1m, 5m, 15m, 1h, 4h, 1d)"),
    exchange: str = Query("binance", description="Exchange name"),
    include_ai: bool = Query(True, description="Include AI analysis"),
    portfolio_size: float = Query(10000.0, description="Portfolio size for position sizing")
):
    """Get comprehensive combined analysis including technical, pattern, and AI analysis"""
    try:
        logger.info(f"Combined analysis requested for {symbol} ({timeframe})")

        # Validate symbol format
        if '/' not in symbol:
            symbol = f"{symbol}/USDT"

        # Get enhanced technical analysis (includes patterns and AI)
        from app.services.technical_analysis_service import technical_analysis_service
        enhanced_analysis = await technical_analysis_service.get_enhanced_analysis(
            symbol=symbol,
            timeframe=timeframe,
            exchange=exchange,
            include_ai=include_ai
        )

        if "error" in enhanced_analysis:
            return {"error": enhanced_analysis["error"]}

        # Get comprehensive trading signal (includes AI factors)
        trading_signal = await signal_generation_service.generate_signal(
            symbol=symbol,
            timeframe=timeframe,
            exchange=exchange,
            portfolio_size=portfolio_size
        )

        if not trading_signal:
            return {"error": "Failed to generate trading signal"}

        # Combine all analysis
        combined_result = {
            "symbol": symbol,
            "timeframe": timeframe,
            "exchange": exchange,
            "analysis_timestamp": datetime.now().isoformat(),

            # Enhanced technical analysis
            "enhanced_analysis": enhanced_analysis,

            # Trading signal with AI factors
            "trading_signal": {
                "signal_type": trading_signal.signal_type.value,
                "signal_strength": trading_signal.signal_strength.value,
                "overall_score": round(trading_signal.overall_score, 3),
                "confidence": round(trading_signal.confidence, 3),
                "reasoning": trading_signal.reasoning,

                # Factor analysis
                "factor_analysis": {
                    "technical": {
                        "contribution": sum(f.value * f.weight for f in trading_signal.technical_factors),
                        "weight": signal_generation_service.signal_weights["technical"],
                        "factor_count": len(trading_signal.technical_factors)
                    },
                    "pattern": {
                        "contribution": sum(f.value * f.weight for f in trading_signal.pattern_factors),
                        "weight": signal_generation_service.signal_weights["pattern"],
                        "factor_count": len(trading_signal.pattern_factors)
                    },
                    "market": {
                        "contribution": sum(f.value * f.weight for f in trading_signal.market_factors),
                        "weight": signal_generation_service.signal_weights["market"],
                        "factor_count": len(trading_signal.market_factors)
                    },
                    "ai": {
                        "contribution": sum(f.value * f.weight for f in trading_signal.ai_factors),
                        "weight": signal_generation_service.signal_weights["ai"],
                        "factor_count": len(trading_signal.ai_factors)
                    }
                }
            },

            # Summary and recommendations
            "summary": {
                "overall_recommendation": trading_signal.signal_type.value,
                "confidence_level": "high" if trading_signal.confidence > 0.7 else "medium" if trading_signal.confidence > 0.4 else "low",
                "risk_level": "high" if trading_signal.volatility_risk > 0.7 else "medium" if trading_signal.volatility_risk > 0.4 else "low",
                "key_factors": [
                    f.description for f in sorted(
                        trading_signal.technical_factors + trading_signal.pattern_factors +
                        trading_signal.market_factors + trading_signal.ai_factors,
                        key=lambda x: abs(x.value * x.weight),
                        reverse=True
                    )[:3]
                ]
            }
        }

        logger.info(f"Combined analysis completed for {symbol}")
        return combined_result

    except Exception as e:
        logger.error(f"Error in combined analysis for {symbol}: {e}")
        return {"error": "Failed to generate combined analysis", "details": str(e)}
